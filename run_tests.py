#!/usr/bin/env python3
"""
测试运行脚本

提供便捷的测试运行命令。
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败")
        if e.stdout:
            print("标准输出:")
            print(e.stdout)
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="钉钉MCP服务器测试运行器")
    parser.add_argument("--test", "-t", help="运行特定测试文件")
    parser.add_argument("--coverage", "-c", action="store_true", help="运行覆盖率测试")
    parser.add_argument("--lint", "-l", action="store_true", help="运行代码检查")
    parser.add_argument("--format", "-f", action="store_true", help="格式化代码")
    parser.add_argument("--all", "-a", action="store_true", help="运行所有检查")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 确保在项目根目录
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    success = True
    
    if args.all or args.format:
        # 代码格式化
        if not run_command(["black", "src/", "tests/"], "代码格式化"):
            success = False
    
    if args.all or args.lint:
        # 代码风格检查
        if not run_command(["ruff", "check", "src/", "tests/"], "代码风格检查"):
            success = False
        
        # 类型检查
        if not run_command(["mypy", "src/"], "类型检查"):
            success = False
    
    if args.test:
        # 运行特定测试
        cmd = ["pytest", f"tests/{args.test}"]
        if args.verbose:
            cmd.append("-v")
        if not run_command(cmd, f"运行测试 {args.test}"):
            success = False
    elif args.coverage:
        # 运行覆盖率测试
        cmd = ["pytest", "--cov=src", "--cov-report=html", "--cov-report=term"]
        if args.verbose:
            cmd.append("-v")
        if not run_command(cmd, "覆盖率测试"):
            success = False
        print("\n📊 覆盖率报告已生成到 htmlcov/ 目录")
    else:
        # 运行所有测试
        cmd = ["pytest", "tests/"]
        if args.verbose:
            cmd.append("-v")
        if not run_command(cmd, "运行所有测试"):
            success = False
    
    if success:
        print("\n🎉 所有检查都通过了！")
        return 0
    else:
        print("\n💥 有检查失败，请查看上面的错误信息")
        return 1


if __name__ == "__main__":
    sys.exit(main())

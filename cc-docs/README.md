# MCP Server 开发流程提示词系统

这是一套完整的 Model Context Protocol (MCP) Server 开发指导系统，旨在帮助 AI 助手快速构建功能完整的 MCP Server 应用。

## 系统概览

### 核心组件
- **工具 (Tools)**: 执行特定操作的函数接口
- **提示词 (Prompts)**: 预定义的模板和指令
- **资源 (Resources)**: 数据和内容的访问接口

### 支持语言
- Python (基于 `mcp` SDK)
- TypeScript (基于 `@modelcontextprotocol/sdk`)

## 目录结构

```
cc-docs/
├── README.md                    # 本文件
├── workflows/                   # 开发流程
│   ├── 01-project-setup.md     # 项目初始化流程
│   ├── 02-python-workflow.md   # Python 开发流程
│   ├── 03-typescript-workflow.md # TypeScript 开发流程
│   ├── 04-testing-deployment.md # 测试部署流程
│   └── 05-configuration-debug.md # 配置调试流程
├── templates/                   # 代码模板
│   ├── python/                 # Python 模板
│   ├── typescript/             # TypeScript 模板
│   ├── tools/                  # 工具开发模板
│   ├── prompts/                # 提示词模板
│   └── resources/              # 资源模板
└── guides/                     # 专项指南
    ├── best-practices.md       # 最佳实践
    ├── error-handling.md       # 错误处理
    ├── security-guidelines.md  # 安全指南
    └── documentation-gen.md    # 文档生成
```

## 快速开始

### 步骤 1: 项目规划
参考 `workflows/01-project-setup.md` 完成：
- [ ] 确定 MCP Server 功能需求
- [ ] 选择开发语言 (Python/TypeScript)
- [ ] 设计 API 接口结构

### 步骤 2: 开发实现
根据选择的语言：
- Python: 遵循 `workflows/02-python-workflow.md`
- TypeScript: 遵循 `workflows/03-typescript-workflow.md`

### 步骤 3: 测试部署
按照 `workflows/04-testing-deployment.md` 进行：
- [ ] 单元测试
- [ ] 集成测试
- [ ] 配置部署

## 使用方法

1. **AI 助手使用**: 根据用户需求，选择相应的工作流程文档，按步骤执行
2. **模板复用**: 从 `templates/` 目录选择合适的代码模板进行定制
3. **问题解决**: 参考 `guides/` 目录中的专项指南

## 质量标准

每个开发步骤都包含：
- ✅ **输入要求**: 明确的前置条件
- 🎯 **执行步骤**: 具体的操作指令
- ✅ **验证标准**: 完成度检查点
- 📋 **输出结果**: 预期的交付物

---

*本系统基于 MCP 官方文档标准设计，确保生成的 Server 符合协议规范。*
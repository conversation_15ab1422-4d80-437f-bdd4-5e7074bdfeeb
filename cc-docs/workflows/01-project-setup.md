# MCP Server 项目初始化流程

## 任务概述
指导AI助手完成MCP Server项目的需求分析、技术选型和项目结构设计。

---

## 第1步：需求分析与功能设计

### 输入要求
- 用户提供的功能需求描述
- 目标服务/API的相关文档或接口信息

### 执行步骤

#### 1.1 功能需求整理
```
基于用户描述，整理出：
- MCP Server的核心功能列表
- 需要实现的工具(Tools)清单
- 需要提供的提示词(Prompts)清单  
- 需要暴露的资源(Resources)清单
```

#### 1.2 API能力分析
```
分析目标服务的API文档，提取：
- 认证方式（API Key、OAuth、Token等）
- 主要端点列表
- 请求/响应数据结构
- 错误处理机制
- 访问频率限制
```

#### 1.3 MCP能力映射
```
将API功能映射到MCP三大能力：

Tools映射规则:
- CRUD操作 → MCP Tools
- 数据查询 → MCP Tools  
- 文件操作 → MCP Tools

Prompts映射规则:
- 模板消息 → MCP Prompts
- 格式化输出 → MCP Prompts
- 交互引导 → MCP Prompts

Resources映射规则:
- 数据获取 → MCP Resources
- 文件访问 → MCP Resources
- 流式数据 → MCP Resources
```

### 验证标准
- [ ] 功能清单详细且无遗漏
- [ ] API分析准确完整
- [ ] MCP能力映射合理

### 输出结果
```
项目需求文档包含：
1. 功能概述
2. MCP Tools列表（工具名称、描述、参数）
3. MCP Prompts列表（提示词名称、用途、模板）
4. MCP Resources列表（资源名称、类型、访问方式）
5. 技术依赖清单
```

---

## 第2步：技术选型决策

### 输入要求
- 第1步的需求分析结果
- 用户的技术偏好（如有）

### 执行步骤

#### 2.1 开发语言选择
```
选择标准：
- Python选择条件：
  * 需要复杂数据处理
  * 集成AI/ML库
  * 快速原型开发
  * 用户明确偏好Python

- TypeScript选择条件：
  * 前端集成需求
  * 需要类型安全
  * 团队JavaScript背景
  * 用户明确偏好TypeScript
```

#### 2.2 依赖库评估
```
必需依赖：
- Python: mcp, httpx, pydantic
- TypeScript: @modelcontextprotocol/sdk, axios, zod

可选依赖（根据需求）：
- 数据库：sqlite3, prisma
- 缓存：redis-py, node-redis  
- 认证：python-jose, jsonwebtoken
- 文件处理：pandas, csv-parser
```

#### 2.3 架构模式确定
```
基础架构（必选）：
- MCP Server核心类
- 工具注册管理器
- 错误处理中间件

扩展架构（按需选择）：
- 数据访问层（DAO）
- 缓存管理层
- 配置管理模块
- 日志记录系统
```

### 验证标准
- [ ] 语言选择有明确理由
- [ ] 依赖库版本兼容
- [ ] 架构设计合理

### 输出结果
```
技术选型文档包含：
1. 开发语言及理由
2. 核心依赖库列表
3. 项目架构图
4. 开发环境要求
```

---

## 第3步：项目结构创建

### 输入要求
- 第2步的技术选型结果
- 项目名称

### 执行步骤

#### 3.1 目录结构生成

**Python项目结构：**
```
{project_name}/
├── src/
│   └── {project_name}/
│       ├── __init__.py
│       ├── server.py          # MCP Server主类
│       ├── tools/             # 工具实现
│       │   ├── __init__.py
│       │   └── base.py
│       ├── prompts/           # 提示词实现
│       │   ├── __init__.py
│       │   └── base.py
│       ├── resources/         # 资源实现
│       │   ├── __init__.py
│       │   └── base.py
│       ├── config/            # 配置管理
│       │   ├── __init__.py
│       │   └── settings.py
│       └── utils/             # 工具函数
│           ├── __init__.py
│           └── common.py
├── tests/                     # 测试文件
├── requirements.txt           # 依赖列表
├── pyproject.toml            # 项目配置
├── README.md                 # 项目说明
└── .env.example              # 环境变量模板
```

**TypeScript项目结构：**
```
{project_name}/
├── src/
│   ├── index.ts              # 入口文件
│   ├── server.ts             # MCP Server主类
│   ├── tools/                # 工具实现
│   │   └── index.ts
│   ├── prompts/              # 提示词实现
│   │   └── index.ts
│   ├── resources/            # 资源实现
│   │   └── index.ts
│   ├── config/               # 配置管理
│   │   └── settings.ts
│   └── utils/                # 工具函数
│       └── common.ts
├── tests/                    # 测试文件
├── package.json              # 项目配置
├── tsconfig.json            # TypeScript配置
├── README.md                # 项目说明
└── .env.example             # 环境变量模板
```

#### 3.2 配置文件生成

**Python配置文件：**
```
# requirements.txt
mcp>=1.0.0
httpx>=0.25.0
pydantic>=2.0.0
python-dotenv>=1.0.0

# pyproject.toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "{project_name}"
version = "0.1.0"
description = "MCP Server for {description}"
dependencies = [
    "mcp>=1.0.0",
    "httpx>=0.25.0", 
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0"
]

[project.scripts]
{project_name} = "{project_name}.server:main"
```

**TypeScript配置文件：**
```
// package.json
{
  "name": "{project_name}",
  "version": "0.1.0",
  "description": "MCP Server for {description}",
  "main": "dist/index.js",
  "scripts": {
    "build": "tsc",
    "start": "node dist/index.js",
    "dev": "tsx src/index.ts",
    "test": "jest"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "axios": "^1.6.0",
    "zod": "^3.22.0",
    "dotenv": "^16.3.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "tsx": "^4.0.0",
    "jest": "^29.0.0"
  }
}

// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "CommonJS",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
```

#### 3.3 基础文件生成

```
创建以下基础文件：
- README.md（项目介绍和使用说明）
- .env.example（环境变量模板）
- .gitignore（版本控制忽略规则）
- LICENSE（开源许可证，如需要）
```

### 验证标准
- [ ] 目录结构完整规范
- [ ] 配置文件语法正确
- [ ] 基础文件内容合适

### 输出结果
```
完整的项目骨架包含：
1. 标准化目录结构
2. 正确的配置文件
3. 基础的README文档
4. 环境变量模板
```

---

## 第4步：开发环境验证

### 输入要求
- 完整的项目结构
- 正确的配置文件

### 执行步骤

#### 4.1 依赖安装测试
```
Python项目：
cd {project_name}
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

TypeScript项目：
cd {project_name}
npm install
```

#### 4.2 基础运行测试
```
创建最小化的MCP Server实例：
- 实现基本的initialize处理
- 添加一个简单的ping工具
- 验证服务器可以正常启动
```

#### 4.3 开发工具配置
```
推荐的开发工具配置：
- VS Code扩展（Python/TypeScript）
- 代码格式化工具（black/prettier）
- 静态检查工具（mypy/eslint）
- 调试配置（launch.json）
```

### 验证标准
- [ ] 依赖安装成功
- [ ] 基础服务器可启动
- [ ] 开发环境配置正确

### 输出结果
```
就绪的开发环境包含：
1. 已安装的依赖库
2. 可运行的基础服务器
3. 配置好的开发工具
4. 验证通过的环境检查
```

---

## 完成检查清单

- [ ] **需求分析**：功能清单、API分析、MCP映射完成
- [ ] **技术选型**：语言、依赖、架构确定  
- [ ] **项目结构**：目录、配置、基础文件创建
- [ ] **环境验证**：依赖安装、运行测试、工具配置完成

## 下一步行动

✅ **项目初始化完成后，请根据选择的开发语言继续：**
- Python → `workflows/02-python-workflow.md`
- TypeScript → `workflows/03-typescript-workflow.md`
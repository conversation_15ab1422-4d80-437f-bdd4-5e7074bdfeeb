# MCP Server 集成测试和部署指南

## 任务概述
指导AI助手完成MCP Server的全面测试、性能验证和生产部署。

---

## 第1步：集成测试策略

### 输入要求
- 完整的MCP Server实现
- 所有工具、提示词、资源的单元测试通过
- 测试环境配置

### 执行步骤

#### 1.1 MCP协议集成测试

**Python测试框架**
```python
# tests/integration/test_mcp_protocol.py
import pytest
import asyncio
import json
from mcp import ClientSession, StdioServerTransport
from mcp.client.stdio import stdio_client
from src.{project_name}.server import {ClassName}Server

class TestMCPProtocolIntegration:
    \"\"\"MCP协议集成测试\"\"\"
    
    @pytest.fixture
    async def mcp_session(self):
        \"\"\"创建MCP客户端会话\"\"\"
        # 启动MCP服务器进程
        import subprocess
        import sys
        
        server_process = subprocess.Popen(
            [sys.executable, "-m", "src.{project_name}.server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 创建客户端连接
        async with stdio_client(server_process.stdin, server_process.stdout) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化连接
                await session.initialize()
                yield session
        
        # 清理
        server_process.terminate()
        server_process.wait()
    
    @pytest.mark.asyncio
    async def test_full_mcp_workflow(self, mcp_session):
        \"\"\"测试完整的MCP工作流\"\"\"
        
        # 1. 测试工具列表
        tools_response = await mcp_session.list_tools()
        assert tools_response.tools
        assert len(tools_response.tools) > 0
        
        # 2. 测试工具调用
        if tools_response.tools:
            tool = tools_response.tools[0]
            call_result = await mcp_session.call_tool(
                tool.name,
                arguments=self._get_valid_arguments_for_tool(tool)
            )
            assert call_result
            assert not call_result.isError
        
        # 3. 测试提示词列表
        prompts_response = await mcp_session.list_prompts()
        assert prompts_response.prompts is not None
        
        # 4. 测试提示词获取
        if prompts_response.prompts:
            prompt = prompts_response.prompts[0]
            prompt_result = await mcp_session.get_prompt(
                prompt.name,
                arguments=self._get_valid_arguments_for_prompt(prompt)
            )
            assert prompt_result
            assert prompt_result.messages
        
        # 5. 测试资源列表
        resources_response = await mcp_session.list_resources()
        assert resources_response.resources is not None
        
        # 6. 测试资源读取
        if resources_response.resources:
            resource = resources_response.resources[0]
            resource_result = await mcp_session.read_resource(resource.uri)
            assert resource_result
            assert resource_result.contents
    
    def _get_valid_arguments_for_tool(self, tool) -> dict:
        \"\"\"为工具生成有效参数\"\"\"
        arguments = {}
        
        if hasattr(tool, 'inputSchema') and tool.inputSchema:
            properties = tool.inputSchema.get('properties', {})
            required = tool.inputSchema.get('required', [])
            
            for param_name in required:
                if param_name in properties:
                    param_type = properties[param_name].get('type', 'string')
                    if param_type == 'string':
                        arguments[param_name] = 'test_value'
                    elif param_type == 'integer':
                        arguments[param_name] = 1
                    elif param_type == 'boolean':
                        arguments[param_name] = True
        
        return arguments
    
    def _get_valid_arguments_for_prompt(self, prompt) -> dict:
        \"\"\"为提示词生成有效参数\"\"\"
        arguments = {}
        
        if hasattr(prompt, 'arguments') and prompt.arguments:
            for arg in prompt.arguments:
                if arg.required:
                    arguments[arg.name] = 'test_content'
        
        return arguments

    @pytest.mark.asyncio
    async def test_error_handling(self, mcp_session):
        \"\"\"测试错误处理\"\"\"
        
        # 测试不存在的工具
        try:
            await mcp_session.call_tool("nonexistent_tool", {})
            assert False, "Should have raised an error"
        except Exception:
            pass  # 预期的错误
        
        # 测试无效参数的工具调用
        tools_response = await mcp_session.list_tools()
        if tools_response.tools:
            tool = tools_response.tools[0]
            result = await mcp_session.call_tool(tool.name, {"invalid": "params"})
            # 应该返回错误结果而不是抛异常
            assert result.isError or "error" in str(result).lower()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, mcp_session):
        \"\"\"测试并发请求处理\"\"\"
        
        # 并发执行多个请求
        tasks = []
        for _ in range(5):
            tasks.append(mcp_session.list_tools())
            tasks.append(mcp_session.list_prompts())
            tasks.append(mcp_session.list_resources())
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 检查所有请求都成功完成
        for result in results:
            assert not isinstance(result, Exception)
```

**TypeScript测试框架**
```typescript
// tests/integration/mcp-protocol.test.ts
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn, ChildProcess } from 'child_process';

describe('MCP Protocol Integration', () => {
  let serverProcess: ChildProcess;
  let client: Client;

  beforeAll(async () => {
    // 启动MCP服务器进程
    serverProcess = spawn('node', ['dist/index.js'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 创建客户端连接
    const transport = new StdioClientTransport({
      stdin: serverProcess.stdout!,
      stdout: serverProcess.stdin!
    });

    client = new Client(
      {
        name: 'test-client',
        version: '1.0.0'
      },
      {
        capabilities: {}
      }
    );

    await client.connect(transport);
  });

  afterAll(async () => {
    await client.close();
    serverProcess.kill();
  });

  test('should complete full MCP workflow', async () => {
    // 1. 测试工具列表
    const toolsResponse = await client.request(
      { method: 'tools/list' },
      { method: 'tools/list' }
    );
    expect(toolsResponse.tools).toBeDefined();

    // 2. 测试工具调用
    if (toolsResponse.tools && toolsResponse.tools.length > 0) {
      const tool = toolsResponse.tools[0];
      const callResult = await client.request(
        { 
          method: 'tools/call',
          params: {
            name: tool.name,
            arguments: getValidArgumentsForTool(tool)
          }
        },
        { method: 'tools/call' }
      );
      expect(callResult).toBeDefined();
      expect(callResult.isError).toBeFalsy();
    }

    // 3. 测试提示词和资源...
  });

  test('should handle errors gracefully', async () => {
    // 测试不存在的工具调用
    const result = await client.request(
      {
        method: 'tools/call',
        params: {
          name: 'nonexistent_tool',
          arguments: {}
        }
      },
      { method: 'tools/call' }
    );

    expect(result.isError).toBeTruthy();
  });
});

function getValidArgumentsForTool(tool: any): Record<string, any> {
  const arguments_: Record<string, any> = {};
  
  if (tool.inputSchema?.properties) {
    const required = tool.inputSchema.required || [];
    
    for (const paramName of required) {
      const param = tool.inputSchema.properties[paramName];
      if (param.type === 'string') {
        arguments_[paramName] = 'test_value';
      } else if (param.type === 'number') {
        arguments_[paramName] = 1;
      } else if (param.type === 'boolean') {
        arguments_[paramName] = true;
      }
    }
  }
  
  return arguments_;
}
```

#### 1.2 端到端功能测试

**功能测试脚本模板**
```python
# tests/e2e/test_functionality.py
import pytest
import asyncio
from tests.helpers import MCPTestClient

class TestEndToEndFunctionality:
    \"\"\"端到端功能测试\"\"\"
    
    @pytest.fixture
    async def mcp_client(self):
        async with MCPTestClient() as client:
            yield client
    
    @pytest.mark.asyncio
    async def test_data_query_workflow(self, mcp_client):
        \"\"\"测试数据查询完整流程\"\"\"
        
        # 1. 使用工具查询数据
        query_result = await mcp_client.call_tool('data_query', {
            'query': 'test search term',
            'limit': 10
        })
        
        assert query_result['success']
        assert 'data' in query_result
        
        # 2. 使用提示词分析结果
        analysis_prompt = await mcp_client.get_prompt('data_analysis', {
            'content': str(query_result['data']),
            'analysis_type': 'summary'
        })
        
        assert len(analysis_prompt.messages) > 0
        
        # 3. 获取相关配置资源
        config_resource = await mcp_client.read_resource('config://application')
        assert config_resource.contents
    
    @pytest.mark.asyncio
    async def test_content_generation_workflow(self, mcp_client):
        \"\"\"测试内容生成完整流程\"\"\"
        
        # 1. 使用提示词生成内容
        content_prompt = await mcp_client.get_prompt('content_generation', {
            'content_type': 'article',
            'topic': 'MCP Server Development',
            'length': 'medium'
        })
        
        assert len(content_prompt.messages) >= 2
        
        # 2. 获取API文档资源作为参考
        docs_resource = await mcp_client.read_resource('docs://api')
        assert docs_resource.contents
        
        # 3. 使用工具验证生成的内容格式
        if hasattr(mcp_client, 'validate_content_tool'):
            validation_result = await mcp_client.call_tool('validate_content', {
                'content': 'sample generated content',
                'content_type': 'article'
            })
            assert validation_result['success']
    
    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self, mcp_client):
        \"\"\"测试错误恢复流程\"\"\"
        
        # 1. 故意触发工具错误
        error_result = await mcp_client.call_tool('nonexistent_tool', {})
        assert error_result.get('isError', True)
        
        # 2. 验证服务器仍然响应正常请求
        tools_list = await mcp_client.list_tools()
        assert len(tools_list.tools) > 0
        
        # 3. 测试参数验证错误恢复
        if len(tools_list.tools) > 0:
            tool = tools_list.tools[0]
            invalid_result = await mcp_client.call_tool(tool.name, {
                'invalid_param': 'value'
            })
            # 应该返回参数错误而不是崩溃
            assert 'error' in str(invalid_result).lower()
```

### 验证标准
- [ ] 所有MCP协议操作正常工作
- [ ] 并发请求处理正确
- [ ] 错误处理机制有效
- [ ] 端到端功能流程完整

### 输出结果
```
集成测试报告包含：
1. MCP协议合规性验证
2. 功能完整性测试结果
3. 并发处理能力验证
4. 错误处理机制测试
```

---

## 第2步：性能测试与优化

### 输入要求
- 通过集成测试的MCP Server
- 性能基准要求
- 负载测试场景

### 执行步骤

#### 2.1 性能基准测试

**Python性能测试**
```python
# tests/performance/test_benchmarks.py
import pytest
import asyncio
import time
import psutil
import gc
from tests.helpers import MCPTestClient

class TestPerformanceBenchmarks:
    \"\"\"性能基准测试\"\"\"
    
    @pytest.mark.asyncio
    async def test_tool_execution_performance(self):
        \"\"\"测试工具执行性能\"\"\"
        async with MCPTestClient() as client:
            tools = await client.list_tools()
            
            if not tools.tools:
                pytest.skip("No tools available for testing")
            
            tool = tools.tools[0]
            valid_args = self._get_valid_arguments_for_tool(tool)
            
            # 预热
            for _ in range(5):
                await client.call_tool(tool.name, valid_args)
            
            # 性能测试
            start_time = time.time()
            results = []
            
            for _ in range(100):
                result = await client.call_tool(tool.name, valid_args)
                results.append(result)
            
            end_time = time.time()
            
            # 验证性能指标
            total_time = end_time - start_time
            avg_time = total_time / 100
            
            assert avg_time < 1.0, f"Average tool execution time too slow: {avg_time}s"
            assert all(not r.get('isError', False) for r in results), "Some tool calls failed"
            
            print(f"Tool execution benchmark: {avg_time:.3f}s average, {total_time:.3f}s total")
    
    @pytest.mark.asyncio
    async def test_concurrent_load_performance(self):
        \"\"\"测试并发负载性能\"\"\"
        async with MCPTestClient() as client:
            
            async def make_request():
                return await client.list_tools()
            
            # 并发测试
            concurrent_levels = [1, 5, 10, 20]
            
            for concurrency in concurrent_levels:
                start_time = time.time()
                
                tasks = [make_request() for _ in range(concurrency)]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                end_time = time.time()
                
                # 验证结果
                assert all(not isinstance(r, Exception) for r in results), \
                    f"Some requests failed at concurrency {concurrency}"
                
                total_time = end_time - start_time
                avg_time = total_time / concurrency
                
                assert total_time < 10.0, \
                    f"Concurrent requests took too long: {total_time}s for {concurrency} requests"
                
                print(f"Concurrency {concurrency}: {total_time:.3f}s total, {avg_time:.3f}s average")
    
    @pytest.mark.asyncio
    async def test_memory_usage_performance(self):
        \"\"\"测试内存使用性能\"\"\"
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        async with MCPTestClient() as client:
            
            # 执行大量操作
            for _ in range(1000):
                await client.list_tools()
                await client.list_prompts()
                await client.list_resources()
                
                # 每100次操作检查一次内存
                if _ % 100 == 0:
                    current_memory = process.memory_info().rss
                    memory_increase = (current_memory - initial_memory) / 1024 / 1024  # MB
                    
                    # 内存增长不应超过100MB
                    assert memory_increase < 100, \
                        f"Memory usage increased too much: {memory_increase:.2f}MB"
            
            # 强制垃圾回收
            gc.collect()
            
            final_memory = process.memory_info().rss
            total_increase = (final_memory - initial_memory) / 1024 / 1024
            
            print(f"Memory usage test: {total_increase:.2f}MB increase after 1000 operations")
            assert total_increase < 50, f"Memory leak detected: {total_increase:.2f}MB increase"
    
    @pytest.mark.asyncio
    async def test_resource_intensive_operations(self):
        \"\"\"测试资源密集型操作\"\"\"
        async with MCPTestClient() as client:
            
            # 测试大型资源读取
            resources = await client.list_resources()
            
            start_time = time.time()
            
            for resource in resources.resources[:5]:  # 限制测试数量
                content = await client.read_resource(resource.uri)
                assert content.contents
            
            end_time = time.time()
            
            assert (end_time - start_time) < 30.0, "Resource reading took too long"
            
            print(f"Resource reading benchmark: {end_time - start_time:.3f}s for {len(resources.resources[:5])} resources")
```

**TypeScript性能测试**
```typescript
// tests/performance/benchmarks.test.ts
import { performance } from 'perf_hooks';
import { MCPTestClient } from '../helpers';

describe('Performance Benchmarks', () => {
  
  test('tool execution performance', async () => {
    const client = new MCPTestClient();
    await client.connect();
    
    try {
      const tools = await client.listTools();
      
      if (tools.tools.length === 0) {
        return; // Skip if no tools
      }
      
      const tool = tools.tools[0];
      const validArgs = getValidArgumentsForTool(tool);
      
      // 预热
      for (let i = 0; i < 5; i++) {
        await client.callTool(tool.name, validArgs);
      }
      
      // 性能测试
      const startTime = performance.now();
      const results = [];
      
      for (let i = 0; i < 100; i++) {
        const result = await client.callTool(tool.name, validArgs);
        results.push(result);
      }
      
      const endTime = performance.now();
      const totalTime = (endTime - startTime) / 1000; // 转换为秒
      const avgTime = totalTime / 100;
      
      expect(avgTime).toBeLessThan(1.0);
      expect(results.every(r => !r.isError)).toBeTruthy();
      
      console.log(`Tool execution benchmark: ${avgTime.toFixed(3)}s average`);
      
    } finally {
      await client.disconnect();
    }
  });
  
  test('concurrent load performance', async () => {
    const client = new MCPTestClient();
    await client.connect();
    
    try {
      const concurrencyLevels = [1, 5, 10, 20];
      
      for (const concurrency of concurrencyLevels) {
        const startTime = performance.now();
        
        const promises = Array(concurrency).fill(0).map(() => client.listTools());
        const results = await Promise.all(promises);
        
        const endTime = performance.now();
        const totalTime = (endTime - startTime) / 1000;
        
        expect(results.every(r => r.tools !== undefined)).toBeTruthy();
        expect(totalTime).toBeLessThan(10.0);
        
        console.log(`Concurrency ${concurrency}: ${totalTime.toFixed(3)}s total`);
      }
      
    } finally {
      await client.disconnect();
    }
  });
  
  test('memory usage performance', async () => {
    const client = new MCPTestClient();
    await client.connect();
    
    try {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // 执行大量操作
      for (let i = 0; i < 1000; i++) {
        await client.listTools();
        await client.listPrompts();
        await client.listResources();
        
        if (i % 100 === 0) {
          const currentMemory = process.memoryUsage().heapUsed;
          const memoryIncrease = (currentMemory - initialMemory) / 1024 / 1024; // MB
          
          expect(memoryIncrease).toBeLessThan(100);
        }
      }
      
      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const totalIncrease = (finalMemory - initialMemory) / 1024 / 1024;
      
      expect(totalIncrease).toBeLessThan(50);
      console.log(`Memory usage test: ${totalIncrease.toFixed(2)}MB increase`);
      
    } finally {
      await client.disconnect();
    }
  });
});
```

#### 2.2 性能优化建议

**优化检查清单**
```
性能优化方向：

1. 工具执行优化：
   - [ ] 实现工具结果缓存
   - [ ] 优化API调用频率
   - [ ] 使用连接池复用
   - [ ] 实现请求去重

2. 内存管理优化：
   - [ ] 定期清理缓存
   - [ ] 优化大对象处理
   - [ ] 实现内存监控
   - [ ] 避免内存泄漏

3. 并发处理优化：
   - [ ] 实现请求队列
   - [ ] 限制并发数量
   - [ ] 优化异步处理
   - [ ] 实现背压控制

4. 资源访问优化：
   - [ ] 实现流式读取
   - [ ] 压缩大型资源
   - [ ] 缓存静态资源
   - [ ] 优化文件I/O
```

### 验证标准
- [ ] 工具平均执行时间 < 1秒
- [ ] 并发请求处理能力 >= 20个/秒
- [ ] 内存使用增长 < 50MB/1000次操作
- [ ] 资源读取时间在合理范围内

### 输出结果
```
性能测试报告包含：
1. 基准性能指标
2. 并发处理能力评估
3. 内存使用分析
4. 优化建议和实施计划
```

---

## 第3步：部署准备

### 输入要求
- 通过性能测试的MCP Server
- 部署环境规范
- 配置管理要求

### 执行步骤

#### 3.1 生产环境配置

**Python部署配置**
```python
# deploy/production_config.py
import os
from typing import Dict, Any

class ProductionConfig:
    \"\"\"生产环境配置\"\"\"
    
    @staticmethod
    def get_environment_config() -> Dict[str, Any]:
        return {
            # 应用配置
            'APP_NAME': os.getenv('APP_NAME', '{project_name}'),
            'ENV': 'production',
            'DEBUG': False,
            
            # API配置
            'API_BASE_URL': os.getenv('API_BASE_URL'),
            'API_KEY': os.getenv('API_KEY'),
            'API_TIMEOUT': int(os.getenv('API_TIMEOUT', '30')),
            'API_RETRY_COUNT': int(os.getenv('API_RETRY_COUNT', '3')),
            
            # 缓存配置
            'CACHE_ENABLED': os.getenv('CACHE_ENABLED', 'true').lower() == 'true',
            'CACHE_TTL': int(os.getenv('CACHE_TTL', '300')),
            'CACHE_MAX_SIZE': int(os.getenv('CACHE_MAX_SIZE', '1000')),
            
            # 日志配置
            'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
            'LOG_FILE': os.getenv('LOG_FILE', '/var/log/{project_name}/app.log'),
            'LOG_MAX_SIZE': int(os.getenv('LOG_MAX_SIZE', '10485760')),  # 10MB
            'LOG_BACKUP_COUNT': int(os.getenv('LOG_BACKUP_COUNT', '5')),
            
            # 性能配置
            'MAX_CONCURRENT_REQUESTS': int(os.getenv('MAX_CONCURRENT_REQUESTS', '50')),
            'REQUEST_TIMEOUT': int(os.getenv('REQUEST_TIMEOUT', '300')),
            'MEMORY_LIMIT_MB': int(os.getenv('MEMORY_LIMIT_MB', '512')),
            
            # 监控配置
            'METRICS_ENABLED': os.getenv('METRICS_ENABLED', 'true').lower() == 'true',
            'HEALTH_CHECK_INTERVAL': int(os.getenv('HEALTH_CHECK_INTERVAL', '60')),
        }
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> None:
        \"\"\"验证配置有效性\"\"\"
        required_keys = ['API_BASE_URL', 'API_KEY']
        
        for key in required_keys:
            if not config.get(key):
                raise ValueError(f"Required configuration missing: {key}")
        
        # 验证URL格式
        import urllib.parse
        parsed_url = urllib.parse.urlparse(config['API_BASE_URL'])
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError(f"Invalid API_BASE_URL: {config['API_BASE_URL']}")
        
        # 验证数值范围
        if config['API_TIMEOUT'] <= 0:
            raise ValueError("API_TIMEOUT must be positive")
        
        if config['CACHE_TTL'] <= 0:
            raise ValueError("CACHE_TTL must be positive")

# deploy/health_check.py
import asyncio
import sys
import logging
from src.{project_name}.server import {ClassName}Server
from src.{project_name}.config.settings import Settings

async def health_check() -> bool:
    \"\"\"健康检查函数\"\"\"
    try:
        # 创建服务器实例
        settings = Settings()
        server = {ClassName}Server()
        
        # 验证核心组件
        tools = server.tool_manager.list_tools()
        prompts = server.prompt_manager.list_prompts()
        resources = server.resource_manager.list_resources()
        
        # 基本健康检查
        if len(tools) == 0:
            logging.error("No tools registered")
            return False
        
        # 测试一个简单的工具调用
        if tools:
            try:
                # 使用mock参数测试第一个工具
                test_args = _get_test_arguments_for_tool(tools[0])
                result = await server.tool_manager.call_tool({
                    'params': {'name': tools[0].name, 'arguments': test_args}
                })
                
                if result.isError:
                    logging.error(f"Tool execution failed: {result}")
                    return False
                    
            except Exception as e:
                logging.error(f"Tool test failed: {str(e)}")
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"Health check failed: {str(e)}")
        return False

def _get_test_arguments_for_tool(tool) -> dict:
    \"\"\"生成工具测试参数\"\"\"
    # 简化的参数生成逻辑
    return {}

if __name__ == "__main__":
    import asyncio
    
    logging.basicConfig(level=logging.INFO)
    
    result = asyncio.run(health_check())
    sys.exit(0 if result else 1)
```

**TypeScript部署配置**
```typescript
// deploy/production-config.ts
import { z } from 'zod';

const ProductionConfigSchema = z.object({
  // 应用配置
  appName: z.string().default(process.env.APP_NAME || '{project_name}'),
  env: z.literal('production'),
  debug: z.literal(false),
  
  // API配置
  apiBaseUrl: z.string().url(),
  apiKey: z.string().min(1),
  apiTimeout: z.number().positive().default(30000),
  apiRetryCount: z.number().int().positive().default(3),
  
  // 缓存配置
  cacheEnabled: z.boolean().default(true),
  cacheTtl: z.number().positive().default(300),
  cacheMaxSize: z.number().int().positive().default(1000),
  
  // 日志配置
  logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  logFile: z.string().default('/var/log/{project_name}/app.log'),
  logMaxSize: z.number().positive().default(10485760), // 10MB
  logBackupCount: z.number().int().positive().default(5),
  
  // 性能配置
  maxConcurrentRequests: z.number().int().positive().default(50),
  requestTimeout: z.number().positive().default(300000),
  memoryLimitMb: z.number().int().positive().default(512),
  
  // 监控配置
  metricsEnabled: z.boolean().default(true),
  healthCheckInterval: z.number().int().positive().default(60)
});

export type ProductionConfig = z.infer<typeof ProductionConfigSchema>;

export function getProductionConfig(): ProductionConfig {
  const config = {
    appName: process.env.APP_NAME,
    env: 'production' as const,
    debug: false as const,
    apiBaseUrl: process.env.API_BASE_URL,
    apiKey: process.env.API_KEY,
    apiTimeout: process.env.API_TIMEOUT ? parseInt(process.env.API_TIMEOUT) : undefined,
    apiRetryCount: process.env.API_RETRY_COUNT ? parseInt(process.env.API_RETRY_COUNT) : undefined,
    cacheEnabled: process.env.CACHE_ENABLED !== 'false',
    cacheTtl: process.env.CACHE_TTL ? parseInt(process.env.CACHE_TTL) : undefined,
    cacheMaxSize: process.env.CACHE_MAX_SIZE ? parseInt(process.env.CACHE_MAX_SIZE) : undefined,
    logLevel: process.env.LOG_LEVEL as any,
    logFile: process.env.LOG_FILE,
    logMaxSize: process.env.LOG_MAX_SIZE ? parseInt(process.env.LOG_MAX_SIZE) : undefined,
    logBackupCount: process.env.LOG_BACKUP_COUNT ? parseInt(process.env.LOG_BACKUP_COUNT) : undefined,
    maxConcurrentRequests: process.env.MAX_CONCURRENT_REQUESTS ? parseInt(process.env.MAX_CONCURRENT_REQUESTS) : undefined,
    requestTimeout: process.env.REQUEST_TIMEOUT ? parseInt(process.env.REQUEST_TIMEOUT) : undefined,
    memoryLimitMb: process.env.MEMORY_LIMIT_MB ? parseInt(process.env.MEMORY_LIMIT_MB) : undefined,
    metricsEnabled: process.env.METRICS_ENABLED !== 'false',
    healthCheckInterval: process.env.HEALTH_CHECK_INTERVAL ? parseInt(process.env.HEALTH_CHECK_INTERVAL) : undefined
  };

  return ProductionConfigSchema.parse(config);
}

// deploy/health-check.ts
import { MCPServer } from '../src/server.js';
import { logger } from '../src/utils/common.js';

export async function healthCheck(): Promise<boolean> {
  try {
    const server = new MCPServer();
    
    // 验证核心组件初始化
    const tools = server['toolManager'].listTools();
    const prompts = server['promptManager'].listPrompts();
    const resources = server['resourceManager'].listResources();
    
    if (tools.length === 0) {
      logger.error('No tools registered');
      return false;
    }
    
    // 测试简单的工具调用
    if (tools.length > 0) {
      try {
        const testArgs = getTestArgumentsForTool(tools[0]);
        const result = await server['toolManager'].callTool({
          params: { name: tools[0].name, arguments: testArgs }
        });
        
        if (result.isError) {
          logger.error('Tool execution failed:', result);
          return false;
        }
      } catch (error) {
        logger.error('Tool test failed:', error);
        return false;
      }
    }
    
    return true;
    
  } catch (error) {
    logger.error('Health check failed:', error);
    return false;
  }
}

function getTestArgumentsForTool(tool: any): Record<string, any> {
  // 简化的参数生成逻辑
  return {};
}

// 命令行健康检查
if (import.meta.url === `file://${process.argv[1]}`) {
  healthCheck().then(result => {
    process.exit(result ? 0 : 1);
  }).catch(error => {
    console.error('Health check error:', error);
    process.exit(1);
  });
}
```

#### 3.2 Docker容器化部署

**多阶段Dockerfile优化**
```dockerfile
# Dockerfile
ARG NODE_VERSION=20
ARG PYTHON_VERSION=3.11

# === Python构建阶段 ===
FROM python:${PYTHON_VERSION}-slim as python-builder

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制Python依赖文件
COPY requirements.txt requirements-dev.txt ./

# 安装Python依赖
RUN pip install --no-cache-dir --user -r requirements.txt

# === TypeScript构建阶段 ===
FROM node:${NODE_VERSION}-alpine as typescript-builder

WORKDIR /app

# 复制package文件
COPY package*.json tsconfig.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/

# 构建应用
RUN npm run build

# === 生产阶段 ===
FROM node:${NODE_VERSION}-alpine as production

# 安装Python运行时（如果需要）
RUN apk add --no-cache python3 py3-pip

# 安装dumb-init用于信号处理
RUN apk add --no-cache dumb-init

# 创建非root用户
RUN addgroup -g 1001 -S mcpuser && \
    adduser -S mcpuser -u 1001 -G mcpuser

WORKDIR /app

# 从构建阶段复制文件
COPY --from=python-builder --chown=mcpuser:mcpuser /root/.local /home/<USER>/.local
COPY --from=typescript-builder --chown=mcpuser:mcpuser /app/node_modules ./node_modules/
COPY --from=typescript-builder --chown=mcpuser:mcpuser /app/dist ./dist/
COPY --from=typescript-builder --chown=mcpuser:mcpuser /app/package*.json ./

# 复制配置和脚本
COPY --chown=mcpuser:mcpuser deploy/ ./deploy/
COPY --chown=mcpuser:mcpuser scripts/ ./scripts/

# 设置环境变量
ENV NODE_ENV=production
ENV PATH="/home/<USER>/.local/bin:$PATH"

# 创建必要的目录
RUN mkdir -p /app/logs /app/tmp && \
    chown -R mcpuser:mcpuser /app/logs /app/tmp

# 切换到非root用户
USER mcpuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node deploy/health-check.js || exit 1

# 暴露端口（如果需要HTTP接口）
EXPOSE 3000

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/index.js"]
```

**Docker Compose生产配置**
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    environment:
      - NODE_ENV=production
      - API_BASE_URL=${API_BASE_URL}
      - API_KEY=${API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - CACHE_ENABLED=${CACHE_ENABLED:-true}
      - CACHE_TTL=${CACHE_TTL:-300}
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-50}
      - MEMORY_LIMIT_MB=${MEMORY_LIMIT_MB:-512}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    networks:
      - mcp-network
    depends_on:
      - redis
      - monitoring

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - mcp-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

  monitoring:
    image: prom/prometheus:latest
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - mcp-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - mcp-network
    depends_on:
      - mcp-server

volumes:
  redis_data:
  prometheus_data:

networks:
  mcp-network:
    driver: bridge
```

#### 3.3 部署自动化脚本

**部署脚本**
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# 配置变量
PROJECT_NAME="{project_name}"
DEPLOY_ENV="${1:-production}"
VERSION="${2:-latest}"

echo "🚀 Starting deployment of $PROJECT_NAME to $DEPLOY_ENV environment"

# 1. 验证环境变量
if [ "$DEPLOY_ENV" = "production" ]; then
    required_vars=("API_BASE_URL" "API_KEY")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo "❌ Error: Required environment variable $var is not set"
            exit 1
        fi
    done
fi

# 2. 构建Docker镜像
echo "📦 Building Docker image..."
docker build -t $PROJECT_NAME:$VERSION .

# 3. 运行测试
echo "🧪 Running tests..."
docker run --rm $PROJECT_NAME:$VERSION npm test

# 4. 健康检查
echo "🏥 Running health check..."
docker run --rm $PROJECT_NAME:$VERSION node deploy/health-check.js

# 5. 停止现有服务
echo "🛑 Stopping existing services..."
docker-compose -f docker-compose.prod.yml down

# 6. 启动新服务
echo "🟢 Starting new services..."
docker-compose -f docker-compose.prod.yml up -d

# 7. 等待服务启动
echo "⏳ Waiting for services to start..."
sleep 30

# 8. 验证部署
echo "✅ Verifying deployment..."
if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
    echo "🎉 Deployment successful!"
    
    # 显示服务状态
    docker-compose -f docker-compose.prod.yml ps
    
    # 显示日志
    echo "📋 Recent logs:"
    docker-compose -f docker-compose.prod.yml logs --tail=20 mcp-server
else
    echo "❌ Deployment failed!"
    docker-compose -f docker-compose.prod.yml logs mcp-server
    exit 1
fi

echo "✨ Deployment completed successfully!"
```

**回滚脚本**
```bash
#!/bin/bash
# scripts/rollback.sh

set -e

PROJECT_NAME="{project_name}"
BACKUP_VERSION="${1}"

if [ -z "$BACKUP_VERSION" ]; then
    echo "❌ Error: Please specify backup version"
    echo "Usage: $0 <backup_version>"
    exit 1
fi

echo "🔄 Rolling back $PROJECT_NAME to version $BACKUP_VERSION"

# 1. 停止当前服务
echo "🛑 Stopping current services..."
docker-compose -f docker-compose.prod.yml down

# 2. 切换到备份版本
echo "🔀 Switching to backup version..."
docker tag $PROJECT_NAME:$BACKUP_VERSION $PROJECT_NAME:latest

# 3. 启动服务
echo "🟢 Starting services with backup version..."
docker-compose -f docker-compose.prod.yml up -d

# 4. 验证回滚
echo "✅ Verifying rollback..."
sleep 30

if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
    echo "🎉 Rollback successful!"
    docker-compose -f docker-compose.prod.yml ps
else
    echo "❌ Rollback failed!"
    docker-compose -f docker-compose.prod.yml logs mcp-server
    exit 1
fi
```

### 验证标准
- [ ] 生产配置文件完整且有效
- [ ] 健康检查机制正常工作
- [ ] Docker容器正确构建和运行
- [ ] 部署脚本执行成功

### 输出结果
```
部署准备包含：
1. 生产环境配置文件
2. 健康检查和监控机制
3. Docker容器化配置
4. 自动化部署和回滚脚本
```

---

## 第4步：监控和维护

### 输入要求
- 已部署的MCP Server
- 监控需求规范
- 维护计划要求

### 执行步骤

#### 4.1 监控系统设置

**应用监控配置**
```python
# monitoring/metrics.py
import time
import psutil
import logging
from typing import Dict, Any
from prometheus_client import Counter, Histogram, Gauge, start_http_server

class MetricsCollector:
    \"\"\"指标收集器\"\"\"
    
    def __init__(self):
        # 工具调用指标
        self.tool_calls_total = Counter(
            'mcp_tool_calls_total',
            'Total number of tool calls',
            ['tool_name', 'status']
        )
        
        self.tool_call_duration = Histogram(
            'mcp_tool_call_duration_seconds',
            'Tool call duration in seconds',
            ['tool_name']
        )
        
        # 提示词指标
        self.prompt_requests_total = Counter(
            'mcp_prompt_requests_total',
            'Total number of prompt requests',
            ['prompt_name']
        )
        
        # 资源指标
        self.resource_reads_total = Counter(
            'mcp_resource_reads_total',
            'Total number of resource reads',
            ['resource_uri']
        )
        
        # 系统指标
        self.memory_usage = Gauge(
            'mcp_memory_usage_bytes',
            'Current memory usage in bytes'
        )
        
        self.cpu_usage = Gauge(
            'mcp_cpu_usage_percent',
            'Current CPU usage percentage'
        )
        
        self.active_connections = Gauge(
            'mcp_active_connections',
            'Number of active MCP connections'
        )
        
        # 错误指标
        self.errors_total = Counter(
            'mcp_errors_total',
            'Total number of errors',
            ['error_type', 'component']
        )
    
    def record_tool_call(self, tool_name: str, duration: float, success: bool):
        \"\"\"记录工具调用指标\"\"\"
        status = 'success' if success else 'error'
        self.tool_calls_total.labels(tool_name=tool_name, status=status).inc()
        self.tool_call_duration.labels(tool_name=tool_name).observe(duration)
    
    def record_prompt_request(self, prompt_name: str):
        \"\"\"记录提示词请求指标\"\"\"
        self.prompt_requests_total.labels(prompt_name=prompt_name).inc()
    
    def record_resource_read(self, resource_uri: str):
        \"\"\"记录资源读取指标\"\"\"
        self.resource_reads_total.labels(resource_uri=resource_uri).inc()
    
    def record_error(self, error_type: str, component: str):
        \"\"\"记录错误指标\"\"\"
        self.errors_total.labels(error_type=error_type, component=component).inc()
    
    def update_system_metrics(self):
        \"\"\"更新系统指标\"\"\"
        process = psutil.Process()
        self.memory_usage.set(process.memory_info().rss)
        self.cpu_usage.set(process.cpu_percent())
    
    def start_metrics_server(self, port: int = 8000):
        \"\"\"启动指标服务器\"\"\"
        start_http_server(port)
        logging.info(f"Metrics server started on port {port}")

# 全局指标收集器实例
metrics = MetricsCollector()

# monitoring/logging_config.py
import logging
import logging.handlers
import json
from datetime import datetime

class StructuredFormatter(logging.Formatter):
    \"\"\"结构化日志格式器\"\"\"
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'tool_name'):
            log_entry['tool_name'] = record.tool_name
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        return json.dumps(log_entry)

def setup_structured_logging(log_file: str, log_level: str = 'INFO'):
    \"\"\"设置结构化日志\"\"\"
    
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(StructuredFormatter())
    root_logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(StructuredFormatter())
    root_logger.addHandler(console_handler)
    
    return root_logger
```

**监控配置文件**
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'mcp-server'
    static_configs:
      - targets: ['mcp-server:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# monitoring/alert_rules.yml
groups:
  - name: mcp_server_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(mcp_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} per second"

      - alert: HighMemoryUsage
        expr: mcp_memory_usage_bytes > 500*1024*1024
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanize }}B"

      - alert: SlowToolCalls
        expr: histogram_quantile(0.95, rate(mcp_tool_call_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Slow tool calls detected"
          description: "95th percentile tool call duration is {{ $value }}s"

      - alert: ServerDown
        expr: up{job="mcp-server"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "MCP Server is down"
          description: "MCP Server has been down for more than 1 minute"
```

#### 4.2 日志管理

**日志聚合和分析**
```bash
#!/bin/bash
# scripts/log_analysis.sh

LOG_DIR="/app/logs"
ANALYSIS_DIR="/app/logs/analysis"
DATE=$(date +%Y-%m-%d)

mkdir -p $ANALYSIS_DIR

echo "📊 Analyzing logs for $DATE"

# 1. 错误统计
echo "🔍 Error Analysis:"
jq -r 'select(.level == "ERROR") | .message' $LOG_DIR/app.log* | sort | uniq -c | sort -nr > $ANALYSIS_DIR/errors_$DATE.txt

# 2. 工具使用统计
echo "🛠️ Tool Usage Statistics:"
jq -r 'select(.tool_name) | .tool_name' $LOG_DIR/app.log* | sort | uniq -c | sort -nr > $ANALYSIS_DIR/tool_usage_$DATE.txt

# 3. 性能分析
echo "⚡ Performance Analysis:"
jq -r 'select(.message | contains("duration")) | .message' $LOG_DIR/app.log* | grep -o '[0-9.]*s' | sort -n > $ANALYSIS_DIR/performance_$DATE.txt

# 4. 生成报告
cat > $ANALYSIS_DIR/daily_report_$DATE.md << EOF
# MCP Server Daily Report - $DATE

## Error Summary
\`\`\`
$(head -10 $ANALYSIS_DIR/errors_$DATE.txt)
\`\`\`

## Top 10 Used Tools
\`\`\`
$(head -10 $ANALYSIS_DIR/tool_usage_$DATE.txt)
\`\`\`

## Performance Summary
- Median response time: $(cat $ANALYSIS_DIR/performance_$DATE.txt | awk '{a[i++]=$1} END{print a[int(i/2)]}')
- 95th percentile: $(cat $ANALYSIS_DIR/performance_$DATE.txt | tail -n $(echo "scale=0; $(wc -l < $ANALYSIS_DIR/performance_$DATE.txt) * 0.05" | bc) | head -1)

## Recommendations
- Review errors above and implement fixes
- Consider optimizing frequently used tools
- Monitor resource usage trends
EOF

echo "✅ Log analysis complete. Report saved to $ANALYSIS_DIR/daily_report_$DATE.md"
```

#### 4.3 自动化维护

**维护脚本**
```bash
#!/bin/bash
# scripts/maintenance.sh

PROJECT_NAME="{project_name}"
MAINTENANCE_TYPE="${1:-routine}"

echo "🔧 Starting $MAINTENANCE_TYPE maintenance for $PROJECT_NAME"

case $MAINTENANCE_TYPE in
    "routine")
        # 日常维护任务
        echo "📋 Running routine maintenance..."
        
        # 清理旧日志
        find /app/logs -name "*.log.*" -mtime +30 -delete
        echo "✅ Cleaned old log files"
        
        # 清理Docker镜像
        docker image prune -f --filter "until=168h"
        echo "✅ Cleaned old Docker images"
        
        # 更新系统指标
        docker-compose -f docker-compose.prod.yml exec -T mcp-server node -e "
            const metrics = require('./monitoring/metrics');
            metrics.update_system_metrics();
            console.log('Updated system metrics');
        "
        
        # 健康检查
        if docker-compose -f docker-compose.prod.yml exec -T mcp-server node deploy/health-check.js; then
            echo "✅ Health check passed"
        else
            echo "❌ Health check failed - requires attention"
            exit 1
        fi
        ;;
        
    "cache")
        # 缓存维护
        echo "🗄️ Running cache maintenance..."
        
        # 清理Redis缓存
        docker-compose -f docker-compose.prod.yml exec -T redis redis-cli FLUSHDB
        echo "✅ Cleared Redis cache"
        
        # 重启服务以清理内存缓存
        docker-compose -f docker-compose.prod.yml restart mcp-server
        echo "✅ Restarted MCP server"
        ;;
        
    "security")
        # 安全维护
        echo "🔒 Running security maintenance..."
        
        # 更新依赖
        docker-compose -f docker-compose.prod.yml exec -T mcp-server npm audit fix
        echo "✅ Updated npm dependencies"
        
        # 检查证书过期
        if [ -f "/app/nginx/ssl/cert.pem" ]; then
            CERT_EXPIRY=$(openssl x509 -enddate -noout -in /app/nginx/ssl/cert.pem | cut -d= -f2)
            CERT_EXPIRY_EPOCH=$(date -d "$CERT_EXPIRY" +%s)
            CURRENT_EPOCH=$(date +%s)
            DAYS_UNTIL_EXPIRY=$(( (CERT_EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
            
            if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
                echo "⚠️ SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
            else
                echo "✅ SSL certificate valid for $DAYS_UNTIL_EXPIRY days"
            fi
        fi
        ;;
        
    *)
        echo "❌ Unknown maintenance type: $MAINTENANCE_TYPE"
        echo "Available types: routine, cache, security"
        exit 1
        ;;
esac

echo "✨ Maintenance completed successfully!"
```

**自动化Cron任务**
```bash
# scripts/setup_cron.sh
#!/bin/bash

# 创建cron任务文件
cat > /tmp/mcp_cron << EOF
# MCP Server维护任务

# 每日日志分析 (每天凌晨2点)
0 2 * * * /app/scripts/log_analysis.sh

# 日常维护 (每周日凌晨3点)
0 3 * * 0 /app/scripts/maintenance.sh routine

# 缓存清理 (每天凌晨4点)
0 4 * * * /app/scripts/maintenance.sh cache

# 安全检查 (每月1号凌晨5点)
0 5 1 * * /app/scripts/maintenance.sh security

# 健康检查 (每小时)
0 * * * * /app/deploy/health-check.py

# 备份配置 (每天凌晨1点)
0 1 * * * tar -czf /app/backups/config_\$(date +\%Y\%m\%d).tar.gz /app/config/
EOF

# 安装cron任务
crontab /tmp/mcp_cron
rm /tmp/mcp_cron

echo "✅ Cron jobs installed successfully"
crontab -l
```

### 验证标准
- [ ] 监控指标正确收集和展示
- [ ] 告警规则按预期触发
- [ ] 日志分析脚本正常运行
- [ ] 自动化维护任务执行成功

### 输出结果
```
监控和维护系统包含：
1. 完整的指标收集和监控配置
2. 结构化日志记录和分析
3. 自动化告警和通知机制
4. 定期维护和清理脚本
```

---

## 完成检查清单

### 测试质量
- [ ] **集成测试**：MCP协议操作全面验证
- [ ] **功能测试**：端到端工作流程完整
- [ ] **性能测试**：基准指标达到要求
- [ ] **错误处理**：异常情况恢复机制有效

### 部署就绪
- [ ] **环境配置**：生产配置完整且安全
- [ ] **容器化**：Docker镜像构建和运行正常
- [ ] **自动化**：部署和回滚脚本可靠
- [ ] **健康检查**：监控和检查机制完善

### 运维支持
- [ ] **监控系统**：指标收集和展示完整
- [ ] **日志管理**：结构化记录和分析到位
- [ ] **维护自动化**：定期任务和清理机制
- [ ] **文档完整**：部署和运维文档清晰

## 下一步行动

✅ **测试部署完成后，请继续：**
- 配置调试 → `workflows/05-configuration-debug.md`
- 文档生成 → `guides/documentation-gen.md`
- 最佳实践 → `guides/best-practices.md`
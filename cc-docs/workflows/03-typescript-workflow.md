# MCP Server TypeScript 开发工作流程

## 任务概述
指导AI助手使用TypeScript和@modelcontextprotocol/sdk完成功能完整的MCP Server开发。

---

## 第1步：MCP Server 核心类实现

### 输入要求
- 已完成的项目初始化（参考 `01-project-setup.md`）
- 明确的功能需求清单

### 执行步骤

#### 1.1 创建主服务器类

**文件：`src/server.ts`**
```typescript
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ListPromptsRequestSchema,
  GetPromptRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
  CallToolResult,
  ListToolsResult,
  ListPromptsResult,
  GetPromptResult,
  ListResourcesResult,
  ReadResourceResult
} from '@modelcontextprotocol/sdk/types.js';

import { Settings } from './config/settings.js';
import { ToolManager } from './tools/index.js';
import { PromptManager } from './prompts/index.js';
import { ResourceManager } from './resources/index.js';
import { setupLogging, logger } from './utils/common.js';

export class MCPServer {
  private server: Server;
  private settings: Settings;
  private toolManager: ToolManager;
  private promptManager: PromptManager;
  private resourceManager: ResourceManager;

  constructor() {
    this.settings = new Settings();
    setupLogging(this.settings.logLevel);
    
    this.server = new Server(
      {
        name: this.settings.appName,
        version: '0.1.0',
      },
      {
        capabilities: {
          tools: {},
          prompts: {},
          resources: {}
        }
      }
    );

    this.toolManager = new ToolManager(this.settings);
    this.promptManager = new PromptManager(this.settings);
    this.resourceManager = new ResourceManager(this.settings);

    this.setupHandlers();
  }

  private setupHandlers(): void {
    // 工具处理程序
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: this.toolManager.listTools()
      } as ListToolsResult;
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      return await this.toolManager.callTool(request);
    });

    // 提示词处理程序
    this.server.setRequestHandler(ListPromptsRequestSchema, async () => {
      return {
        prompts: this.promptManager.listPrompts()
      } as ListPromptsResult;
    });

    this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
      return await this.promptManager.getPrompt(request);
    });

    // 资源处理程序
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      return {
        resources: this.resourceManager.listResources()
      } as ListResourcesResult;
    });

    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      return await this.resourceManager.readResource(request);
    });
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    logger.info(`${this.settings.appName} MCP server running`);
  }
}

export async function main(): Promise<void> {
  try {
    const server = new MCPServer();
    await server.run();
  } catch (error) {
    logger.error('Server startup failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    logger.error('Unhandled error:', error);
    process.exit(1);
  });
}
```

#### 1.2 创建配置管理模块

**文件：`src/config/settings.ts`**
```typescript
import { z } from 'zod';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 配置模式定义
const SettingsSchema = z.object({
  // 基础配置
  appName: z.string().default(process.env.APP_NAME || '{project_name}'),
  debug: z.boolean().default(process.env.DEBUG === 'true'),
  
  // API配置
  apiBaseUrl: z.string().url(),
  apiKey: z.string().optional(),
  apiTimeout: z.number().default(parseInt(process.env.API_TIMEOUT || '30000')),
  
  // 缓存配置
  cacheEnabled: z.boolean().default(process.env.CACHE_ENABLED !== 'false'),
  cacheTtl: z.number().default(parseInt(process.env.CACHE_TTL || '300')),
  
  // 日志配置
  logLevel: z.enum(['debug', 'info', 'warn', 'error']).default(
    (process.env.LOG_LEVEL?.toLowerCase() as any) || 'info'
  )
});

export type SettingsType = z.infer<typeof SettingsSchema>;

export class Settings {
  private config: SettingsType;

  constructor() {
    try {
      this.config = SettingsSchema.parse({
        appName: process.env.APP_NAME,
        debug: process.env.DEBUG === 'true',
        apiBaseUrl: process.env.API_BASE_URL,
        apiKey: process.env.API_KEY,
        apiTimeout: process.env.API_TIMEOUT ? parseInt(process.env.API_TIMEOUT) : undefined,
        cacheEnabled: process.env.CACHE_ENABLED !== 'false',
        cacheTtl: process.env.CACHE_TTL ? parseInt(process.env.CACHE_TTL) : undefined,
        logLevel: process.env.LOG_LEVEL?.toLowerCase()
      });
    } catch (error) {
      throw new Error(`Invalid configuration: ${error}`);
    }
  }

  get appName(): string { return this.config.appName; }
  get debug(): boolean { return this.config.debug; }
  get apiBaseUrl(): string { return this.config.apiBaseUrl; }
  get apiKey(): string | undefined { return this.config.apiKey; }
  get apiTimeout(): number { return this.config.apiTimeout; }
  get cacheEnabled(): boolean { return this.config.cacheEnabled; }
  get cacheTtl(): number { return this.config.cacheTtl; }
  get logLevel(): string { return this.config.logLevel; }

  // 获取API头部
  getApiHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }
    
    return headers;
  }
}
```

#### 1.3 创建入口文件

**文件：`src/index.ts`**
```typescript
#!/usr/bin/env node

import { main } from './server.js';

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// 启动服务器
main().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
```

### 验证标准
- [ ] 服务器类可以正常实例化
- [ ] 所有MCP协议处理程序已注册
- [ ] 配置管理模块可以正确验证和加载环境变量

### 输出结果
```
MCP Server核心框架包含：
1. 主服务器类 (src/server.ts)
2. 配置管理 (src/config/settings.ts)
3. 入口文件 (src/index.ts)
4. 类型安全的配置验证
```

---

## 第2步：工具(Tools)管理器实现

### 输入要求
- 完成的服务器核心类
- 工具功能需求清单

### 执行步骤

#### 2.1 创建工具管理器类型定义

**文件：`src/tools/types.ts`**
```typescript
import { z } from 'zod';
import { Tool, CallToolRequest, CallToolResult } from '@modelcontextprotocol/sdk/types.js';

// 工具处理程序类型
export type ToolHandler = (arguments: Record<string, unknown>) => Promise<unknown>;

// 工具注册接口
export interface ToolRegistration {
  tool: Tool;
  handler: ToolHandler;
}

// API响应类型
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.unknown().optional(),
  error: z.string().optional(),
  message: z.string().optional()
});

export type ApiResponse = z.infer<typeof ApiResponseSchema>;

// 工具执行结果类型
export interface ToolExecutionResult {
  success: boolean;
  data?: unknown;
  error?: string;
}
```

#### 2.2 创建工具管理器基类

**文件：`src/tools/index.ts`**
```typescript
import { Tool, CallToolRequest, CallToolResult, TextContent } from '@modelcontextprotocol/sdk/types.js';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { ToolHandler, ToolRegistration } from './types.js';

export class ToolManager {
  private tools: Map<string, Tool> = new Map();
  private handlers: Map<string, ToolHandler> = new Map();

  constructor(private settings: Settings) {
    this.registerTools();
  }

  private registerTools(): void {
    // 这里将注册具体的工具实现
    logger.info('Registering tools...');
    // TODO: 添加工具注册逻辑
  }

  registerTool(registration: ToolRegistration): void {
    const { tool, handler } = registration;
    
    this.tools.set(tool.name, tool);
    this.handlers.set(tool.name, handler);
    
    logger.debug(`Registered tool: ${tool.name}`);
  }

  listTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  async callTool(request: CallToolRequest): Promise<CallToolResult> {
    const toolName = request.params.name;
    const arguments_ = request.params.arguments || {};

    logger.info(`Calling tool: ${toolName}`, { arguments: arguments_ });

    if (!this.handlers.has(toolName)) {
      const errorMessage = `Unknown tool: ${toolName}`;
      logger.error(errorMessage);
      
      return {
        content: [{
          type: 'text',
          text: errorMessage
        } as TextContent],
        isError: true
      };
    }

    try {
      const handler = this.handlers.get(toolName)!;
      const result = await handler(arguments_);

      logger.info(`Tool ${toolName} executed successfully`);

      return {
        content: [{
          type: 'text',
          text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
        } as TextContent]
      };
    } catch (error) {
      const errorMessage = `Tool execution error: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMessage, { toolName, error });

      return {
        content: [{
          type: 'text', 
          text: errorMessage
        } as TextContent],
        isError: true
      };
    }
  }
}
```

#### 2.3 创建工具基类和示例实现

**文件：`src/tools/base.ts`**
```typescript
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { Tool } from '@modelcontextprotocol/sdk/types.js';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { ApiResponse, ToolExecutionResult } from './types.js';

export abstract class BaseTool {
  protected client: AxiosInstance;

  constructor(protected settings: Settings) {
    this.client = axios.create({
      baseURL: settings.apiBaseUrl,
      timeout: settings.apiTimeout,
      headers: settings.getApiHeaders()
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params
        });
        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`API Response: ${response.status}`, { data: response.data });
        return response;
      },
      (error) => {
        logger.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  protected async makeRequest<T = unknown>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: unknown,
    params?: Record<string, unknown>
  ): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.request({
        method,
        url: endpoint,
        data,
        params
      });

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.message || error.message;
        throw new Error(`API request failed: ${message}`);
      }
      throw error;
    }
  }

  abstract getToolDefinition(): Tool;
  abstract execute(arguments: Record<string, unknown>): Promise<ToolExecutionResult>;
}

// 示例工具实现
export class ExampleTool extends BaseTool {
  getToolDefinition(): Tool {
    return {
      name: 'example_tool',
      description: 'An example tool that demonstrates the TypeScript structure',
      inputSchema: {
        type: 'object',
        properties: {
          param1: {
            type: 'string',
            description: 'First parameter'
          },
          param2: {
            type: 'number',
            description: 'Second parameter',
            default: 10
          }
        },
        required: ['param1']
      }
    };
  }

  async execute(arguments: Record<string, unknown>): Promise<ToolExecutionResult> {
    const { param1, param2 = 10 } = arguments;

    if (typeof param1 !== 'string') {
      throw new Error('param1 must be a string');
    }

    if (typeof param2 !== 'number') {
      throw new Error('param2 must be a number');
    }

    try {
      // 实际的API调用
      const result = await this.makeRequest(
        'GET',
        `/api/example`,
        null,
        { param1, param2 }
      );

      return {
        success: true,
        data: {
          result,
          message: `Processed ${param1} with ${param2}`,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('Example tool execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

// HTTP请求工具
export class HttpRequestTool extends BaseTool {
  getToolDefinition(): Tool {
    return {
      name: 'http_request',
      description: 'Make HTTP requests to external APIs',
      inputSchema: {
        type: 'object',
        properties: {
          method: {
            type: 'string',
            enum: ['GET', 'POST', 'PUT', 'DELETE'],
            description: 'HTTP method'
          },
          url: {
            type: 'string',
            format: 'uri',
            description: 'Request URL'
          },
          headers: {
            type: 'object',
            description: 'Request headers',
            additionalProperties: { type: 'string' }
          },
          body: {
            type: 'object',
            description: 'Request body (for POST/PUT requests)'
          }
        },
        required: ['method', 'url']
      }
    };
  }

  async execute(arguments: Record<string, unknown>): Promise<ToolExecutionResult> {
    const { method, url, headers = {}, body } = arguments;

    if (typeof method !== 'string' || typeof url !== 'string') {
      throw new Error('method and url are required and must be strings');
    }

    try {
      const response = await axios.request({
        method: method as any,
        url,
        headers: headers as Record<string, string>,
        data: body,
        timeout: this.settings.apiTimeout
      });

      return {
        success: true,
        data: {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data
        }
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: `HTTP request failed: ${error.response?.status} ${error.response?.statusText}`,
          data: {
            status: error.response?.status,
            data: error.response?.data
          }
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
```

#### 2.4 更新工具管理器注册逻辑

**更新 `src/tools/index.ts`**
```typescript
// 在 registerTools 方法中添加：
private registerTools(): void {
  logger.info('Registering tools...');
  
  // 注册示例工具
  const exampleTool = new ExampleTool(this.settings);
  this.registerTool({
    tool: exampleTool.getToolDefinition(),
    handler: exampleTool.execute.bind(exampleTool)
  });

  // 注册HTTP请求工具
  const httpTool = new HttpRequestTool(this.settings);
  this.registerTool({
    tool: httpTool.getToolDefinition(), 
    handler: httpTool.execute.bind(httpTool)
  });

  logger.info(`Registered ${this.tools.size} tools`);
}

// 添加导入语句
import { ExampleTool, HttpRequestTool } from './base.js';
```

### 验证标准
- [ ] 工具管理器可以正确注册和列出工具
- [ ] 基础工具类提供完整的HTTP请求封装
- [ ] 示例工具可以正常执行且有类型安全保证

### 输出结果
```
工具管理系统包含：
1. 类型定义 (src/tools/types.ts)
2. 工具管理器类 (src/tools/index.ts)
3. 基础工具类 (src/tools/base.ts)
4. 示例工具实现
5. 完整的错误处理和日志记录
```

---

## 第3步：提示词(Prompts)管理器实现

### 输入要求
- 完成的工具管理器
- 提示词需求清单

### 执行步骤

#### 3.1 创建提示词类型定义

**文件：`src/prompts/types.ts`**
```typescript
import { Prompt, PromptMessage, GetPromptRequest, GetPromptResult } from '@modelcontextprotocol/sdk/types.js';

// 提示词生成器类型
export type PromptGenerator = (arguments: Record<string, unknown>) => Promise<PromptMessage[]>;

// 提示词注册接口
export interface PromptRegistration {
  prompt: Prompt;
  generator: PromptGenerator;
}

// 提示词模板参数
export interface PromptTemplate {
  system?: string;
  user?: string;
  assistant?: string;
}

// 消息角色类型
export type MessageRole = 'system' | 'user' | 'assistant';
```

#### 3.2 创建提示词管理器

**文件：`src/prompts/index.ts`**
```typescript
import { 
  Prompt, 
  PromptMessage, 
  GetPromptRequest, 
  GetPromptResult 
} from '@modelcontextprotocol/sdk/types.js';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { PromptGenerator, PromptRegistration } from './types.js';

export class PromptManager {
  private prompts: Map<string, Prompt> = new Map();
  private generators: Map<string, PromptGenerator> = new Map();

  constructor(private settings: Settings) {
    this.registerPrompts();
  }

  private registerPrompts(): void {
    logger.info('Registering prompts...');
    // TODO: 添加提示词注册逻辑
  }

  registerPrompt(registration: PromptRegistration): void {
    const { prompt, generator } = registration;
    
    this.prompts.set(prompt.name, prompt);
    this.generators.set(prompt.name, generator);
    
    logger.debug(`Registered prompt: ${prompt.name}`);
  }

  listPrompts(): Prompt[] {
    return Array.from(this.prompts.values());
  }

  async getPrompt(request: GetPromptRequest): Promise<GetPromptResult> {
    const promptName = request.name;
    const arguments_ = request.arguments || {};

    logger.info(`Getting prompt: ${promptName}`, { arguments: arguments_ });

    if (!this.generators.has(promptName)) {
      throw new Error(`Unknown prompt: ${promptName}`);
    }

    try {
      const generator = this.generators.get(promptName)!;
      const messages = await generator(arguments_);
      
      const prompt = this.prompts.get(promptName)!;

      logger.info(`Prompt ${promptName} generated successfully`);

      return {
        description: prompt.description,
        messages
      };
    } catch (error) {
      const errorMessage = `Prompt generation error: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMessage, { promptName, error });
      throw new Error(errorMessage);
    }
  }
}
```

#### 3.3 创建提示词基类和示例实现

**文件：`src/prompts/base.ts`**
```typescript
import { 
  Prompt, 
  PromptMessage, 
  PromptArgument,
  TextContent 
} from '@modelcontextprotocol/sdk/types.js';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { MessageRole, PromptTemplate } from './types.js';

export abstract class BasePrompt {
  constructor(protected settings: Settings) {}

  protected createMessage(role: MessageRole, content: string): PromptMessage {
    return {
      role,
      content: {
        type: 'text',
        text: content
      } as TextContent
    };
  }

  protected validateArguments(
    arguments_: Record<string, unknown>, 
    schema: Record<string, any>
  ): void {
    const required = schema.required || [];
    
    for (const reqArg of required) {
      if (!(reqArg in arguments_)) {
        throw new Error(`Missing required argument: ${reqArg}`);
      }
    }
  }

  abstract getPromptDefinition(): Prompt;
  abstract generateMessages(arguments: Record<string, unknown>): Promise<PromptMessage[]>;
}

// 示例提示词
export class ExamplePrompt extends BasePrompt {
  getPromptDefinition(): Prompt {
    return {
      name: 'example_prompt',
      description: 'An example prompt template for demonstration',
      arguments: [
        {
          name: 'topic',
          description: 'The topic to generate content about',
          required: true
        } as PromptArgument,
        {
          name: 'style',
          description: 'The writing style (formal, casual, technical)',
          required: false
        } as PromptArgument,
        {
          name: 'length',
          description: 'Response length (short, medium, long)',
          required: false
        } as PromptArgument
      ]
    };
  }

  async generateMessages(arguments_: Record<string, unknown>): Promise<PromptMessage[]> {
    this.validateArguments(arguments_, { required: ['topic'] });

    const topic = arguments_.topic as string;
    const style = (arguments_.style as string) || 'casual';
    const length = (arguments_.length as string) || 'medium';

    const lengthInstructions = {
      short: 'Keep your response brief and to the point (1-2 paragraphs).',
      medium: 'Provide a comprehensive response (3-5 paragraphs).',
      long: 'Give a detailed, thorough explanation (5+ paragraphs with examples).'
    };

    const systemMessage = this.createMessage(
      'system',
      `You are a ${style} writer. ${lengthInstructions[length as keyof typeof lengthInstructions] || lengthInstructions.medium} 
      Please provide helpful, accurate information about the given topic.`
    );

    const userMessage = this.createMessage(
      'user',
      `Please write about: ${topic}`
    );

    return [systemMessage, userMessage];
  }
}

// 代码生成提示词
export class CodeGenerationPrompt extends BasePrompt {
  getPromptDefinition(): Prompt {
    return {
      name: 'code_generation',
      description: 'Generate code in specified programming language',
      arguments: [
        {
          name: 'language',
          description: 'Programming language (javascript, python, typescript, etc.)',
          required: true
        } as PromptArgument,
        {
          name: 'task',
          description: 'Description of what the code should do',
          required: true
        } as PromptArgument,
        {
          name: 'framework',
          description: 'Framework or library to use (optional)',
          required: false
        } as PromptArgument,
        {
          name: 'style_guide',
          description: 'Code style guide to follow (optional)',
          required: false
        } as PromptArgument
      ]
    };
  }

  async generateMessages(arguments_: Record<string, unknown>): Promise<PromptMessage[]> {
    this.validateArguments(arguments_, { required: ['language', 'task'] });

    const language = arguments_.language as string;
    const task = arguments_.task as string;
    const framework = arguments_.framework as string | undefined;
    const styleGuide = arguments_.style_guide as string | undefined;

    let systemContent = `You are an expert ${language} programmer. Generate clean, efficient, and well-documented code.`;
    
    if (framework) {
      systemContent += ` Use the ${framework} framework.`;
    }
    
    if (styleGuide) {
      systemContent += ` Follow ${styleGuide} style guidelines.`;
    }

    systemContent += `\n\nRequirements:
- Write production-ready code
- Include proper error handling
- Add clear comments explaining the logic
- Follow best practices for ${language}
- Provide example usage if applicable`;

    const systemMessage = this.createMessage('system', systemContent);

    const userMessage = this.createMessage(
      'user',
      `Generate ${language} code for the following task: ${task}`
    );

    return [systemMessage, userMessage];
  }
}

// 数据分析提示词
export class DataAnalysisPrompt extends BasePrompt {
  getPromptDefinition(): Prompt {
    return {
      name: 'data_analysis',
      description: 'Analyze data and provide insights',
      arguments: [
        {
          name: 'data_description',
          description: 'Description of the data to analyze',
          required: true
        } as PromptArgument,
        {
          name: 'analysis_type',
          description: 'Type of analysis (descriptive, diagnostic, predictive, prescriptive)',
          required: true
        } as PromptArgument,
        {
          name: 'focus_areas',
          description: 'Specific areas to focus on (comma-separated)',
          required: false
        } as PromptArgument
      ]
    };
  }

  async generateMessages(arguments_: Record<string, unknown>): Promise<PromptMessage[]> {
    this.validateArguments(arguments_, { required: ['data_description', 'analysis_type'] });

    const dataDescription = arguments_.data_description as string;
    const analysisType = arguments_.analysis_type as string;
    const focusAreas = arguments_.focus_areas as string | undefined;

    const analysisInstructions = {
      descriptive: 'Focus on summarizing and describing the main characteristics of the data.',
      diagnostic: 'Identify patterns, trends, and relationships to explain why certain outcomes occurred.',
      predictive: 'Use the data to make forecasts and predictions about future outcomes.',
      prescriptive: 'Recommend specific actions based on the analysis findings.'
    };

    let systemContent = `You are a data analyst expert. Perform ${analysisType} analysis on the provided data.

${analysisInstructions[analysisType as keyof typeof analysisInstructions] || 'Provide comprehensive data analysis.'}

Structure your analysis with:
1. Data Summary
2. Key Findings
3. Insights and Patterns
4. Recommendations (if applicable)
5. Limitations and Assumptions`;

    if (focusAreas) {
      systemContent += `\n\nPay special attention to: ${focusAreas}`;
    }

    const systemMessage = this.createMessage('system', systemContent);

    const userMessage = this.createMessage(
      'user',
      `Please analyze this data: ${dataDescription}`
    );

    return [systemMessage, userMessage];
  }
}

// API文档生成提示词
export class ApiDocumentationPrompt extends BasePrompt {
  getPromptDefinition(): Prompt {
    return {
      name: 'api_documentation',
      description: 'Generate API documentation from code or specifications',
      arguments: [
        {
          name: 'api_info',
          description: 'API information (code, endpoints, or specification)',
          required: true
        } as PromptArgument,
        {
          name: 'format',
          description: 'Documentation format (markdown, openapi, postman)',
          required: false
        } as PromptArgument,
        {
          name: 'include_examples',
          description: 'Include request/response examples',
          required: false
        } as PromptArgument
      ]
    };
  }

  async generateMessages(arguments_: Record<string, unknown>): Promise<PromptMessage[]> {
    this.validateArguments(arguments_, { required: ['api_info'] });

    const apiInfo = arguments_.api_info as string;
    const format = (arguments_.format as string) || 'markdown';
    const includeExamples = arguments_.include_examples !== false;

    let systemContent = `You are a technical writer specializing in API documentation. Create comprehensive, clear, and user-friendly API documentation.

Generate documentation in ${format} format with:
1. API Overview
2. Authentication methods
3. Endpoint descriptions
4. Request/response schemas
5. Error codes and handling`;

    if (includeExamples) {
      systemContent += `\n6. Request and response examples
7. Code samples in multiple languages`;
    }

    systemContent += `\n\nEnsure the documentation is:
- Clear and easy to understand
- Complete with all necessary details
- Well-structured and organized
- Include practical examples
- Follow industry best practices`;

    const systemMessage = this.createMessage('system', systemContent);

    const userMessage = this.createMessage(
      'user',
      `Generate API documentation for: ${apiInfo}`
    );

    return [systemMessage, userMessage];
  }
}
```

#### 3.4 更新提示词管理器注册逻辑

**更新 `src/prompts/index.ts`**
```typescript
// 在 registerPrompts 方法中添加：
private registerPrompts(): void {
  logger.info('Registering prompts...');
  
  // 注册示例提示词
  const examplePrompt = new ExamplePrompt(this.settings);
  this.registerPrompt({
    prompt: examplePrompt.getPromptDefinition(),
    generator: examplePrompt.generateMessages.bind(examplePrompt)
  });

  // 注册代码生成提示词
  const codePrompt = new CodeGenerationPrompt(this.settings);
  this.registerPrompt({
    prompt: codePrompt.getPromptDefinition(),
    generator: codePrompt.generateMessages.bind(codePrompt)
  });

  // 注册数据分析提示词
  const dataPrompt = new DataAnalysisPrompt(this.settings);
  this.registerPrompt({
    prompt: dataPrompt.getPromptDefinition(),
    generator: dataPrompt.generateMessages.bind(dataPrompt)
  });

  // 注册API文档提示词
  const apiDocPrompt = new ApiDocumentationPrompt(this.settings);
  this.registerPrompt({
    prompt: apiDocPrompt.getPromptDefinition(),
    generator: apiDocPrompt.generateMessages.bind(apiDocPrompt)
  });

  logger.info(`Registered ${this.prompts.size} prompts`);
}

// 添加导入语句
import { 
  ExamplePrompt, 
  CodeGenerationPrompt, 
  DataAnalysisPrompt,
  ApiDocumentationPrompt 
} from './base.js';
```

### 验证标准
- [ ] 提示词管理器可以正确注册和列出提示词
- [ ] 基础提示词类提供完整的消息生成框架
- [ ] 示例提示词可以正常生成消息且类型安全

### 输出结果
```
提示词管理系统包含：
1. 类型定义 (src/prompts/types.ts)
2. 提示词管理器类 (src/prompts/index.ts)
3. 基础提示词类 (src/prompts/base.ts)
4. 多种实用提示词实现
5. 参数验证和错误处理
```

---

## 第4步：资源(Resources)管理器实现

### 输入要求
- 完成的提示词管理器
- 资源访问需求清单

### 执行步骤

#### 4.1 创建资源类型定义

**文件：`src/resources/types.ts`**
```typescript
import { 
  Resource, 
  ResourceContents, 
  TextResourceContents,
  BlobResourceContents,
  ReadResourceRequest,
  ReadResourceResult 
} from '@modelcontextprotocol/sdk/types.js';

// 资源读取器类型
export type ResourceReader = () => Promise<ResourceContents[]>;

// 资源注册接口
export interface ResourceRegistration {
  resource: Resource;
  reader: ResourceReader;
}

// 资源内容类型
export type ResourceContent = TextResourceContents | BlobResourceContents;

// 资源元数据
export interface ResourceMetadata {
  lastModified?: string;
  size?: number;
  encoding?: string;
  checksum?: string;
}
```

#### 4.2 创建资源管理器

**文件：`src/resources/index.ts`**
```typescript
import { 
  Resource, 
  ResourceContents,
  ReadResourceRequest, 
  ReadResourceResult 
} from '@modelcontextprotocol/sdk/types.js';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { ResourceReader, ResourceRegistration } from './types.js';

export class ResourceManager {
  private resources: Map<string, Resource> = new Map();
  private readers: Map<string, ResourceReader> = new Map();

  constructor(private settings: Settings) {
    this.registerResources();
  }

  private registerResources(): void {
    logger.info('Registering resources...');
    // TODO: 添加资源注册逻辑
  }

  registerResource(registration: ResourceRegistration): void {
    const { resource, reader } = registration;
    
    this.resources.set(resource.uri, resource);
    this.readers.set(resource.uri, reader);
    
    logger.debug(`Registered resource: ${resource.uri}`);
  }

  listResources(): Resource[] {
    return Array.from(this.resources.values());
  }

  async readResource(request: ReadResourceRequest): Promise<ReadResourceResult> {
    const uri = request.uri;

    logger.info(`Reading resource: ${uri}`);

    if (!this.readers.has(uri)) {
      throw new Error(`Unknown resource: ${uri}`);
    }

    try {
      const reader = this.readers.get(uri)!;
      const contents = await reader();

      logger.info(`Resource ${uri} read successfully`);

      return { contents };
    } catch (error) {
      const errorMessage = `Resource read error: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMessage, { uri, error });
      throw new Error(errorMessage);
    }
  }
}
```

#### 4.3 创建资源基类和实现

**文件：`src/resources/base.ts`**
```typescript
import fs from 'fs/promises';
import path from 'path';
import { 
  Resource, 
  TextResourceContents, 
  BlobResourceContents,
  ResourceContents 
} from '@modelcontextprotocol/sdk/types.js';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { ResourceContent, ResourceMetadata } from './types.js';

export abstract class BaseResource {
  constructor(protected settings: Settings) {}

  protected createTextContent(uri: string, text: string, mimeType = 'text/plain'): TextResourceContents {
    return {
      uri,
      mimeType,
      text
    };
  }

  protected createBlobContent(uri: string, blob: string, mimeType = 'application/octet-stream'): BlobResourceContents {
    return {
      uri,
      mimeType,
      blob
    };
  }

  protected async readFile(filePath: string): Promise<string> {
    try {
      return await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  protected async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  protected async getFileStats(filePath: string): Promise<ResourceMetadata> {
    try {
      const stats = await fs.stat(filePath);
      return {
        lastModified: stats.mtime.toISOString(),
        size: stats.size
      };
    } catch (error) {
      logger.warn(`Failed to get file stats for ${filePath}:`, error);
      return {};
    }
  }

  abstract getResourceDefinition(): Resource;
  abstract readContent(): Promise<ResourceContents[]>;
}

// 配置资源
export class ConfigResource extends BaseResource {
  getResourceDefinition(): Resource {
    return {
      uri: 'config://settings',
      name: 'Application Settings',
      description: 'Current application configuration and environment variables',
      mimeType: 'application/json'
    };
  }

  async readContent(): Promise<ResourceContents[]> {
    const config = {
      appName: this.settings.appName,
      apiBaseUrl: this.settings.apiBaseUrl,
      apiTimeout: this.settings.apiTimeout,
      cacheEnabled: this.settings.cacheEnabled,
      cacheTtl: this.settings.cacheTtl,
      logLevel: this.settings.logLevel,
      debug: this.settings.debug,
      // 不包含敏感信息如API密钥
      hasApiKey: !!this.settings.apiKey
    };

    return [this.createTextContent(
      'config://settings',
      JSON.stringify(config, null, 2),
      'application/json'
    )];
  }
}

// 文件系统资源
export class FileSystemResource extends BaseResource {
  constructor(
    settings: Settings,
    private basePath: string,
    private allowedExtensions: string[] = ['.txt', '.md', '.json', '.yaml', '.yml']
  ) {
    super(settings);
  }

  getResourceDefinition(): Resource {
    return {
      uri: `file://${this.basePath}`,
      name: 'File System Access',
      description: `Access to files in ${this.basePath}`,
      mimeType: 'text/plain'
    };
  }

  async readContent(): Promise<ResourceContents[]> {
    try {
      const files = await this.listFiles(this.basePath);
      const contents: ResourceContents[] = [];

      for (const file of files) {
        const ext = path.extname(file);
        if (this.allowedExtensions.includes(ext)) {
          const content = await this.readFile(file);
          const relativePath = path.relative(this.basePath, file);
          
          contents.push(this.createTextContent(
            `file://${relativePath}`,
            content,
            this.getMimeType(ext)
          ));
        }
      }

      return contents;
    } catch (error) {
      throw new Error(`Failed to read directory ${this.basePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async listFiles(dir: string): Promise<string[]> {
    const files: string[] = [];
    const entries = await fs.readdir(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        // 递归读取子目录
        const subFiles = await this.listFiles(fullPath);
        files.push(...subFiles);
      } else if (entry.isFile()) {
        files.push(fullPath);
      }
    }

    return files;
  }

  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.json': 'application/json',
      '.yaml': 'application/yaml',
      '.yml': 'application/yaml',
      '.js': 'application/javascript',
      '.ts': 'application/typescript',
      '.py': 'text/x-python',
      '.html': 'text/html',
      '.css': 'text/css'
    };

    return mimeTypes[extension] || 'text/plain';
  }
}

// API数据资源
export class ApiDataResource extends BaseResource {
  constructor(
    settings: Settings,
    private dataType: string,
    private endpoint: string
  ) {
    super(settings);
  }

  getResourceDefinition(): Resource {
    return {
      uri: `api://data/${this.dataType}`,
      name: `${this.dataType} Data`,
      description: `Real-time ${this.dataType} data from API`,
      mimeType: 'application/json'
    };
  }

  async readContent(): Promise<ResourceContents[]> {
    try {
      // 这里应该实现实际的API调用
      const data = await this.fetchApiData();
      
      return [this.createTextContent(
        `api://data/${this.dataType}`,
        JSON.stringify(data, null, 2),
        'application/json'
      )];
    } catch (error) {
      throw new Error(`Failed to fetch ${this.dataType} data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async fetchApiData(): Promise<unknown> {
    // 模拟API数据获取
    // 在实际实现中，这里应该使用 axios 或其他 HTTP 客户端
    return {
      type: this.dataType,
      timestamp: new Date().toISOString(),
      endpoint: this.endpoint,
      data: [
        { id: 1, name: `Sample ${this.dataType} 1`, status: 'active' },
        { id: 2, name: `Sample ${this.dataType} 2`, status: 'inactive' }
      ],
      metadata: {
        total: 2,
        page: 1,
        pageSize: 10
      }
    };
  }
}

// 系统信息资源
export class SystemInfoResource extends BaseResource {
  getResourceDefinition(): Resource {
    return {
      uri: 'system://info',
      name: 'System Information',
      description: 'Current system and runtime information',
      mimeType: 'application/json'
    };
  }

  async readContent(): Promise<ResourceContents[]> {
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      architecture: process.arch,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      },
      uptime: Math.round(process.uptime()),
      pid: process.pid,
      cwd: process.cwd(),
      timestamp: new Date().toISOString()
    };

    return [this.createTextContent(
      'system://info',
      JSON.stringify(systemInfo, null, 2),
      'application/json'
    )];
  }
}

// 日志资源
export class LogResource extends BaseResource {
  constructor(
    settings: Settings,
    private logFile: string = 'mcp_server.log'
  ) {
    super(settings);
  }

  getResourceDefinition(): Resource {
    return {
      uri: `logs://${this.logFile}`,
      name: 'Application Logs',
      description: `Recent application logs from ${this.logFile}`,
      mimeType: 'text/plain'
    };
  }

  async readContent(): Promise<ResourceContents[]> {
    try {
      if (!(await this.fileExists(this.logFile))) {
        return [this.createTextContent(
          `logs://${this.logFile}`,
          'No log file found',
          'text/plain'
        )];
      }

      const logContent = await this.readFile(this.logFile);
      const lines = logContent.split('\n');
      
      // 只返回最近1000行日志
      const recentLines = lines.slice(-1000);
      
      return [this.createTextContent(
        `logs://${this.logFile}`,
        recentLines.join('\n'),
        'text/plain'
      )];
    } catch (error) {
      throw new Error(`Failed to read log file: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
```

#### 4.4 更新资源管理器注册逻辑

**更新 `src/resources/index.ts`**
```typescript
// 在 registerResources 方法中添加：
private registerResources(): void {
  logger.info('Registering resources...');
  
  // 注册配置资源
  const configResource = new ConfigResource(this.settings);
  this.registerResource({
    resource: configResource.getResourceDefinition(),
    reader: configResource.readContent.bind(configResource)
  });

  // 注册系统信息资源
  const systemResource = new SystemInfoResource(this.settings);
  this.registerResource({
    resource: systemResource.getResourceDefinition(),
    reader: systemResource.readContent.bind(systemResource)
  });

  // 注册日志资源
  const logResource = new LogResource(this.settings);
  this.registerResource({
    resource: logResource.getResourceDefinition(),
    reader: logResource.readContent.bind(logResource)
  });

  // 注册API数据资源
  const dataTypes = ['users', 'projects', 'reports'];
  for (const dataType of dataTypes) {
    const apiResource = new ApiDataResource(this.settings, dataType, `/api/${dataType}`);
    this.registerResource({
      resource: apiResource.getResourceDefinition(),
      reader: apiResource.readContent.bind(apiResource)
    });
  }

  // 注册文件系统资源（如果需要）
  if (this.settings.debug) {
    const fsResource = new FileSystemResource(this.settings, './docs');
    this.registerResource({
      resource: fsResource.getResourceDefinition(),
      reader: fsResource.readContent.bind(fsResource)
    });
  }

  logger.info(`Registered ${this.resources.size} resources`);
}

// 添加导入语句
import { 
  ConfigResource, 
  SystemInfoResource, 
  LogResource,
  ApiDataResource,
  FileSystemResource 
} from './base.js';
```

### 验证标准
- [ ] 资源管理器可以正确注册和列出资源
- [ ] 基础资源类提供完整的内容读取框架
- [ ] 示例资源可以正常返回内容且类型安全

### 输出结果
```
资源管理系统包含：
1. 类型定义 (src/resources/types.ts)
2. 资源管理器类 (src/resources/index.ts)
3. 基础资源类 (src/resources/base.ts)
4. 多种实用资源实现
5. 文件系统和API数据访问
```

---

## 第5步：错误处理和工具系统

### 输入要求
- 完成的资源管理器
- 错误处理需求

### 执行步骤

#### 5.1 创建通用工具模块

**文件：`src/utils/common.ts`**
```typescript
import { createLogger, format, transports, Logger } from 'winston';
import { CallToolResult, TextContent } from '@modelcontextprotocol/sdk/types.js';

// 全局日志器
export let logger: Logger;

export function setupLogging(level: string = 'info'): void {
  logger = createLogger({
    level,
    format: format.combine(
      format.timestamp(),
      format.errors({ stack: true }),
      format.json()
    ),
    defaultMeta: { service: 'mcp-server' },
    transports: [
      new transports.File({ filename: 'error.log', level: 'error' }),
      new transports.File({ filename: 'combined.log' }),
      new transports.Console({
        format: format.combine(
          format.colorize(),
          format.simple()
        )
      })
    ]
  });

  // 处理未捕获的异常
  logger.exceptions.handle(
    new transports.File({ filename: 'exceptions.log' })
  );

  logger.info('Logging system initialized');
}

// 自定义错误类型
export class MCPError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'MCPError';
  }
}

export class ValidationError extends MCPError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends MCPError {
  constructor(message: string) {
    super(message, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class RateLimitError extends MCPError {
  constructor(message: string) {
    super(message, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class ResourceNotFoundError extends MCPError {
  constructor(message: string) {
    super(message, 'RESOURCE_NOT_FOUND');
    this.name = 'ResourceNotFoundError';
  }
}

// 错误处理装饰器
export function handleErrors<T extends (...args: any[]) => Promise<any>>(
  target: any,
  propertyName: string,
  descriptor: TypedPropertyDescriptor<T>
): TypedPropertyDescriptor<T> {
  const method = descriptor.value!;

  descriptor.value = (async function (this: any, ...args: any[]): Promise<any> {
    try {
      return await method.apply(this, args);
    } catch (error) {
      logger.error(`Error in ${propertyName}:`, error);
      
      if (error instanceof MCPError) {
        throw error;
      }
      
      throw new MCPError(`Internal error in ${propertyName}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }) as T;

  return descriptor;
}

// 参数验证装饰器
export function validateSchema(schema: any) {
  return function <T extends (...args: any[]) => any>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ): TypedPropertyDescriptor<T> {
    const method = descriptor.value!;
    
    descriptor.value = (function (this: any, arguments_: any, ...args: any[]): any {
      try {
        // 简化的验证逻辑
        const required = schema.required || [];
        const properties = schema.properties || {};
        
        for (const reqParam of required) {
          if (!(reqParam in arguments_)) {
            throw new ValidationError(`Missing required parameter: ${reqParam}`);
          }
        }
        
        // 类型检查
        for (const [param, value] of Object.entries(arguments_)) {
          if (param in properties) {
            const expectedType = properties[param].type;
            const actualType = typeof value;
            
            if (expectedType === 'string' && actualType !== 'string') {
              throw new ValidationError(`Parameter ${param} must be a string`);
            } else if (expectedType === 'number' && actualType !== 'number') {
              throw new ValidationError(`Parameter ${param} must be a number`);
            } else if (expectedType === 'boolean' && actualType !== 'boolean') {
              throw new ValidationError(`Parameter ${param} must be a boolean`);
            }
          }
        }
        
        return method.apply(this, [arguments_, ...args]);
      } catch (error) {
        if (error instanceof ValidationError) {
          throw error;
        }
        throw new ValidationError(`Validation failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    }) as T;
    
    return descriptor;
  };
}

// 重试装饰器
export function retry(attempts: number = 3, delay: number = 1000) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ): TypedPropertyDescriptor<T> {
    const method = descriptor.value!;
    
    descriptor.value = (async function (this: any, ...args: any[]): Promise<any> {
      let lastError: Error;
      
      for (let i = 0; i < attempts; i++) {
        try {
          return await method.apply(this, args);
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));
          
          if (i === attempts - 1) {
            throw lastError;
          }
          
          logger.warn(`Attempt ${i + 1} failed, retrying in ${delay}ms:`, error);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
      
      throw lastError!;
    }) as T;
    
    return descriptor;
  };
}

// 缓存装饰器
const cache = new Map<string, { value: any; expiry: number }>();

export function cached(ttl: number = 300000) { // 5分钟默认TTL
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ): TypedPropertyDescriptor<T> {
    const method = descriptor.value!;
    
    descriptor.value = (async function (this: any, ...args: any[]): Promise<any> {
      const cacheKey = `${target.constructor.name}.${propertyName}.${JSON.stringify(args)}`;
      const now = Date.now();
      
      // 检查缓存
      if (cache.has(cacheKey)) {
        const cached = cache.get(cacheKey)!;
        if (cached.expiry > now) {
          logger.debug(`Cache hit for ${cacheKey}`);
          return cached.value;
        } else {
          cache.delete(cacheKey);
        }
      }
      
      // 执行方法并缓存结果
      const result = await method.apply(this, args);
      cache.set(cacheKey, {
        value: result,
        expiry: now + ttl
      });
      
      logger.debug(`Cached result for ${cacheKey}`);
      return result;
    }) as T;
    
    return descriptor;
  };
}

// 工具函数
export function createErrorResult(message: string): CallToolResult {
  return {
    content: [{
      type: 'text',
      text: message
    } as TextContent],
    isError: true
  };
}

export function createSuccessResult(data: any): CallToolResult {
  return {
    content: [{
      type: 'text',
      text: typeof data === 'string' ? data : JSON.stringify(data, null, 2)
    } as TextContent]
  };
}

export function sanitizeInput(input: string): string {
  // 基本的输入清理
  return input
    .replace(/[<>]/g, '') // 移除潜在的HTML标签
    .replace(/javascript:/gi, '') // 移除JavaScript协议
    .trim();
}

export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 速率限制器
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(
    private maxRequests: number = 100, 
    private windowMs: number = 60000 // 1分钟
  ) {}
  
  isAllowed(key: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    // 清理过期的请求记录
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }
}

export const rateLimiter = new RateLimiter();
```

#### 5.2 更新服务器类集成错误处理

**更新 `src/server.ts`**
```typescript
// 在导入部分添加：
import { MCPError, logger, createErrorResult } from './utils/common.js';

// 更新处理程序以包含错误处理：
private setupHandlers(): void {
  // 工具处理程序
  this.server.setRequestHandler(ListToolsRequestSchema, async () => {
    try {
      return {
        tools: this.toolManager.listTools()
      } as ListToolsResult;
    } catch (error) {
      logger.error('Error listing tools:', error);
      throw error;
    }
  });

  this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
    try {
      return await this.toolManager.callTool(request);
    } catch (error) {
      logger.error('Error calling tool:', error);
      
      if (error instanceof MCPError) {
        return createErrorResult(error.message);
      }
      
      return createErrorResult('Internal server error');
    }
  });

  // 提示词处理程序
  this.server.setRequestHandler(ListPromptsRequestSchema, async () => {
    try {
      return {
        prompts: this.promptManager.listPrompts()
      } as ListPromptsResult;
    } catch (error) {
      logger.error('Error listing prompts:', error);
      throw error;
    }
  });

  this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
    try {
      return await this.promptManager.getPrompt(request);
    } catch (error) {
      logger.error('Error getting prompt:', error);
      throw error;
    }
  });

  // 资源处理程序
  this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
    try {
      return {
        resources: this.resourceManager.listResources()
      } as ListResourcesResult;
    } catch (error) {
      logger.error('Error listing resources:', error);
      throw error;
    }
  });

  this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
    try {
      return await this.resourceManager.readResource(request);
    } catch (error) {
      logger.error('Error reading resource:', error);
      throw error;
    }
  });
}
```

### 验证标准
- [ ] 日志系统正确配置并输出到文件和控制台
- [ ] 错误处理装饰器可以捕获和格式化异常
- [ ] 工具函数提供完整的验证和清理功能

### 输出结果
```
错误处理和工具系统包含：
1. 通用工具模块 (src/utils/common.ts)
2. 结构化的异常类型
3. 装饰器支持的错误处理
4. 缓存、重试、验证等功能
5. 完整的日志配置
```

---

## 第6步：测试和验证

### 输入要求
- 完整的MCP Server实现
- 测试用例需求

### 执行步骤

#### 6.1 安装测试依赖并创建配置

**更新 `package.json`**
```json
{
  "devDependencies": {
    "@types/jest": "^29.5.0",
    "@types/node": "^20.0.0",
    "jest": "^29.5.0",
    "ts-jest": "^29.1.0",
    "typescript": "^5.0.0",
    "tsx": "^4.0.0"
  },
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  }
}
```

**文件：`jest.config.js`**
```javascript
export default {
  preset: 'ts-jest',
  testEnvironment: 'node',
  extensionsToTreatAsEsm: ['.ts'],
  moduleNameMapping: {
    '^(\\.{1,2}/.*)\\.js$': '$1'
  },
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      useESM: true
    }]
  },
  testMatch: [
    '**/tests/**/*.test.ts',
    '**/tests/**/*.spec.ts'
  ],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/index.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts']
};
```

#### 6.2 创建测试基础设施

**文件：`tests/setup.ts`**
```typescript
import { setupLogging } from '../src/utils/common.js';

// 设置测试环境
process.env.NODE_ENV = 'test';
process.env.API_BASE_URL = 'https://api.test.com';
process.env.API_KEY = 'test-key';
process.env.LOG_LEVEL = 'error'; // 测试时减少日志输出

// 初始化日志系统
setupLogging('error');

// 全局测试超时
jest.setTimeout(10000);
```

**文件：`tests/helpers.ts`**
```typescript
import { Settings } from '../src/config/settings.js';
import { MCPServer } from '../src/server.js';

export function createTestSettings(overrides: Partial<any> = {}): Settings {
  // 设置测试环境变量
  const testEnv = {
    APP_NAME: 'test-mcp-server',
    API_BASE_URL: 'https://api.test.com',
    API_KEY: 'test-key',
    DEBUG: 'true',
    CACHE_ENABLED: 'false',
    LOG_LEVEL: 'error',
    ...overrides
  };

  // 临时设置环境变量
  const originalEnv = { ...process.env };
  Object.assign(process.env, testEnv);

  const settings = new Settings();

  // 恢复原始环境变量
  process.env = originalEnv;

  return settings;
}

export function createTestServer(settings?: Settings): MCPServer {
  const testSettings = settings || createTestSettings();
  return new MCPServer();
}

export function mockApiResponse(data: any, status: number = 200) {
  return {
    data,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    headers: {},
    config: {}
  };
}
```

#### 6.3 创建单元测试

**文件：`tests/config.test.ts`**
```typescript
import { Settings } from '../src/config/settings.js';

describe('Settings', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  test('should load settings from environment variables', () => {
    process.env.APP_NAME = 'test-app';
    process.env.API_BASE_URL = 'https://api.example.com';
    process.env.API_KEY = 'test-key';
    process.env.DEBUG = 'true';

    const settings = new Settings();

    expect(settings.appName).toBe('test-app');
    expect(settings.apiBaseUrl).toBe('https://api.example.com');
    expect(settings.apiKey).toBe('test-key');
    expect(settings.debug).toBe(true);
  });

  test('should use default values when environment variables are not set', () => {
    process.env.API_BASE_URL = 'https://api.example.com';
    delete process.env.APP_NAME;
    delete process.env.DEBUG;

    const settings = new Settings();

    expect(settings.appName).toBe('{project_name}');
    expect(settings.debug).toBe(false);
  });

  test('should throw error for invalid configuration', () => {
    delete process.env.API_BASE_URL;

    expect(() => new Settings()).toThrow('Invalid configuration');
  });

  test('should generate correct API headers', () => {
    process.env.API_BASE_URL = 'https://api.example.com';
    process.env.API_KEY = 'test-key';

    const settings = new Settings();
    const headers = settings.getApiHeaders();

    expect(headers['Content-Type']).toBe('application/json');
    expect(headers['Authorization']).toBe('Bearer test-key');
  });

  test('should not include Authorization header when API key is not set', () => {
    process.env.API_BASE_URL = 'https://api.example.com';
    delete process.env.API_KEY;

    const settings = new Settings();
    const headers = settings.getApiHeaders();

    expect(headers['Content-Type']).toBe('application/json');
    expect(headers['Authorization']).toBeUndefined();
  });
});
```

**文件：`tests/tools.test.ts`**
```typescript
import { ToolManager } from '../src/tools/index.js';
import { ExampleTool } from '../src/tools/base.js';
import { createTestSettings } from './helpers.js';

describe('ToolManager', () => {
  let toolManager: ToolManager;
  let settings: ReturnType<typeof createTestSettings>;

  beforeEach(() => {
    settings = createTestSettings();
    toolManager = new ToolManager(settings);
  });

  test('should initialize with empty tools', () => {
    const tools = toolManager.listTools();
    expect(Array.isArray(tools)).toBe(true);
  });

  test('should register and list tools', () => {
    const exampleTool = new ExampleTool(settings);
    toolManager.registerTool({
      tool: exampleTool.getToolDefinition(),
      handler: exampleTool.execute.bind(exampleTool)
    });

    const tools = toolManager.listTools();
    expect(tools).toHaveLength(1);
    expect(tools[0].name).toBe('example_tool');
  });

  test('should handle unknown tool call', async () => {
    const request = {
      params: {
        name: 'unknown_tool',
        arguments: {}
      }
    };

    const result = await toolManager.callTool(request as any);
    expect(result.isError).toBe(true);
    expect(result.content[0].text).toContain('Unknown tool');
  });

  test('should execute tool successfully', async () => {
    const exampleTool = new ExampleTool(settings);
    toolManager.registerTool({
      tool: exampleTool.getToolDefinition(),
      handler: async (args) => ({ success: true, data: args })
    });

    const request = {
      params: {
        name: 'example_tool',
        arguments: { param1: 'test' }
      }
    };

    const result = await toolManager.callTool(request as any);
    expect(result.isError).toBeFalsy();
    expect(result.content[0].text).toContain('success');
  });

  test('should handle tool execution errors', async () => {
    const exampleTool = new ExampleTool(settings);
    toolManager.registerTool({
      tool: exampleTool.getToolDefinition(),
      handler: async () => { throw new Error('Test error'); }
    });

    const request = {
      params: {
        name: 'example_tool',
        arguments: { param1: 'test' }
      }
    };

    const result = await toolManager.callTool(request as any);
    expect(result.isError).toBe(true);
    expect(result.content[0].text).toContain('Test error');
  });
});

describe('ExampleTool', () => {
  let tool: ExampleTool;
  let settings: ReturnType<typeof createTestSettings>;

  beforeEach(() => {
    settings = createTestSettings();
    tool = new ExampleTool(settings);
  });

  test('should have correct tool definition', () => {
    const definition = tool.getToolDefinition();
    
    expect(definition.name).toBe('example_tool');
    expect(definition.description).toBeDefined();
    expect(definition.inputSchema).toBeDefined();
    expect(definition.inputSchema.properties).toHaveProperty('param1');
  });

  test('should validate required parameters', async () => {
    await expect(tool.execute({})).rejects.toThrow('param1 must be a string');
  });

  test('should validate parameter types', async () => {
    await expect(tool.execute({ param1: 123 })).rejects.toThrow('param1 must be a string');
  });

  test('should execute with valid parameters', async () => {
    // Mock HTTP client
    jest.spyOn(tool as any, 'makeRequest').mockResolvedValue({ result: 'success' });

    const result = await tool.execute({ param1: 'test', param2: 5 });
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
  });
});
```

#### 6.4 创建集成测试

**文件：`tests/integration.test.ts`**
```typescript
import { MCPServer } from '../src/server.js';
import { createTestSettings } from './helpers.js';

describe('MCP Server Integration', () => {
  let server: MCPServer;

  beforeEach(() => {
    server = new MCPServer();
  });

  test('should initialize server with all components', () => {
    expect(server).toBeDefined();
    // 测试服务器的私有属性需要类型断言或反射
  });

  test('should handle server lifecycle', async () => {
    // 这里测试服务器启动和关闭
    // 由于MCP服务器使用stdio，实际测试可能需要mock
    expect(server).toBeDefined();
  });
});

describe('End-to-End Workflows', () => {
  let server: MCPServer;

  beforeEach(() => {
    server = new MCPServer();
  });

  test('should complete full tool workflow', async () => {
    // 1. 列出工具
    // 2. 调用工具
    // 3. 验证结果
    // 由于需要模拟MCP协议通信，这里需要更复杂的设置
  });

  test('should complete full prompt workflow', async () => {
    // 1. 列出提示词
    // 2. 获取提示词
    // 3. 验证消息
  });

  test('should complete full resource workflow', async () => {
    // 1. 列出资源
    // 2. 读取资源
    // 3. 验证内容
  });
});
```

#### 6.5 创建性能测试

**文件：`tests/performance.test.ts`**
```typescript
import { ToolManager } from '../src/tools/index.js';
import { PromptManager } from '../src/prompts/index.js';
import { ResourceManager } from '../src/resources/index.js';
import { createTestSettings } from './helpers.js';

describe('Performance Tests', () => {
  const settings = createTestSettings();

  test('tool manager should handle concurrent requests', async () => {
    const toolManager = new ToolManager(settings);
    
    const startTime = Date.now();
    const promises = Array(10).fill(0).map(() => 
      toolManager.listTools()
    );
    
    const results = await Promise.all(promises);
    const endTime = Date.now();
    
    expect(results).toHaveLength(10);
    expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
  });

  test('prompt manager should generate prompts efficiently', async () => {
    const promptManager = new PromptManager(settings);
    
    const startTime = Date.now();
    const prompts = promptManager.listPrompts();
    const endTime = Date.now();
    
    expect(prompts).toBeDefined();
    expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
  });

  test('resource manager should handle multiple resource reads', async () => {
    const resourceManager = new ResourceManager(settings);
    
    const resources = resourceManager.listResources();
    if (resources.length > 0) {
      const startTime = Date.now();
      const promises = resources.slice(0, 5).map(resource => 
        resourceManager.readResource({ uri: resource.uri })
      );
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      expect(results).toHaveLength(Math.min(5, resources.length));
      expect(endTime - startTime).toBeLessThan(5000); // 应该在5秒内完成
    }
  });

  test('memory usage should remain stable', async () => {
    const initialMemory = process.memoryUsage().heapUsed;
    
    // 执行一些操作
    const toolManager = new ToolManager(settings);
    const promptManager = new PromptManager(settings);
    const resourceManager = new ResourceManager(settings);
    
    for (let i = 0; i < 100; i++) {
      toolManager.listTools();
      promptManager.listPrompts();
      resourceManager.listResources();
    }
    
    // 强制垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }
    
    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;
    
    // 内存增长应该小于10MB
    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
  });
});
```

### 验证标准
- [ ] 所有测试用例通过
- [ ] 测试覆盖率达到80%以上
- [ ] 性能测试满足预期标准

### 输出结果
```
测试系统包含：
1. 测试配置 (jest.config.js, tests/setup.ts)
2. 测试助手 (tests/helpers.ts)
3. 单元测试 (tests/*.test.ts)
4. 集成测试 (tests/integration.test.ts)
5. 性能测试 (tests/performance.test.ts)
```

---

## 第7步：构建和部署

### 输入要求
- 通过测试的MCP Server
- 部署环境要求

### 执行步骤

#### 7.1 创建构建脚本

**更新 `package.json`**
```json
{
  "scripts": {
    "build": "tsc",
    "build:watch": "tsc --watch",
    "start": "node dist/index.js",
    "dev": "tsx src/index.ts",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix",
    "clean": "rimraf dist",
    "prepare": "npm run build",
    "deploy": "node scripts/deploy.js"
  }
}
```

**文件：`scripts/deploy.js`**
```javascript
#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

const REQUIRED_FILES = [
  'package.json',
  'tsconfig.json',
  'src/index.ts',
  '.env.example'
];

const REQUIRED_SCRIPTS = [
  'build',
  'start',
  'test'
];

async function checkRequirements() {
  console.log('🔍 Checking deployment requirements...');
  
  // 检查Node.js版本
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion < 18) {
    throw new Error(`Node.js 18+ required, got ${nodeVersion}`);
  }
  
  // 检查必需文件
  for (const file of REQUIRED_FILES) {
    try {
      await fs.access(file);
    } catch {
      throw new Error(`Required file missing: ${file}`);
    }
  }
  
  // 检查package.json脚本
  const packageJson = JSON.parse(await fs.readFile('package.json', 'utf-8'));
  for (const script of REQUIRED_SCRIPTS) {
    if (!packageJson.scripts?.[script]) {
      throw new Error(`Required script missing: ${script}`);
    }
  }
  
  console.log('✅ Requirements check passed');
}

async function installDependencies() {
  console.log('📦 Installing dependencies...');
  
  try {
    execSync('npm ci', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
  } catch (error) {
    throw new Error(`Failed to install dependencies: ${error.message}`);
  }
}

async function runTests() {
  console.log('🧪 Running tests...');
  
  try {
    execSync('npm test', { stdio: 'inherit' });
    console.log('✅ All tests passed');
  } catch (error) {
    throw new Error('Tests failed');
  }
}

async function buildProject() {
  console.log('🔨 Building project...');
  
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build completed');
  } catch (error) {
    throw new Error(`Build failed: ${error.message}`);
  }
}

async function setupEnvironment() {
  console.log('⚙️  Setting up environment...');
  
  try {
    await fs.access('.env');
    console.log('✅ .env file exists');
  } catch {
    try {
      await fs.copyFile('.env.example', '.env');
      console.log('✅ .env file created from template');
      console.log('⚠️  Please edit .env file with your configuration');
    } catch {
      console.log('⚠️  .env.example not found, please create .env manually');
    }
  }
}

async function verifyDeployment() {
  console.log('🚀 Verifying deployment...');
  
  try {
    // 检查构建输出
    await fs.access('dist/index.js');
    console.log('✅ Build output verified');
    
    // 验证可以启动（简单检查）
    execSync('node dist/index.js --help || true', { stdio: 'pipe' });
    console.log('✅ Application can start');
    
  } catch (error) {
    throw new Error(`Deployment verification failed: ${error.message}`);
  }
}

async function main() {
  try {
    console.log('🚀 Starting deployment process...\n');
    
    await checkRequirements();
    await installDependencies();
    await runTests();
    await buildProject();
    await setupEnvironment();
    await verifyDeployment();
    
    console.log('\n🎉 Deployment completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Edit .env file with your configuration');
    console.log('2. Run: npm start');
    console.log('3. Test with MCP client');
    
  } catch (error) {
    console.error(`\n❌ Deployment failed: ${error.message}`);
    process.exit(1);
  }
}

main();
```

#### 7.2 创建Docker支持

**文件：`Dockerfile`**
```dockerfile
# 多阶段构建
FROM node:20-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY tsconfig.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/

# 构建应用
RUN npm run build

# 生产阶段
FROM node:20-alpine AS production

# 安装dumb-init用于信号处理
RUN apk add --no-cache dumb-init

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# 复制构建结果和依赖
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules/
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist/
COPY --from=builder --chown=nextjs:nodejs /app/package*.json ./

# 创建日志目录
RUN mkdir -p /app/logs && chown nextjs:nodejs /app/logs

USER nextjs

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "console.log('Health check passed')" || exit 1

# 暴露端口（如果需要HTTP接口）
EXPOSE 3000

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/index.js"]
```

**文件：`docker-compose.yml`**
```yaml
version: '3.8'

services:
  mcp-server:
    build: 
      context: .
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - API_BASE_URL=${API_BASE_URL}
      - API_KEY=${API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - DEBUG=${DEBUG:-false}
      - CACHE_ENABLED=${CACHE_ENABLED:-true}
      - CACHE_TTL=${CACHE_TTL:-300}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    restart: unless-stopped
    networks:
      - mcp-network
    
  # 可选：Redis缓存
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    ports:
      - "127.0.0.1:6379:6379"
    networks:
      - mcp-network

  # 可选：监控
  prometheus:
    image: prom/prometheus:latest
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "127.0.0.1:9090:9090"
    networks:
      - mcp-network

volumes:
  redis_data:
  prometheus_data:

networks:
  mcp-network:
    driver: bridge
```

**文件：`.dockerignore`**
```
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.vscode
tests
*.test.ts
*.spec.ts
```

#### 7.3 创建系统服务配置

**文件：`{project_name}.service`**
```ini
[Unit]
Description={project_name} MCP Server
Documentation=https://github.com/your-org/{project_name}
After=network.target
Wants=network.target

[Service]
Type=simple
User=mcpuser
Group=mcpuser
WorkingDirectory=/opt/{project_name}
Environment=NODE_ENV=production
Environment=PATH=/opt/{project_name}/node_modules/.bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/usr/bin/node /opt/{project_name}/dist/index.js
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier={project_name}

# 安全设置
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/{project_name}/logs
PrivateTmp=yes
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

#### 7.4 创建监控配置

**文件：`monitoring/prometheus.yml`**
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'mcp-server'
    static_configs:
      - targets: ['mcp-server:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
```

**文件：`scripts/setup-monitoring.sh`**
```bash
#!/bin/bash

# 创建监控用户
sudo useradd --no-create-home --shell /bin/false prometheus
sudo useradd --no-create-home --shell /bin/false node_exporter

# 创建目录
sudo mkdir /etc/prometheus
sudo mkdir /var/lib/prometheus
sudo chown prometheus:prometheus /etc/prometheus
sudo chown prometheus:prometheus /var/lib/prometheus

# 复制配置文件
sudo cp monitoring/prometheus.yml /etc/prometheus/
sudo chown prometheus:prometheus /etc/prometheus/prometheus.yml

echo "Monitoring setup completed"
```

### 验证标准
- [ ] 构建脚本成功执行
- [ ] Docker容器正确构建和运行
- [ ] 系统服务配置正确

### 输出结果
```
部署配置包含：
1. 构建和部署脚本 (scripts/deploy.js)
2. Docker配置 (Dockerfile, docker-compose.yml)
3. 系统服务配置 ({project_name}.service)
4. 监控配置 (monitoring/)
5. 环境管理和依赖处理
```

---

## 完成检查清单

### 核心功能
- [ ] **MCP Server核心类**：正确实现协议处理程序和类型安全
- [ ] **工具管理器**：支持工具注册、列出、调用，包含错误处理
- [ ] **提示词管理器**：支持模板生成、参数验证和消息创建
- [ ] **资源管理器**：支持多种资源类型和内容格式

### 质量保证
- [ ] **类型安全**：完整的TypeScript类型定义和验证
- [ ] **错误处理**：结构化异常处理和日志记录
- [ ] **测试覆盖**：单元测试、集成测试、性能测试
- [ ] **代码质量**：遵循TypeScript最佳实践和MCP协议标准

### 部署就绪
- [ ] **构建系统**：TypeScript编译和优化配置
- [ ] **容器化**：Docker支持和多阶段构建
- [ ] **监控**：日志记录、健康检查、性能监控
- [ ] **环境管理**：配置验证和环境变量支持

## 下一步行动

✅ **TypeScript开发完成后，请继续：**
- 集成测试 → `workflows/04-testing-deployment.md`
- 配置调试 → `workflows/05-configuration-debug.md`
- 文档生成 → `guides/documentation-gen.md`
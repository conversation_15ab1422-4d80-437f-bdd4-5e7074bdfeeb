# MCP Server 配置调试指南

## 任务概述
指导AI助手完成MCP Server的配置管理、问题诊断和调试优化。

---

## 第1步：配置管理策略

### 输入要求
- 已部署的MCP Server
- 配置需求清单
- 环境特定要求

### 执行步骤

#### 1.1 分层配置架构

**配置层次结构设计**
```
配置优先级（从高到低）：
1. 命令行参数
2. 环境变量
3. 配置文件
4. 默认值

配置文件层次：
- config/production.json    # 生产环境
- config/staging.json       # 测试环境
- config/development.json   # 开发环境
- config/default.json       # 默认配置
```

**Python配置管理实现**
```python
# src/{project_name}/config/manager.py
import os
import json
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field
from ..utils.common import MCPError

@dataclass
class ConfigValue:
    \"\"\"配置值容器\"\"\"
    value: Any
    source: str  # 'default', 'file', 'env', 'cli'
    required: bool = False
    description: str = ""
    validator: Optional[callable] = None

class ConfigurationManager:
    \"\"\"配置管理器\"\"\"
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger(__name__)
        self._config: Dict[str, ConfigValue] = {}
        self._loaded = False
    
    def load_configuration(self, environment: str = None) -> Dict[str, Any]:
        \"\"\"加载配置\"\"\"
        if self._loaded:
            return self.get_config()
        
        # 1. 加载默认配置
        self._load_defaults()
        
        # 2. 加载环境配置文件
        environment = environment or os.getenv('NODE_ENV', 'development')
        self._load_config_file('default.json')
        self._load_config_file(f'{environment}.json')
        
        # 3. 加载环境变量
        self._load_environment_variables()
        
        # 4. 应用命令行参数
        self._load_cli_arguments()
        
        # 5. 验证配置
        self._validate_configuration()
        
        self._loaded = True
        self.logger.info(f"Configuration loaded for environment: {environment}")
        
        return self.get_config()
    
    def _load_defaults(self):
        \"\"\"加载默认配置\"\"\"
        defaults = {
            'app_name': ConfigValue(
                value='{project_name}',
                source='default',
                required=True,
                description='Application name'
            ),
            'debug': ConfigValue(
                value=False,
                source='default',
                description='Enable debug mode'
            ),
            'api_base_url': ConfigValue(
                value=None,
                source='default',
                required=True,
                description='Base URL for API calls',
                validator=self._validate_url
            ),
            'api_key': ConfigValue(
                value=None,
                source='default',
                required=True,
                description='API authentication key'
            ),
            'api_timeout': ConfigValue(
                value=30,
                source='default',
                description='API request timeout in seconds',
                validator=lambda x: x > 0
            ),
            'cache_enabled': ConfigValue(
                value=True,
                source='default',
                description='Enable caching'
            ),
            'cache_ttl': ConfigValue(
                value=300,
                source='default',
                description='Cache TTL in seconds',
                validator=lambda x: x > 0
            ),
            'log_level': ConfigValue(
                value='INFO',
                source='default',
                description='Logging level',
                validator=lambda x: x.upper() in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            ),
            'max_concurrent_requests': ConfigValue(
                value=10,
                source='default',
                description='Maximum concurrent requests',
                validator=lambda x: x > 0
            )
        }
        
        self._config.update(defaults)
    
    def _load_config_file(self, filename: str):
        \"\"\"加载配置文件\"\"\"
        config_path = self.config_dir / filename
        
        if not config_path.exists():
            self.logger.debug(f"Config file not found: {config_path}")
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            for key, value in file_config.items():
                if key in self._config:
                    self._config[key].value = value
                    self._config[key].source = f'file:{filename}'
                else:
                    self._config[key] = ConfigValue(
                        value=value,
                        source=f'file:{filename}',
                        description=f'Loaded from {filename}'
                    )
            
            self.logger.debug(f"Loaded config from {config_path}")
            
        except Exception as e:
            raise MCPError(f"Failed to load config file {config_path}: {str(e)}")
    
    def _load_environment_variables(self):
        \"\"\"加载环境变量\"\"\"
        env_mapping = {
            'app_name': 'APP_NAME',
            'debug': 'DEBUG',
            'api_base_url': 'API_BASE_URL',
            'api_key': 'API_KEY',
            'api_timeout': 'API_TIMEOUT',
            'cache_enabled': 'CACHE_ENABLED',
            'cache_ttl': 'CACHE_TTL',
            'log_level': 'LOG_LEVEL',
            'max_concurrent_requests': 'MAX_CONCURRENT_REQUESTS'
        }
        
        for config_key, env_key in env_mapping.items():
            env_value = os.getenv(env_key)
            if env_value is not None and config_key in self._config:
                # 类型转换
                converted_value = self._convert_env_value(env_value, self._config[config_key].value)
                self._config[config_key].value = converted_value
                self._config[config_key].source = f'env:{env_key}'
    
    def _load_cli_arguments(self):
        \"\"\"加载命令行参数\"\"\"
        import sys
        import argparse
        
        parser = argparse.ArgumentParser(description='MCP Server Configuration')
        parser.add_argument('--api-base-url', help='API base URL')
        parser.add_argument('--api-key', help='API key')
        parser.add_argument('--debug', action='store_true', help='Enable debug mode')
        parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'], help='Log level')
        parser.add_argument('--cache-enabled', type=bool, help='Enable caching')
        
        # 只解析已知参数，忽略未知参数
        args, unknown = parser.parse_known_args()
        
        # 应用命令行参数
        arg_mapping = {
            'api_base_url': args.api_base_url,
            'api_key': args.api_key,
            'debug': args.debug,
            'log_level': args.log_level,
            'cache_enabled': args.cache_enabled
        }
        
        for config_key, cli_value in arg_mapping.items():
            if cli_value is not None and config_key in self._config:
                self._config[config_key].value = cli_value
                self._config[config_key].source = 'cli'
    
    def _convert_env_value(self, env_value: str, default_value: Any) -> Any:
        \"\"\"转换环境变量值类型\"\"\"
        if isinstance(default_value, bool):
            return env_value.lower() in ('true', '1', 'yes', 'on')
        elif isinstance(default_value, int):
            return int(env_value)
        elif isinstance(default_value, float):
            return float(env_value)
        else:
            return env_value
    
    def _validate_configuration(self):
        \"\"\"验证配置\"\"\"
        errors = []
        
        for key, config_value in self._config.items():
            # 检查必需配置
            if config_value.required and config_value.value is None:
                errors.append(f"Required configuration missing: {key}")
            
            # 运行验证器
            if config_value.validator and config_value.value is not None:
                try:
                    if not config_value.validator(config_value.value):
                        errors.append(f"Invalid value for {key}: {config_value.value}")
                except Exception as e:
                    errors.append(f"Validation error for {key}: {str(e)}")
        
        if errors:
            raise MCPError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def _validate_url(self, url: str) -> bool:
        \"\"\"验证URL格式\"\"\"
        import urllib.parse
        try:
            result = urllib.parse.urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def get_config(self) -> Dict[str, Any]:
        \"\"\"获取配置字典\"\"\"
        return {key: config_value.value for key, config_value in self._config.items()}
    
    def get_config_info(self) -> Dict[str, Dict[str, Any]]:
        \"\"\"获取配置详细信息\"\"\"
        return {
            key: {
                'value': config_value.value,
                'source': config_value.source,
                'required': config_value.required,
                'description': config_value.description
            }
            for key, config_value in self._config.items()
        }
    
    def update_config(self, key: str, value: Any, source: str = 'runtime'):
        \"\"\"运行时更新配置\"\"\"
        if key in self._config:
            old_value = self._config[key].value
            self._config[key].value = value
            self._config[key].source = source
            self.logger.info(f"Updated config {key}: {old_value} -> {value} (source: {source})")
        else:
            self._config[key] = ConfigValue(
                value=value,
                source=source,
                description=f'Runtime configuration'
            )
            self.logger.info(f"Added new config {key}: {value} (source: {source})")
    
    def export_config(self, format: str = 'json') -> str:
        \"\"\"导出配置\"\"\"
        if format == 'json':
            return json.dumps(self.get_config(), indent=2, default=str)
        elif format == 'env':
            lines = []
            for key, value in self.get_config().items():
                env_key = key.upper()
                lines.append(f"{env_key}={value}")
            return '\\n'.join(lines)
        else:
            raise ValueError(f"Unsupported export format: {format}")

# 全局配置管理器实例
config_manager = ConfigurationManager()
```

**TypeScript配置管理实现**
```typescript
// src/config/manager.ts
import { z } from 'zod';
import * as fs from 'fs/promises';
import * as path from 'path';
import { logger } from '../utils/common.js';

export interface ConfigValue<T = any> {
  value: T;
  source: 'default' | 'file' | 'env' | 'cli' | 'runtime';
  required?: boolean;
  description?: string;
  validator?: (value: T) => boolean;
}

export class ConfigurationManager {
  private config: Map<string, ConfigValue> = new Map();
  private loaded = false;

  constructor(private configDir: string = 'config') {}

  async loadConfiguration(environment?: string): Promise<Record<string, any>> {
    if (this.loaded) {
      return this.getConfig();
    }

    // 1. 加载默认配置
    this.loadDefaults();

    // 2. 加载配置文件
    const env = environment || process.env.NODE_ENV || 'development';
    await this.loadConfigFile('default.json');
    await this.loadConfigFile(`${env}.json`);

    // 3. 加载环境变量
    this.loadEnvironmentVariables();

    // 4. 应用命令行参数
    this.loadCliArguments();

    // 5. 验证配置
    this.validateConfiguration();

    this.loaded = true;
    logger.info(`Configuration loaded for environment: ${env}`);

    return this.getConfig();
  }

  private loadDefaults(): void {
    const defaults: Record<string, ConfigValue> = {
      appName: {
        value: '{project_name}',
        source: 'default',
        required: true,
        description: 'Application name'
      },
      debug: {
        value: false,
        source: 'default',
        description: 'Enable debug mode'
      },
      apiBaseUrl: {
        value: null,
        source: 'default',
        required: true,
        description: 'Base URL for API calls',
        validator: this.validateUrl
      },
      apiKey: {
        value: null,
        source: 'default',
        required: true,
        description: 'API authentication key'
      },
      apiTimeout: {
        value: 30000,
        source: 'default',
        description: 'API request timeout in milliseconds',
        validator: (x: number) => x > 0
      },
      cacheEnabled: {
        value: true,
        source: 'default',
        description: 'Enable caching'
      },
      cacheTtl: {
        value: 300,
        source: 'default',
        description: 'Cache TTL in seconds',
        validator: (x: number) => x > 0
      },
      logLevel: {
        value: 'info',
        source: 'default',
        description: 'Logging level',
        validator: (x: string) => ['debug', 'info', 'warn', 'error'].includes(x.toLowerCase())
      },
      maxConcurrentRequests: {
        value: 10,
        source: 'default',
        description: 'Maximum concurrent requests',
        validator: (x: number) => x > 0
      }
    };

    for (const [key, configValue] of Object.entries(defaults)) {
      this.config.set(key, configValue);
    }
  }

  private async loadConfigFile(filename: string): Promise<void> {
    const configPath = path.join(this.configDir, filename);

    try {
      const content = await fs.readFile(configPath, 'utf-8');
      const fileConfig = JSON.parse(content);

      for (const [key, value] of Object.entries(fileConfig)) {
        if (this.config.has(key)) {
          const existing = this.config.get(key)!;
          existing.value = value;
          existing.source = 'file';
        } else {
          this.config.set(key, {
            value,
            source: 'file',
            description: `Loaded from ${filename}`
          });
        }
      }

      logger.debug(`Loaded config from ${configPath}`);
    } catch (error) {
      if ((error as any).code !== 'ENOENT') {
        throw new Error(`Failed to load config file ${configPath}: ${error}`);
      }
      logger.debug(`Config file not found: ${configPath}`);
    }
  }

  private loadEnvironmentVariables(): void {
    const envMapping: Record<string, string> = {
      appName: 'APP_NAME',
      debug: 'DEBUG',
      apiBaseUrl: 'API_BASE_URL',
      apiKey: 'API_KEY',
      apiTimeout: 'API_TIMEOUT',
      cacheEnabled: 'CACHE_ENABLED',
      cacheTtl: 'CACHE_TTL',
      logLevel: 'LOG_LEVEL',
      maxConcurrentRequests: 'MAX_CONCURRENT_REQUESTS'
    };

    for (const [configKey, envKey] of Object.entries(envMapping)) {
      const envValue = process.env[envKey];
      if (envValue !== undefined && this.config.has(configKey)) {
        const existing = this.config.get(configKey)!;
        const convertedValue = this.convertEnvValue(envValue, existing.value);
        existing.value = convertedValue;
        existing.source = 'env';
      }
    }
  }

  private loadCliArguments(): void {
    // 简化的命令行参数处理
    const args = process.argv.slice(2);
    
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      if (arg.startsWith('--')) {
        const key = arg.slice(2).replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
        const value = args[i + 1];
        
        if (this.config.has(key) && value && !value.startsWith('--')) {
          const existing = this.config.get(key)!;
          const convertedValue = this.convertEnvValue(value, existing.value);
          existing.value = convertedValue;
          existing.source = 'cli';
          i++; // 跳过值参数
        }
      }
    }
  }

  private convertEnvValue(envValue: string, defaultValue: any): any {
    if (typeof defaultValue === 'boolean') {
      return ['true', '1', 'yes', 'on'].includes(envValue.toLowerCase());
    } else if (typeof defaultValue === 'number') {
      const num = Number(envValue);
      return isNaN(num) ? defaultValue : num;
    } else {
      return envValue;
    }
  }

  private validateConfiguration(): void {
    const errors: string[] = [];

    for (const [key, configValue] of this.config) {
      // 检查必需配置
      if (configValue.required && configValue.value == null) {
        errors.push(`Required configuration missing: ${key}`);
      }

      // 运行验证器
      if (configValue.validator && configValue.value != null) {
        try {
          if (!configValue.validator(configValue.value)) {
            errors.push(`Invalid value for ${key}: ${configValue.value}`);
          }
        } catch (error) {
          errors.push(`Validation error for ${key}: ${error}`);
        }
      }
    }

    if (errors.length > 0) {
      throw new Error(`Configuration validation failed: ${errors.join('; ')}`);
    }
  }

  private validateUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  getConfig(): Record<string, any> {
    const result: Record<string, any> = {};
    for (const [key, configValue] of this.config) {
      result[key] = configValue.value;
    }
    return result;
  }

  getConfigInfo(): Record<string, Omit<ConfigValue, 'validator'>> {
    const result: Record<string, any> = {};
    for (const [key, configValue] of this.config) {
      result[key] = {
        value: configValue.value,
        source: configValue.source,
        required: configValue.required,
        description: configValue.description
      };
    }
    return result;
  }

  updateConfig(key: string, value: any, source: ConfigValue['source'] = 'runtime'): void {
    if (this.config.has(key)) {
      const existing = this.config.get(key)!;
      const oldValue = existing.value;
      existing.value = value;
      existing.source = source;
      logger.info(`Updated config ${key}: ${oldValue} -> ${value} (source: ${source})`);
    } else {
      this.config.set(key, {
        value,
        source,
        description: 'Runtime configuration'
      });
      logger.info(`Added new config ${key}: ${value} (source: ${source})`);
    }
  }

  exportConfig(format: 'json' | 'env' = 'json'): string {
    if (format === 'json') {
      return JSON.stringify(this.getConfig(), null, 2);
    } else if (format === 'env') {
      const lines: string[] = [];
      for (const [key, value] of Object.entries(this.getConfig())) {
        const envKey = key.replace(/([A-Z])/g, '_$1').toUpperCase();
        lines.push(`${envKey}=${value}`);
      }
      return lines.join('\\n');
    } else {
      throw new Error(`Unsupported export format: ${format}`);
    }
  }
}

// 全局配置管理器实例
export const configManager = new ConfigurationManager();
```

#### 1.2 配置文件模板

**开发环境配置**
```json
// config/development.json
{
  "debug": true,
  "logLevel": "debug",
  "apiTimeout": 10,
  "cacheEnabled": false,
  "maxConcurrentRequests": 5,
  "metrics": {
    "enabled": false,
    "port": 8001
  },
  "development": {
    "hotReload": true,
    "mockApi": true,
    "testData": true
  }
}
```

**生产环境配置**
```json
// config/production.json
{
  "debug": false,
  "logLevel": "info",
  "apiTimeout": 30,
  "cacheEnabled": true,
  "cacheTtl": 600,
  "maxConcurrentRequests": 50,
  "metrics": {
    "enabled": true,
    "port": 8000
  },
  "security": {
    "rateLimiting": {
      "enabled": true,
      "maxRequests": 100,
      "windowMs": 60000
    },
    "cors": {
      "enabled": true,
      "origins": []
    }
  },
  "performance": {
    "clustering": false,
    "compression": true,
    "keepAlive": true
  }
}
```

**测试环境配置**
```json
// config/test.json
{
  "debug": false,
  "logLevel": "error",
  "apiTimeout": 5,
  "cacheEnabled": false,
  "maxConcurrentRequests": 10,
  "testing": {
    "mockResponses": true,
    "timeoutOverride": true,
    "errorInjection": false
  }
}
```

### 验证标准
- [ ] 配置分层架构正确实现
- [ ] 配置验证机制有效
- [ ] 环境变量覆盖正常工作
- [ ] 配置文件加载顺序正确

### 输出结果
```
配置管理系统包含：
1. 分层配置管理器 (ConfigurationManager)
2. 环境特定配置文件
3. 配置验证和转换机制
4. 运行时配置更新支持
```

---

## 第2步：调试工具和技巧

### 输入要求
- 配置完善的MCP Server
- 调试需求和问题场景
- 开发和生产环境访问

### 执行步骤

#### 2.1 调试工具集成

**Python调试工具**
```python
# src/{project_name}/debug/debugger.py
import logging
import traceback
import functools
import time
import inspect
from typing import Any, Dict, List, Optional, Callable
from ..config.manager import config_manager

class MCPDebugger:
    \"\"\"MCP调试器\"\"\"
    
    def __init__(self):
        self.enabled = config_manager.get_config().get('debug', False)
        self.traces: List[Dict[str, Any]] = []
        self.max_traces = 1000
    
    def debug_tool_call(self, func: Callable) -> Callable:
        \"\"\"工具调用调试装饰器\"\"\"
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            if not self.enabled:
                return await func(*args, **kwargs)
            
            # 记录调用信息
            call_info = {
                'type': 'tool_call',
                'function': func.__name__,
                'module': func.__module__,
                'timestamp': time.time(),
                'args': str(args)[:200],  # 截断长参数
                'kwargs': str(kwargs)[:200]
            }
            
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                
                call_info.update({
                    'success': True,
                    'duration': time.time() - start_time,
                    'result_type': type(result).__name__,
                    'result_size': len(str(result)) if result else 0
                })
                
                self._add_trace(call_info)
                return result
                
            except Exception as e:
                call_info.update({
                    'success': False,
                    'duration': time.time() - start_time,
                    'error': str(e),
                    'traceback': traceback.format_exc()
                })
                
                self._add_trace(call_info)
                logging.error(f"Tool call failed: {func.__name__}", extra=call_info)
                raise
                
        return wrapper
    
    def debug_api_call(self, func: Callable) -> Callable:
        \"\"\"API调用调试装饰器\"\"\"
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            if not self.enabled:
                return await func(*args, **kwargs)
            
            # 记录API调用
            api_info = {
                'type': 'api_call',
                'function': func.__name__,
                'timestamp': time.time()
            }
            
            # 尝试提取URL和方法信息
            if args:
                for arg in args:
                    if hasattr(arg, 'url'):
                        api_info['url'] = str(arg.url)
                    if hasattr(arg, 'method'):
                        api_info['method'] = arg.method
            
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                
                api_info.update({
                    'success': True,
                    'duration': time.time() - start_time,
                    'status_code': getattr(result, 'status_code', None),
                    'response_size': len(str(result)) if result else 0
                })
                
                self._add_trace(api_info)
                return result
                
            except Exception as e:
                api_info.update({
                    'success': False,
                    'duration': time.time() - start_time,
                    'error': str(e),
                    'error_type': type(e).__name__
                })
                
                self._add_trace(api_info)
                logging.error(f"API call failed: {func.__name__}", extra=api_info)
                raise
                
        return wrapper
    
    def _add_trace(self, trace_info: Dict[str, Any]):
        \"\"\"添加追踪信息\"\"\"
        self.traces.append(trace_info)
        
        # 限制追踪记录数量
        if len(self.traces) > self.max_traces:
            self.traces = self.traces[-self.max_traces:]
    
    def get_traces(self, trace_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        \"\"\"获取追踪记录\"\"\"
        traces = self.traces
        
        if trace_type:
            traces = [t for t in traces if t.get('type') == trace_type]
        
        return traces[-limit:]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        \"\"\"获取性能摘要\"\"\"
        if not self.traces:
            return {}
        
        tool_calls = [t for t in self.traces if t.get('type') == 'tool_call']
        api_calls = [t for t in self.traces if t.get('type') == 'api_call']
        
        summary = {
            'total_traces': len(self.traces),
            'tool_calls': {
                'count': len(tool_calls),
                'success_rate': sum(1 for t in tool_calls if t.get('success', False)) / len(tool_calls) if tool_calls else 0,
                'avg_duration': sum(t.get('duration', 0) for t in tool_calls) / len(tool_calls) if tool_calls else 0
            },
            'api_calls': {
                'count': len(api_calls),
                'success_rate': sum(1 for t in api_calls if t.get('success', False)) / len(api_calls) if api_calls else 0,
                'avg_duration': sum(t.get('duration', 0) for t in api_calls) / len(api_calls) if api_calls else 0
            }
        }
        
        return summary
    
    def export_traces(self, format: str = 'json') -> str:
        \"\"\"导出追踪数据\"\"\"
        if format == 'json':
            import json
            return json.dumps(self.traces, indent=2, default=str)
        elif format == 'csv':
            import csv
            import io
            
            output = io.StringIO()
            if self.traces:
                fieldnames = set()
                for trace in self.traces:
                    fieldnames.update(trace.keys())
                
                writer = csv.DictWriter(output, fieldnames=list(fieldnames))
                writer.writeheader()
                for trace in self.traces:
                    writer.writerow(trace)
            
            return output.getvalue()
        else:
            raise ValueError(f"Unsupported format: {format}")

# 全局调试器实例
debugger = MCPDebugger()

# src/{project_name}/debug/profiler.py
import cProfile
import pstats
import io
from typing import Optional, Callable, Any
import functools

class MCPProfiler:
    \"\"\"MCP性能分析器\"\"\"
    
    def __init__(self):
        self.enabled = config_manager.get_config().get('profiling_enabled', False)
        self.profiles: Dict[str, pstats.Stats] = {}
    
    def profile_function(self, name: Optional[str] = None):
        \"\"\"函数性能分析装饰器\"\"\"
        def decorator(func: Callable) -> Callable:
            profile_name = name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                if not self.enabled:
                    return await func(*args, **kwargs)
                
                profiler = cProfile.Profile()
                profiler.enable()
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    profiler.disable()
                    
                    # 保存性能数据
                    s = io.StringIO()
                    stats = pstats.Stats(profiler, stream=s)
                    self.profiles[profile_name] = stats
                    
            return wrapper
        return decorator
    
    def get_profile_report(self, name: str, sort_by: str = 'cumulative') -> str:
        \"\"\"获取性能分析报告\"\"\"
        if name not in self.profiles:
            return f"No profile data found for: {name}"
        
        s = io.StringIO()
        stats = self.profiles[name]
        stats.stream = s
        stats.sort_stats(sort_by)
        stats.print_stats()
        
        return s.getvalue()
    
    def get_all_profiles(self) -> List[str]:
        \"\"\"获取所有性能分析名称\"\"\"
        return list(self.profiles.keys())

# 全局性能分析器实例
profiler = MCPProfiler()
```

**TypeScript调试工具**
```typescript
// src/debug/debugger.ts
import { logger } from '../utils/common.js';
import { configManager } from '../config/manager.js';

export interface TraceInfo {
  type: 'tool_call' | 'api_call' | 'resource_read' | 'prompt_generation';
  function: string;
  module?: string;
  timestamp: number;
  duration?: number;
  success?: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

export class MCPDebugger {
  private traces: TraceInfo[] = [];
  private maxTraces = 1000;
  private enabled = false;

  constructor() {
    this.enabled = configManager.getConfig().debug || false;
  }

  debugToolCall<T extends (...args: any[]) => Promise<any>>(
    func: T,
    metadata?: Record<string, any>
  ): T {
    return (async (...args: any[]) => {
      if (!this.enabled) {
        return await func(...args);
      }

      const callInfo: TraceInfo = {
        type: 'tool_call',
        function: func.name,
        timestamp: Date.now(),
        metadata: {
          ...metadata,
          args: JSON.stringify(args).substring(0, 200)
        }
      };

      const startTime = performance.now();
      try {
        const result = await func(...args);
        
        callInfo.success = true;
        callInfo.duration = performance.now() - startTime;
        callInfo.metadata!.resultType = typeof result;
        callInfo.metadata!.resultSize = JSON.stringify(result).length;

        this.addTrace(callInfo);
        return result;
      } catch (error) {
        callInfo.success = false;
        callInfo.duration = performance.now() - startTime;
        callInfo.error = error instanceof Error ? error.message : String(error);

        this.addTrace(callInfo);
        logger.error(`Tool call failed: ${func.name}`, callInfo);
        throw error;
      }
    }) as T;
  }

  debugApiCall<T extends (...args: any[]) => Promise<any>>(
    func: T,
    metadata?: Record<string, any>
  ): T {
    return (async (...args: any[]) => {
      if (!this.enabled) {
        return await func(...args);
      }

      const apiInfo: TraceInfo = {
        type: 'api_call',
        function: func.name,
        timestamp: Date.now(),
        metadata: { ...metadata }
      };

      const startTime = performance.now();
      try {
        const result = await func(...args);
        
        apiInfo.success = true;
        apiInfo.duration = performance.now() - startTime;
        apiInfo.metadata!.statusCode = result?.status;
        apiInfo.metadata!.responseSize = JSON.stringify(result).length;

        this.addTrace(apiInfo);
        return result;
      } catch (error) {
        apiInfo.success = false;
        apiInfo.duration = performance.now() - startTime;
        apiInfo.error = error instanceof Error ? error.message : String(error);

        this.addTrace(apiInfo);
        logger.error(`API call failed: ${func.name}`, apiInfo);
        throw error;
      }
    }) as T;
  }

  private addTrace(trace: TraceInfo): void {
    this.traces.push(trace);
    
    if (this.traces.length > this.maxTraces) {
      this.traces = this.traces.slice(-this.maxTraces);
    }
  }

  getTraces(type?: string, limit = 100): TraceInfo[] {
    let traces = this.traces;
    
    if (type) {
      traces = traces.filter(t => t.type === type);
    }
    
    return traces.slice(-limit);
  }

  getPerformanceSummary(): Record<string, any> {
    if (this.traces.length === 0) {
      return {};
    }

    const toolCalls = this.traces.filter(t => t.type === 'tool_call');
    const apiCalls = this.traces.filter(t => t.type === 'api_call');

    return {
      totalTraces: this.traces.length,
      toolCalls: {
        count: toolCalls.length,
        successRate: toolCalls.filter(t => t.success).length / toolCalls.length || 0,
        avgDuration: toolCalls.reduce((sum, t) => sum + (t.duration || 0), 0) / toolCalls.length || 0
      },
      apiCalls: {
        count: apiCalls.length,
        successRate: apiCalls.filter(t => t.success).length / apiCalls.length || 0,
        avgDuration: apiCalls.reduce((sum, t) => sum + (t.duration || 0), 0) / apiCalls.length || 0
      }
    };
  }

  exportTraces(format: 'json' | 'csv' = 'json'): string {
    if (format === 'json') {
      return JSON.stringify(this.traces, null, 2);
    } else if (format === 'csv') {
      if (this.traces.length === 0) {
        return '';
      }

      const headers = Object.keys(this.traces[0]);
      const csvRows = [
        headers.join(','),
        ...this.traces.map(trace => 
          headers.map(header => {
            const value = (trace as any)[header];
            return typeof value === 'object' ? JSON.stringify(value) : String(value);
          }).join(',')
        )
      ];

      return csvRows.join('\\n');
    } else {
      throw new Error(`Unsupported format: ${format}`);
    }
  }

  clearTraces(): void {
    this.traces = [];
    logger.info('Debug traces cleared');
  }
}

// 全局调试器实例
export const debugger = new MCPDebugger();
```

#### 2.2 实时调试接口

**调试API端点**
```python
# src/{project_name}/debug/api.py
from fastapi import FastAPI, HTTPException
from typing import Optional, Dict, Any
import json
from .debugger import debugger
from .profiler import profiler
from ..config.manager import config_manager

def create_debug_app() -> FastAPI:
    \"\"\"创建调试API应用\"\"\"
    app = FastAPI(title="MCP Server Debug API", version="1.0.0")
    
    @app.get("/debug/status")
    async def debug_status():
        \"\"\"获取调试状态\"\"\"
        return {
            "debug_enabled": debugger.enabled,
            "profiling_enabled": profiler.enabled,
            "trace_count": len(debugger.traces),
            "profile_count": len(profiler.profiles)
        }
    
    @app.get("/debug/config")
    async def get_config():
        \"\"\"获取当前配置\"\"\"
        return config_manager.get_config_info()
    
    @app.post("/debug/config/{key}")
    async def update_config(key: str, value: Any):
        \"\"\"更新配置\"\"\"
        try:
            config_manager.update_config(key, value, 'debug_api')
            return {"message": f"Updated {key} to {value}"}
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    @app.get("/debug/traces")
    async def get_traces(
        trace_type: Optional[str] = None,
        limit: int = 100
    ):
        \"\"\"获取调试追踪\"\"\"
        return debugger.get_traces(trace_type, limit)
    
    @app.get("/debug/performance")
    async def get_performance():
        \"\"\"获取性能摘要\"\"\"
        return debugger.get_performance_summary()
    
    @app.get("/debug/traces/export")
    async def export_traces(format: str = "json"):
        \"\"\"导出追踪数据\"\"\"
        try:
            return {"data": debugger.export_traces(format)}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    @app.post("/debug/traces/clear")
    async def clear_traces():
        \"\"\"清除追踪数据\"\"\"
        debugger.traces.clear()
        return {"message": "Traces cleared"}
    
    @app.get("/debug/profiles")
    async def get_profiles():
        \"\"\"获取性能分析列表\"\"\"
        return {"profiles": profiler.get_all_profiles()}
    
    @app.get("/debug/profiles/{name}")
    async def get_profile_report(name: str, sort_by: str = "cumulative"):
        \"\"\"获取性能分析报告\"\"\"
        report = profiler.get_profile_report(name, sort_by)
        if "No profile data found" in report:
            raise HTTPException(status_code=404, detail=report)
        return {"report": report}
    
    return app

# 启动调试服务器的函数
def start_debug_server(port: int = 8001):
    \"\"\"启动调试服务器\"\"\"
    if not config_manager.get_config().get('debug', False):
        return
    
    import uvicorn
    app = create_debug_app()
    
    uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
```

**调试命令行工具**
```python
# src/{project_name}/debug/cli.py
import click
import json
import requests
from typing import Optional

@click.group()
def debug_cli():
    \"\"\"MCP Server调试命令行工具\"\"\"
    pass

@debug_cli.command()
@click.option('--host', default='localhost', help='Debug server host')
@click.option('--port', default=8001, help='Debug server port')
def status(host: str, port: int):
    \"\"\"获取调试状态\"\"\"
    try:
        response = requests.get(f"http://{host}:{port}/debug/status")
        response.raise_for_status()
        print(json.dumps(response.json(), indent=2))
    except Exception as e:
        click.echo(f"Error: {e}", err=True)

@debug_cli.command()
@click.option('--host', default='localhost', help='Debug server host')
@click.option('--port', default=8001, help='Debug server port')
@click.option('--type', help='Filter by trace type')
@click.option('--limit', default=10, help='Number of traces to show')
def traces(host: str, port: int, type: Optional[str], limit: int):
    \"\"\"获取调试追踪\"\"\"
    try:
        params = {'limit': limit}
        if type:
            params['trace_type'] = type
        
        response = requests.get(f"http://{host}:{port}/debug/traces", params=params)
        response.raise_for_status()
        
        traces = response.json()
        for trace in traces:
            print(f"[{trace['type']}] {trace['function']} - {trace.get('duration', 0):.3f}s")
            if not trace.get('success', True):
                print(f"  Error: {trace.get('error', 'Unknown error')}")
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)

@debug_cli.command()
@click.option('--host', default='localhost', help='Debug server host')
@click.option('--port', default=8001, help='Debug server port')
def performance(host: str, port: int):
    \"\"\"获取性能摘要\"\"\"
    try:
        response = requests.get(f"http://{host}:{port}/debug/performance")
        response.raise_for_status()
        print(json.dumps(response.json(), indent=2))
    except Exception as e:
        click.echo(f"Error: {e}", err=True)

@debug_cli.command()
@click.option('--host', default='localhost', help='Debug server host')
@click.option('--port', default=8001, help='Debug server port')
@click.argument('key')
@click.argument('value')
def set_config(host: str, port: int, key: str, value: str):
    \"\"\"更新配置\"\"\"
    try:
        # 尝试解析JSON值
        try:
            parsed_value = json.loads(value)
        except json.JSONDecodeError:
            parsed_value = value
        
        response = requests.post(f"http://{host}:{port}/debug/config/{key}", json=parsed_value)
        response.raise_for_status()
        print(response.json()['message'])
    except Exception as e:
        click.echo(f"Error: {e}", err=True)

if __name__ == '__main__':
    debug_cli()
```

### 验证标准
- [ ] 调试装饰器正确收集信息
- [ ] 性能分析数据准确
- [ ] 调试API响应正常
- [ ] 命令行工具功能完整

### 输出结果
```
调试工具集合包含：
1. 函数调用追踪器 (MCPDebugger)
2. 性能分析器 (MCPProfiler)
3. 实时调试API接口
4. 命令行调试工具
```

---

## 第3步：问题诊断和排查

### 输入要求
- 已部署的MCP Server
- 问题报告和日志
- 调试工具就绪

### 执行步骤

#### 3.1 常见问题诊断手册

**问题分类和诊断流程**
```python
# src/{project_name}/debug/diagnostics.py
import asyncio
import logging
import traceback
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from ..config.manager import config_manager

class ProblemCategory(Enum):
    CONFIGURATION = "configuration"
    CONNECTIVITY = "connectivity"
    PERFORMANCE = "performance"
    AUTHENTICATION = "authentication"
    DATA_FORMAT = "data_format"
    RESOURCE_EXHAUSTION = "resource_exhaustion"
    LOGIC_ERROR = "logic_error"

@dataclass
class DiagnosticResult:
    category: ProblemCategory
    severity: str  # "low", "medium", "high", "critical"
    description: str
    details: Dict[str, Any]
    recommendations: List[str]
    auto_fixable: bool = False

class MCPDiagnostics:
    \"\"\"MCP诊断系统\"\"\"
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.diagnostics = {
            ProblemCategory.CONFIGURATION: self._diagnose_configuration,
            ProblemCategory.CONNECTIVITY: self._diagnose_connectivity,
            ProblemCategory.PERFORMANCE: self._diagnose_performance,
            ProblemCategory.AUTHENTICATION: self._diagnose_authentication,
            ProblemCategory.DATA_FORMAT: self._diagnose_data_format,
            ProblemCategory.RESOURCE_EXHAUSTION: self._diagnose_resources,
        }
    
    async def run_full_diagnosis(self) -> List[DiagnosticResult]:
        \"\"\"运行完整诊断\"\"\"
        results = []
        
        for category, diagnostic_func in self.diagnostics.items():
            try:
                result = await diagnostic_func()
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.error(f"Diagnostic failed for {category}: {str(e)}")
                results.append(DiagnosticResult(
                    category=category,
                    severity="medium",
                    description="Diagnostic check failed",
                    details={"error": str(e), "traceback": traceback.format_exc()},
                    recommendations=["Check diagnostic system integrity"]
                ))
        
        return results
    
    async def _diagnose_configuration(self) -> Optional[DiagnosticResult]:
        \"\"\"诊断配置问题\"\"\"
        config = config_manager.get_config()
        issues = []
        
        # 检查必需配置
        required_configs = ['api_base_url', 'api_key']
        for key in required_configs:
            if not config.get(key):
                issues.append(f"Missing required configuration: {key}")
        
        # 检查URL格式
        if config.get('api_base_url'):
            import urllib.parse
            try:
                result = urllib.parse.urlparse(config['api_base_url'])
                if not all([result.scheme, result.netloc]):
                    issues.append("Invalid API base URL format")
            except Exception:
                issues.append("Malformed API base URL")
        
        # 检查数值配置
        numeric_configs = {
            'api_timeout': (1, 300),
            'cache_ttl': (1, 3600),
            'max_concurrent_requests': (1, 1000)
        }
        
        for key, (min_val, max_val) in numeric_configs.items():
            value = config.get(key)
            if value is not None:
                if not isinstance(value, (int, float)) or not (min_val <= value <= max_val):
                    issues.append(f"Invalid {key}: {value} (should be between {min_val} and {max_val})")
        
        if issues:
            return DiagnosticResult(
                category=ProblemCategory.CONFIGURATION,
                severity="high" if "Missing required" in str(issues) else "medium",
                description="Configuration issues detected",
                details={"issues": issues, "config": config},
                recommendations=[
                    "Review and fix configuration values",
                    "Check environment variables",
                    "Validate configuration file syntax"
                ],
                auto_fixable=False
            )
        
        return None
    
    async def _diagnose_connectivity(self) -> Optional[DiagnosticResult]:
        \"\"\"诊断连接问题\"\"\"
        import httpx
        
        config = config_manager.get_config()
        api_base_url = config.get('api_base_url')
        
        if not api_base_url:
            return None
        
        issues = []
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # 测试基本连接
                response = await client.get(f"{api_base_url}/health", 
                                          headers={"User-Agent": "MCP-Diagnostics"})
                
                if response.status_code >= 500:
                    issues.append(f"Server error: {response.status_code}")
                elif response.status_code >= 400:
                    issues.append(f"Client error: {response.status_code}")
                
        except httpx.TimeoutException:
            issues.append("Connection timeout - server may be slow or unreachable")
        except httpx.ConnectError:
            issues.append("Connection failed - server may be down or URL incorrect")
        except Exception as e:
            issues.append(f"Unexpected connection error: {str(e)}")
        
        if issues:
            return DiagnosticResult(
                category=ProblemCategory.CONNECTIVITY,
                severity="high",
                description="Connectivity issues detected",
                details={"issues": issues, "api_base_url": api_base_url},
                recommendations=[
                    "Check network connectivity",
                    "Verify API base URL",
                    "Check server status",
                    "Review firewall settings"
                ]
            )
        
        return None
    
    async def _diagnose_performance(self) -> Optional[DiagnosticResult]:
        \"\"\"诊断性能问题\"\"\"
        from .debugger import debugger
        
        summary = debugger.get_performance_summary()
        
        if not summary:
            return None
        
        issues = []
        
        # 检查工具调用性能
        tool_calls = summary.get('tool_calls', {})
        if tool_calls.get('avg_duration', 0) > 5.0:
            issues.append(f"Slow tool calls: {tool_calls['avg_duration']:.2f}s average")
        
        if tool_calls.get('success_rate', 1.0) < 0.9:
            issues.append(f"High tool failure rate: {(1 - tool_calls['success_rate']) * 100:.1f}%")
        
        # 检查API调用性能
        api_calls = summary.get('api_calls', {})
        if api_calls.get('avg_duration', 0) > 3.0:
            issues.append(f"Slow API calls: {api_calls['avg_duration']:.2f}s average")
        
        # 检查内存使用
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        if memory_mb > 500:
            issues.append(f"High memory usage: {memory_mb:.1f}MB")
        
        if issues:
            return DiagnosticResult(
                category=ProblemCategory.PERFORMANCE,
                severity="medium",
                description="Performance issues detected",
                details={"issues": issues, "summary": summary},
                recommendations=[
                    "Optimize slow operations",
                    "Implement caching",
                    "Review resource usage",
                    "Consider connection pooling"
                ]
            )
        
        return None
    
    async def _diagnose_authentication(self) -> Optional[DiagnosticResult]:
        \"\"\"诊断认证问题\"\"\"
        import httpx
        
        config = config_manager.get_config()
        api_base_url = config.get('api_base_url')
        api_key = config.get('api_key')
        
        if not api_base_url or not api_key:
            return None
        
        issues = []
        
        try:
            headers = {"Authorization": f"Bearer {api_key}"}
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{api_base_url}/auth/verify", headers=headers)
                
                if response.status_code == 401:
                    issues.append("Invalid or expired API key")
                elif response.status_code == 403:
                    issues.append("API key lacks required permissions")
                
        except Exception as e:
            issues.append(f"Authentication check failed: {str(e)}")
        
        if issues:
            return DiagnosticResult(
                category=ProblemCategory.AUTHENTICATION,
                severity="high",
                description="Authentication issues detected",
                details={"issues": issues},
                recommendations=[
                    "Verify API key validity",
                    "Check API key permissions",
                    "Regenerate API key if necessary",
                    "Review authentication documentation"
                ]
            )
        
        return None
    
    async def _diagnose_data_format(self) -> Optional[DiagnosticResult]:
        \"\"\"诊断数据格式问题\"\"\"
        # 这里可以添加特定的数据格式检查逻辑
        # 例如检查最近的API响应格式
        return None
    
    async def _diagnose_resources(self) -> Optional[DiagnosticResult]:
        \"\"\"诊断资源耗尽问题\"\"\"
        import psutil
        import shutil
        
        issues = []
        
        # 检查内存使用
        memory = psutil.virtual_memory()
        if memory.percent > 90:
            issues.append(f"High memory usage: {memory.percent:.1f}%")
        
        # 检查磁盘空间
        disk = shutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        if disk_percent > 90:
            issues.append(f"Low disk space: {disk_percent:.1f}% used")
        
        # 检查CPU使用
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent > 90:
            issues.append(f"High CPU usage: {cpu_percent:.1f}%")
        
        if issues:
            return DiagnosticResult(
                category=ProblemCategory.RESOURCE_EXHAUSTION,
                severity="high",
                description="Resource exhaustion detected",
                details={"issues": issues},
                recommendations=[
                    "Free up system resources",
                    "Optimize application performance",
                    "Consider scaling up resources",
                    "Implement resource monitoring"
                ]
            )
        
        return None
    
    def generate_report(self, results: List[DiagnosticResult]) -> str:
        \"\"\"生成诊断报告\"\"\"
        if not results:
            return "✅ No issues detected - system appears healthy"
        
        report_lines = ["# MCP Server 诊断报告\\n"]
        
        # 按严重程度分组
        by_severity = {}
        for result in results:
            if result.severity not in by_severity:
                by_severity[result.severity] = []
            by_severity[result.severity].append(result)
        
        # 输出结果
        severity_order = ['critical', 'high', 'medium', 'low']
        severity_icons = {
            'critical': '🔴',
            'high': '🟠', 
            'medium': '🟡',
            'low': '🔵'
        }
        
        for severity in severity_order:
            if severity in by_severity:
                report_lines.append(f"## {severity_icons[severity]} {severity.upper()} Issues\\n")
                
                for result in by_severity[severity]:
                    report_lines.append(f"### {result.category.value}: {result.description}\\n")
                    
                    if result.details:
                        report_lines.append("**Details:**")
                        for key, value in result.details.items():
                            if isinstance(value, list):
                                report_lines.append(f"- {key}:")
                                for item in value:
                                    report_lines.append(f"  - {item}")
                            else:
                                report_lines.append(f"- {key}: {value}")
                        report_lines.append("")
                    
                    if result.recommendations:
                        report_lines.append("**Recommendations:**")
                        for rec in result.recommendations:
                            report_lines.append(f"- {rec}")
                        report_lines.append("")
        
        return "\\n".join(report_lines)

# 全局诊断系统实例
diagnostics = MCPDiagnostics()
```

#### 3.2 自动化问题修复

**自动修复系统**
```python
# src/{project_name}/debug/auto_fix.py
import logging
from typing import List, Dict, Any, Optional
from .diagnostics import DiagnosticResult, ProblemCategory, diagnostics
from ..config.manager import config_manager

class AutoFixer:
    \"\"\"自动修复系统\"\"\"
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.fixers = {
            ProblemCategory.CONFIGURATION: self._fix_configuration,
            ProblemCategory.PERFORMANCE: self._fix_performance,
            ProblemCategory.RESOURCE_EXHAUSTION: self._fix_resources,
        }
    
    async def attempt_fixes(self, results: List[DiagnosticResult]) -> Dict[str, Any]:
        \"\"\"尝试自动修复问题\"\"\"
        fix_results = {
            "attempted": [],
            "successful": [],
            "failed": [],
            "skipped": []
        }
        
        for result in results:
            if not result.auto_fixable:
                fix_results["skipped"].append({
                    "category": result.category.value,
                    "reason": "Not auto-fixable"
                })
                continue
            
            fix_results["attempted"].append(result.category.value)
            
            if result.category in self.fixers:
                try:
                    success = await self.fixers[result.category](result)
                    if success:
                        fix_results["successful"].append(result.category.value)
                        self.logger.info(f"Successfully fixed {result.category.value}")
                    else:
                        fix_results["failed"].append({
                            "category": result.category.value,
                            "reason": "Fix attempt unsuccessful"
                        })
                except Exception as e:
                    fix_results["failed"].append({
                        "category": result.category.value,
                        "reason": str(e)
                    })
                    self.logger.error(f"Fix failed for {result.category.value}: {str(e)}")
            else:
                fix_results["skipped"].append({
                    "category": result.category.value,
                    "reason": "No fixer available"
                })
        
        return fix_results
    
    async def _fix_configuration(self, result: DiagnosticResult) -> bool:
        \"\"\"修复配置问题\"\"\"
        issues = result.details.get("issues", [])
        fixed_count = 0
        
        for issue in issues:
            if "Invalid api_timeout" in issue:
                # 重置为默认值
                config_manager.update_config("api_timeout", 30, "auto_fix")
                fixed_count += 1
            elif "Invalid cache_ttl" in issue:
                config_manager.update_config("cache_ttl", 300, "auto_fix")
                fixed_count += 1
            elif "Invalid max_concurrent_requests" in issue:
                config_manager.update_config("max_concurrent_requests", 10, "auto_fix")
                fixed_count += 1
        
        return fixed_count > 0
    
    async def _fix_performance(self, result: DiagnosticResult) -> bool:
        \"\"\"修复性能问题\"\"\"
        issues = result.details.get("issues", [])
        fixed_count = 0
        
        for issue in issues:
            if "Slow tool calls" in issue:
                # 启用缓存
                config_manager.update_config("cache_enabled", True, "auto_fix")
                # 减少超时时间
                config_manager.update_config("api_timeout", 15, "auto_fix")
                fixed_count += 1
            elif "High tool failure rate" in issue:
                # 增加重试次数
                config_manager.update_config("api_retry_count", 3, "auto_fix")
                fixed_count += 1
        
        return fixed_count > 0
    
    async def _fix_resources(self, result: DiagnosticResult) -> bool:
        \"\"\"修复资源问题\"\"\"
        issues = result.details.get("issues", [])
        fixed_count = 0
        
        for issue in issues:
            if "High memory usage" in issue:
                # 清理缓存
                from .debugger import debugger
                debugger.traces.clear()
                # 启用更积极的缓存清理
                config_manager.update_config("cache_max_size", 100, "auto_fix")
                fixed_count += 1
            elif "High CPU usage" in issue:
                # 降低并发请求数
                current = config_manager.get_config().get("max_concurrent_requests", 10)
                new_value = max(1, current // 2)
                config_manager.update_config("max_concurrent_requests", new_value, "auto_fix")
                fixed_count += 1
        
        return fixed_count > 0
```

#### 3.3 诊断命令行工具

**诊断CLI工具**
```python
# src/{project_name}/debug/diagnostic_cli.py
import click
import asyncio
import json
from .diagnostics import diagnostics
from .auto_fix import AutoFixer

@click.group()
def diagnostic_cli():
    \"\"\"MCP Server诊断命令行工具\"\"\"
    pass

@diagnostic_cli.command()
@click.option('--format', default='text', type=click.Choice(['text', 'json']), help='Output format')
@click.option('--category', help='Filter by problem category')
def diagnose(format: str, category: str):
    \"\"\"运行系统诊断\"\"\"
    async def run_diagnosis():
        results = await diagnostics.run_full_diagnosis()
        
        if category:
            results = [r for r in results if r.category.value == category]
        
        if format == 'json':
            json_results = []
            for result in results:
                json_results.append({
                    "category": result.category.value,
                    "severity": result.severity,
                    "description": result.description,
                    "details": result.details,
                    "recommendations": result.recommendations,
                    "auto_fixable": result.auto_fixable
                })
            print(json.dumps(json_results, indent=2))
        else:
            report = diagnostics.generate_report(results)
            print(report)
    
    asyncio.run(run_diagnosis())

@diagnostic_cli.command()
@click.option('--dry-run', is_flag=True, help='Show what would be fixed without making changes')
def fix(dry_run: bool):
    \"\"\"自动修复检测到的问题\"\"\"
    async def run_fix():
        # 先运行诊断
        results = await diagnostics.run_full_diagnosis()
        fixable_results = [r for r in results if r.auto_fixable]
        
        if not fixable_results:
            print("No auto-fixable issues found.")
            return
        
        if dry_run:
            print("Dry run - would attempt to fix:")
            for result in fixable_results:
                print(f"- {result.category.value}: {result.description}")
            return
        
        # 尝试修复
        fixer = AutoFixer()
        fix_results = await fixer.attempt_fixes(fixable_results)
        
        print("Fix Results:")
        print(f"  Attempted: {len(fix_results['attempted'])}")
        print(f"  Successful: {len(fix_results['successful'])}")
        print(f"  Failed: {len(fix_results['failed'])}")
        print(f"  Skipped: {len(fix_results['skipped'])}")
        
        if fix_results['successful']:
            print("\\nSuccessfully fixed:")
            for category in fix_results['successful']:
                print(f"  - {category}")
        
        if fix_results['failed']:
            print("\\nFailed to fix:")
            for failed in fix_results['failed']:
                print(f"  - {failed['category']}: {failed['reason']}")
    
    asyncio.run(run_fix())

@diagnostic_cli.command()
def health():
    \"\"\"快速健康检查\"\"\"
    async def run_health_check():
        # 运行简化的健康检查
        config = config_manager.get_config()
        
        # 基本配置检查
        required = ['api_base_url', 'api_key']
        missing = [key for key in required if not config.get(key)]
        
        if missing:
            print(f"❌ Health check failed - missing: {', '.join(missing)}")
            return
        
        # 连接检查
        connectivity_result = await diagnostics._diagnose_connectivity()
        if connectivity_result:
            print("❌ Health check failed - connectivity issues")
            print(connectivity_result.description)
            return
        
        print("✅ Health check passed - system is operational")
    
    asyncio.run(run_health_check())

if __name__ == '__main__':
    diagnostic_cli()
```

### 验证标准
- [ ] 诊断系统能正确识别问题
- [ ] 自动修复功能按预期工作
- [ ] 诊断报告格式清晰易读
- [ ] 命令行工具功能完整

### 输出结果
```
问题诊断系统包含：
1. 全面的诊断框架 (MCPDiagnostics)
2. 自动修复系统 (AutoFixer)
3. 结构化的问题报告
4. 命令行诊断工具
```

---

## 完成检查清单

### 配置管理
- [ ] **分层配置**：优先级正确，覆盖机制有效
- [ ] **环境适配**：不同环境配置正确加载
- [ ] **验证机制**：配置完整性和有效性验证
- [ ] **运行时更新**：动态配置更新功能

### 调试能力
- [ ] **追踪系统**：函数调用和性能数据收集
- [ ] **实时接口**：调试API和状态查询
- [ ] **分析工具**：性能分析和数据导出
- [ ] **命令行**：便捷的调试命令支持

### 问题诊断
- [ ] **自动诊断**：问题自动识别和分类
- [ ] **修复建议**：明确的解决方案指导
- [ ] **自动修复**：可修复问题的自动处理
- [ ] **报告生成**：结构化的诊断报告

## 下一步行动

✅ **配置调试完成后，请继续：**
- 文档生成 → `guides/documentation-gen.md`
- 最佳实践 → `guides/best-practices.md`
- 安全指南 → `guides/security-guidelines.md`
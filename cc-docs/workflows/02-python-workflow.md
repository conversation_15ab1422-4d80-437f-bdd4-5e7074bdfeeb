# MCP Server Python 开发工作流程

## 任务概述
指导AI助手使用Python和mcp SDK完成功能完整的MCP Server开发。

---

## 第1步：MCP Server 核心类实现

### 输入要求
- 已完成的项目初始化（参考 `01-project-setup.md`）
- 明确的功能需求清单

### 执行步骤

#### 1.1 创建主服务器类

**文件：`src/{project_name}/server.py`**
```python
import asyncio
import logging
from typing import Any, Sequence
from mcp import McpServer, NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.server.session import ServerSession
from mcp.types import (
    CallToolRequest, CallToolResult,
    GetPromptRequest, GetPromptResult, 
    ListPromptsRequest, ListPromptsResult,
    ListResourcesRequest, ListResourcesResult,
    ListToolsRequest, ListToolsResult,
    ReadResourceRequest, ReadResourceResult
)

from .config.settings import Settings
from .tools import ToolManager
from .prompts import PromptManager
from .resources import ResourceManager

class {ClassName}Server:
    def __init__(self):
        self.settings = Settings()
        self.server = McpServer("{project_name}")
        self.tool_manager = ToolManager(self.settings)
        self.prompt_manager = PromptManager(self.settings)
        self.resource_manager = ResourceManager(self.settings)
        
        self._setup_handlers()
    
    def _setup_handlers(self):
        \"\"\"设置MCP协议处理程序\"\"\"
        
        @self.server.list_tools()
        async def handle_list_tools() -> ListToolsResult:
            return ListToolsResult(tools=self.tool_manager.list_tools())
        
        @self.server.call_tool()
        async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
            return await self.tool_manager.call_tool(request)
        
        @self.server.list_prompts()
        async def handle_list_prompts() -> ListPromptsResult:
            return ListPromptsResult(prompts=self.prompt_manager.list_prompts())
        
        @self.server.get_prompt()
        async def handle_get_prompt(request: GetPromptRequest) -> GetPromptResult:
            return await self.prompt_manager.get_prompt(request)
        
        @self.server.list_resources()
        async def handle_list_resources() -> ListResourcesRequest:
            return ListResourcesResult(resources=self.resource_manager.list_resources())
        
        @self.server.read_resource()
        async def handle_read_resource(request: ReadResourceRequest) -> ReadResourceResult:
            return await self.resource_manager.read_resource(request)

    async def run(self):
        \"\"\"运行MCP服务器\"\"\"
        async with self.server.run_stdio() as streams:
            await self.server.run(
                streams[0], streams[1], 
                InitializationOptions(
                    server_name="{project_name}",
                    server_version="0.1.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={}
                    )
                )
            )

def main():
    \"\"\"主入口函数\"\"\"
    logging.basicConfig(level=logging.INFO)
    server = {ClassName}Server()
    asyncio.run(server.run())

if __name__ == "__main__":
    main()
```

#### 1.2 创建配置管理模块

**文件：`src/{project_name}/config/settings.py`**
```python
import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    \"\"\"应用配置设置\"\"\"
    
    # 基础配置
    app_name: str = Field(default="{project_name}", env="APP_NAME")
    debug: bool = Field(default=False, env="DEBUG")
    
    # API配置
    api_base_url: str = Field(..., env="API_BASE_URL")
    api_key: Optional[str] = Field(default=None, env="API_KEY")
    api_timeout: int = Field(default=30, env="API_TIMEOUT")
    
    # 缓存配置
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_ttl: int = Field(default=300, env="CACHE_TTL")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
```

### 验证标准
- [ ] 服务器类可以正常实例化
- [ ] 所有MCP协议处理程序已注册
- [ ] 配置管理模块可以正确加载环境变量

### 输出结果
```
MCP Server核心框架包含：
1. 主服务器类 (server.py)
2. 配置管理 (config/settings.py)
3. 基础的协议处理程序
4. 环境变量支持
```

---

## 第2步：工具(Tools)管理器实现

### 输入要求
- 完成的服务器核心类
- 工具功能需求清单

### 执行步骤

#### 2.1 创建工具管理器基类

**文件：`src/{project_name}/tools/__init__.py`**
```python
from typing import List, Dict, Any
from mcp.types import Tool, CallToolRequest, CallToolResult, TextContent
from ..config.settings import Settings

class ToolManager:
    \"\"\"工具管理器\"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._tools: Dict[str, Tool] = {}
        self._handlers: Dict[str, callable] = {}
        self._register_tools()
    
    def _register_tools(self):
        \"\"\"注册所有工具\"\"\"
        # 这里将注册具体的工具实现
        pass
    
    def register_tool(self, tool: Tool, handler: callable):
        \"\"\"注册单个工具\"\"\"
        self._tools[tool.name] = tool
        self._handlers[tool.name] = handler
    
    def list_tools(self) -> List[Tool]:
        \"\"\"返回所有可用工具\"\"\"
        return list(self._tools.values())
    
    async def call_tool(self, request: CallToolRequest) -> CallToolResult:
        \"\"\"调用工具\"\"\"
        if request.params.name not in self._handlers:
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Unknown tool: {request.params.name}"
                )],
                isError=True
            )
        
        try:
            handler = self._handlers[request.params.name]
            result = await handler(request.params.arguments or {})
            
            return CallToolResult(
                content=[TextContent(
                    type="text", 
                    text=str(result)
                )]
            )
        except Exception as e:
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Tool execution error: {str(e)}"
                )],
                isError=True
            )
```

#### 2.2 创建具体工具实现模板

**文件：`src/{project_name}/tools/base.py`**
```python
import httpx
from typing import Dict, Any, Optional
from mcp.types import Tool
from ..config.settings import Settings

class BaseTool:
    \"\"\"工具基类\"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client = httpx.AsyncClient(
            base_url=settings.api_base_url,
            timeout=settings.api_timeout,
            headers=self._get_headers()
        )
    
    def _get_headers(self) -> Dict[str, str]:
        \"\"\"获取API请求头\"\"\"
        headers = {"Content-Type": "application/json"}
        if self.settings.api_key:
            headers["Authorization"] = f"Bearer {self.settings.api_key}"
        return headers
    
    async def make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        \"\"\"发起API请求\"\"\"
        try:
            response = await self.client.request(
                method=method,
                url=endpoint,
                json=data
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            raise Exception(f"API request failed: {str(e)}")
    
    def get_tool_definition(self) -> Tool:
        \"\"\"返回工具定义\"\"\"
        raise NotImplementedError("Subclasses must implement get_tool_definition")
    
    async def execute(self, arguments: Dict[str, Any]) -> Any:
        \"\"\"执行工具逻辑\"\"\"
        raise NotImplementedError("Subclasses must implement execute")

# 示例工具实现
class ExampleTool(BaseTool):
    \"\"\"示例工具\"\"\"
    
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="example_tool",
            description="An example tool that demonstrates the structure",
            inputSchema={
                "type": "object",
                "properties": {
                    "param1": {
                        "type": "string",
                        "description": "First parameter"
                    },
                    "param2": {
                        "type": "integer", 
                        "description": "Second parameter",
                        "default": 10
                    }
                },
                "required": ["param1"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        param1 = arguments.get("param1")
        param2 = arguments.get("param2", 10)
        
        # 实际的工具逻辑
        result = await self.make_request(
            "GET", 
            f"/api/example?param1={param1}&param2={param2}"
        )
        
        return {
            "status": "success",
            "data": result,
            "message": f"Processed {param1} with {param2}"
        }
```

#### 2.3 更新工具管理器注册逻辑

**更新 `src/{project_name}/tools/__init__.py`**
```python
# 在 _register_tools 方法中添加：
def _register_tools(self):
    \"\"\"注册所有工具\"\"\"
    from .base import ExampleTool
    
    # 注册示例工具
    example_tool = ExampleTool(self.settings)
    self.register_tool(
        example_tool.get_tool_definition(),
        example_tool.execute
    )
    
    # TODO: 在这里添加更多工具注册
```

### 验证标准
- [ ] 工具管理器可以正确注册和列出工具
- [ ] 基础工具类提供完整的API请求封装
- [ ] 示例工具可以正常执行

### 输出结果
```
工具管理系统包含：
1. 工具管理器类 (tools/__init__.py)
2. 基础工具类 (tools/base.py)
3. 示例工具实现
4. 统一的错误处理机制
```

---

## 第3步：提示词(Prompts)管理器实现

### 输入要求
- 完成的工具管理器
- 提示词需求清单

### 执行步骤

#### 3.1 创建提示词管理器

**文件：`src/{project_name}/prompts/__init__.py`**
```python
from typing import List, Dict, Any, Optional
from mcp.types import (
    Prompt, PromptMessage, PromptArgument,
    GetPromptRequest, GetPromptResult
)
from ..config.settings import Settings

class PromptManager:
    \"\"\"提示词管理器\"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._prompts: Dict[str, Prompt] = {}
        self._templates: Dict[str, callable] = {}
        self._register_prompts()
    
    def _register_prompts(self):
        \"\"\"注册所有提示词\"\"\"
        # 这里将注册具体的提示词实现
        pass
    
    def register_prompt(self, prompt: Prompt, template_func: callable):
        \"\"\"注册单个提示词\"\"\"
        self._prompts[prompt.name] = prompt
        self._templates[prompt.name] = template_func
    
    def list_prompts(self) -> List[Prompt]:
        \"\"\"返回所有可用提示词\"\"\"
        return list(self._prompts.values())
    
    async def get_prompt(self, request: GetPromptRequest) -> GetPromptResult:
        \"\"\"获取提示词内容\"\"\"
        prompt_name = request.name
        
        if prompt_name not in self._templates:
            raise ValueError(f"Unknown prompt: {prompt_name}")
        
        try:
            template_func = self._templates[prompt_name]
            messages = await template_func(request.arguments or {})
            
            return GetPromptResult(
                description=self._prompts[prompt_name].description,
                messages=messages
            )
        except Exception as e:
            raise Exception(f"Prompt generation error: {str(e)}")
```

#### 3.2 创建提示词基类和示例

**文件：`src/{project_name}/prompts/base.py`**
```python
from typing import List, Dict, Any
from mcp.types import Prompt, PromptMessage, PromptArgument, TextContent
from ..config.settings import Settings

class BasePrompt:
    \"\"\"提示词基类\"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
    
    def get_prompt_definition(self) -> Prompt:
        \"\"\"返回提示词定义\"\"\"
        raise NotImplementedError("Subclasses must implement get_prompt_definition")
    
    async def generate_messages(self, arguments: Dict[str, Any]) -> List[PromptMessage]:
        \"\"\"生成提示词消息\"\"\"
        raise NotImplementedError("Subclasses must implement generate_messages")

class ExamplePrompt(BasePrompt):
    \"\"\"示例提示词\"\"\"
    
    def get_prompt_definition(self) -> Prompt:
        return Prompt(
            name="example_prompt",
            description="An example prompt template",
            arguments=[
                PromptArgument(
                    name="topic",
                    description="The topic to generate content about",
                    required=True
                ),
                PromptArgument(
                    name="style",
                    description="The writing style (formal, casual, technical)",
                    required=False
                )
            ]
        )
    
    async def generate_messages(self, arguments: Dict[str, Any]) -> List[PromptMessage]:
        topic = arguments.get("topic", "")
        style = arguments.get("style", "casual")
        
        if not topic:
            raise ValueError("Topic is required")
        
        # 构建系统提示词
        system_message = PromptMessage(
            role="system",
            content=TextContent(
                type="text",
                text=f"You are a {style} writer. Please provide helpful information about the given topic."
            )
        )
        
        # 构建用户提示词  
        user_message = PromptMessage(
            role="user",
            content=TextContent(
                type="text",
                text=f"Please write about: {topic}"
            )
        )
        
        return [system_message, user_message]

class FormattedOutputPrompt(BasePrompt):
    \"\"\"格式化输出提示词\"\"\"
    
    def get_prompt_definition(self) -> Prompt:
        return Prompt(
            name="formatted_output",
            description="Generate formatted output in specified format",
            arguments=[
                PromptArgument(
                    name="data",
                    description="The data to format",
                    required=True
                ),
                PromptArgument(
                    name="format",
                    description="Output format (json, table, list)",
                    required=True
                )
            ]
        )
    
    async def generate_messages(self, arguments: Dict[str, Any]) -> List[PromptMessage]:
        data = arguments.get("data", "")
        format_type = arguments.get("format", "json")
        
        format_instructions = {
            "json": "Format the output as valid JSON with proper structure and indentation.",
            "table": "Format the output as a clean, readable table with headers and aligned columns.",
            "list": "Format the output as a numbered or bulleted list with clear hierarchy."
        }
        
        instruction = format_instructions.get(format_type, format_instructions["json"])
        
        system_message = PromptMessage(
            role="system",
            content=TextContent(
                type="text",
                text=f"You are a data formatter. {instruction}"
            )
        )
        
        user_message = PromptMessage(
            role="user", 
            content=TextContent(
                type="text",
                text=f"Please format this data: {data}"
            )
        )
        
        return [system_message, user_message]
```

#### 3.3 更新提示词管理器注册逻辑

**更新 `src/{project_name}/prompts/__init__.py`**
```python
# 在 _register_prompts 方法中添加：
def _register_prompts(self):
    \"\"\"注册所有提示词\"\"\"
    from .base import ExamplePrompt, FormattedOutputPrompt
    
    # 注册示例提示词
    example_prompt = ExamplePrompt(self.settings)
    self.register_prompt(
        example_prompt.get_prompt_definition(),
        example_prompt.generate_messages
    )
    
    # 注册格式化输出提示词
    formatted_prompt = FormattedOutputPrompt(self.settings)
    self.register_prompt(
        formatted_prompt.get_prompt_definition(),
        formatted_prompt.generate_messages
    )
    
    # TODO: 在这里添加更多提示词注册
```

### 验证标准
- [ ] 提示词管理器可以正确注册和列出提示词
- [ ] 基础提示词类提供完整的消息生成框架
- [ ] 示例提示词可以正常生成消息

### 输出结果
```
提示词管理系统包含：
1. 提示词管理器类 (prompts/__init__.py)
2. 基础提示词类 (prompts/base.py)
3. 示例提示词实现
4. 灵活的参数处理机制
```

---

## 第4步：资源(Resources)管理器实现

### 输入要求
- 完成的提示词管理器
- 资源访问需求清单

### 执行步骤

#### 4.1 创建资源管理器

**文件：`src/{project_name}/resources/__init__.py`**
```python
from typing import List, Dict, Any, Optional
from mcp.types import (
    Resource, ResourceContents,
    ReadResourceRequest, ReadResourceResult,
    TextResourceContents, BlobResourceContents
)
from ..config.settings import Settings

class ResourceManager:
    \"\"\"资源管理器\"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._resources: Dict[str, Resource] = {}
        self._readers: Dict[str, callable] = {}
        self._register_resources()
    
    def _register_resources(self):
        \"\"\"注册所有资源\"\"\"
        # 这里将注册具体的资源实现
        pass
    
    def register_resource(self, resource: Resource, reader_func: callable):
        \"\"\"注册单个资源\"\"\"
        self._resources[resource.uri] = resource
        self._readers[resource.uri] = reader_func
    
    def list_resources(self) -> List[Resource]:
        \"\"\"返回所有可用资源\"\"\"
        return list(self._resources.values())
    
    async def read_resource(self, request: ReadResourceRequest) -> ReadResourceResult:
        \"\"\"读取资源内容\"\"\"
        uri = request.uri
        
        if uri not in self._readers:
            raise ValueError(f"Unknown resource: {uri}")
        
        try:
            reader_func = self._readers[uri]
            contents = await reader_func()
            
            return ReadResourceResult(contents=[contents])
        except Exception as e:
            raise Exception(f"Resource read error: {str(e)}")
```

#### 4.2 创建资源基类和实现

**文件：`src/{project_name}/resources/base.py`**
```python
import json
from typing import Dict, Any, Optional
from mcp.types import Resource, TextResourceContents, BlobResourceContents
from ..config.settings import Settings

class BaseResource:
    \"\"\"资源基类\"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
    
    def get_resource_definition(self) -> Resource:
        \"\"\"返回资源定义\"\"\"
        raise NotImplementedError("Subclasses must implement get_resource_definition")
    
    async def read_content(self) -> TextResourceContents | BlobResourceContents:
        \"\"\"读取资源内容\"\"\"
        raise NotImplementedError("Subclasses must implement read_content")

class ConfigResource(BaseResource):
    \"\"\"配置资源\"\"\"
    
    def get_resource_definition(self) -> Resource:
        return Resource(
            uri="config://settings",
            name="Application Settings",
            description="Current application configuration",
            mimeType="application/json"
        )
    
    async def read_content(self) -> TextResourceContents:
        config_data = {
            "app_name": self.settings.app_name,
            "api_base_url": self.settings.api_base_url,
            "cache_enabled": self.settings.cache_enabled,
            "cache_ttl": self.settings.cache_ttl,
            "log_level": self.settings.log_level
        }
        
        return TextResourceContents(
            uri="config://settings",
            mimeType="application/json",
            text=json.dumps(config_data, indent=2)
        )

class ApiDocsResource(BaseResource):
    \"\"\"API文档资源\"\"\"
    
    def get_resource_definition(self) -> Resource:
        return Resource(
            uri="docs://api",
            name="API Documentation", 
            description="Available API endpoints and their usage",
            mimeType="text/markdown"
        )
    
    async def read_content(self) -> TextResourceContents:
        # 动态生成API文档
        docs = self._generate_api_docs()
        
        return TextResourceContents(
            uri="docs://api",
            mimeType="text/markdown",
            text=docs
        )
    
    def _generate_api_docs(self) -> str:
        return f\"\"\"# {self.settings.app_name} API Documentation

## Base URL
`{self.settings.api_base_url}`

## Authentication
API Key required in Authorization header: `Bearer <api_key>`

## Available Endpoints

### GET /api/status
Check API status

**Response:**
```json
{{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00Z"
}}
```

### POST /api/data
Submit data for processing

**Request Body:**
```json
{{
  "data": "your_data_here",
  "options": {{}}
}}
```

**Response:**
```json
{{
  "result": "processed_data",
  "id": "unique_id"
}}
```

## Rate Limits
- 100 requests per minute per API key
- Timeout: {self.settings.api_timeout} seconds

## Error Codes
- 400: Bad Request
- 401: Unauthorized  
- 429: Rate Limited
- 500: Internal Server Error
\"\"\"

class DataResource(BaseResource):
    \"\"\"数据资源\"\"\"
    
    def __init__(self, settings: Settings, data_type: str):
        super().__init__(settings)
        self.data_type = data_type
    
    def get_resource_definition(self) -> Resource:
        return Resource(
            uri=f"data://{self.data_type}",
            name=f"{self.data_type.title()} Data",
            description=f"Access to {self.data_type} data",
            mimeType="application/json"
        )
    
    async def read_content(self) -> TextResourceContents:
        # 这里应该实现实际的数据获取逻辑
        data = await self._fetch_data()
        
        return TextResourceContents(
            uri=f"data://{self.data_type}",
            mimeType="application/json", 
            text=json.dumps(data, indent=2)
        )
    
    async def _fetch_data(self) -> Dict[str, Any]:
        # 模拟数据获取
        return {
            "type": self.data_type,
            "timestamp": "2024-01-01T00:00:00Z",
            "items": [
                {"id": 1, "name": f"Sample {self.data_type} 1"},
                {"id": 2, "name": f"Sample {self.data_type} 2"}
            ]
        }
```

#### 4.3 更新资源管理器注册逻辑

**更新 `src/{project_name}/resources/__init__.py`**
```python
# 在 _register_resources 方法中添加：
def _register_resources(self):
    \"\"\"注册所有资源\"\"\"
    from .base import ConfigResource, ApiDocsResource, DataResource
    
    # 注册配置资源
    config_resource = ConfigResource(self.settings)
    self.register_resource(
        config_resource.get_resource_definition(),
        config_resource.read_content
    )
    
    # 注册API文档资源
    docs_resource = ApiDocsResource(self.settings)
    self.register_resource(
        docs_resource.get_resource_definition(),
        docs_resource.read_content
    )
    
    # 注册数据资源
    for data_type in ["users", "projects", "reports"]:
        data_resource = DataResource(self.settings, data_type)
        self.register_resource(
            data_resource.get_resource_definition(),
            data_resource.read_content
        )
    
    # TODO: 在这里添加更多资源注册
```

### 验证标准
- [ ] 资源管理器可以正确注册和列出资源
- [ ] 基础资源类提供完整的内容读取框架
- [ ] 示例资源可以正常返回内容

### 输出结果
```
资源管理系统包含：
1. 资源管理器类 (resources/__init__.py)
2. 基础资源类 (resources/base.py)
3. 配置、文档、数据资源实现
4. 灵活的内容类型支持
```

---

## 第5步：错误处理和日志系统

### 输入要求
- 完成的资源管理器
- 错误处理需求

### 执行步骤

#### 5.1 创建通用工具模块

**文件：`src/{project_name}/utils/common.py`**
```python
import logging
import functools
from typing import Any, Callable, Dict
from mcp.types import CallToolResult, TextContent

def setup_logging(level: str = "INFO"):
    \"\"\"设置日志配置\"\"\"
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('mcp_server.log')
        ]
    )

def handle_errors(func: Callable) -> Callable:
    \"\"\"错误处理装饰器\"\"\"
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logging.error(f"Error in {func.__name__}: {str(e)}", exc_info=True)
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error: {str(e)}"
                )],
                isError=True
            )
    return wrapper

def validate_arguments(schema: Dict[str, Any]) -> Callable:
    \"\"\"参数验证装饰器\"\"\"
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(arguments: Dict[str, Any], *args, **kwargs):
            # 简单的参数验证逻辑
            required = schema.get("required", [])
            properties = schema.get("properties", {})
            
            # 检查必需参数
            for req_param in required:
                if req_param not in arguments:
                    raise ValueError(f"Missing required parameter: {req_param}")
            
            # 检查参数类型（简化版本）
            for param, value in arguments.items():
                if param in properties:
                    expected_type = properties[param].get("type")
                    if expected_type == "string" and not isinstance(value, str):
                        raise ValueError(f"Parameter {param} must be a string")
                    elif expected_type == "integer" and not isinstance(value, int):
                        raise ValueError(f"Parameter {param} must be an integer")
            
            return await func(arguments, *args, **kwargs)
        return wrapper
    return decorator

class MCPError(Exception):
    \"\"\"MCP特定错误\"\"\"
    pass

class AuthenticationError(MCPError):
    \"\"\"认证错误\"\"\"
    pass

class RateLimitError(MCPError):
    \"\"\"速率限制错误\"\"\"
    pass

class ResourceNotFoundError(MCPError):
    \"\"\"资源未找到错误\"\"\"
    pass
```

#### 5.2 更新服务器类集成错误处理

**更新 `src/{project_name}/server.py`**
```python
# 在导入部分添加：
from .utils.common import setup_logging, MCPError

# 在 __init__ 方法中添加：
def __init__(self):
    self.settings = Settings()
    setup_logging(self.settings.log_level)
    self.logger = logging.getLogger(__name__)
    
    # ... 其余初始化代码

# 更新处理程序以包含错误处理：
@self.server.call_tool()
async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
    try:
        return await self.tool_manager.call_tool(request)
    except MCPError as e:
        self.logger.error(f"MCP error in tool call: {str(e)}")
        return CallToolResult(
            content=[TextContent(type="text", text=str(e))],
            isError=True
        )
    except Exception as e:
        self.logger.error(f"Unexpected error in tool call: {str(e)}", exc_info=True)
        return CallToolResult(
            content=[TextContent(type="text", text="Internal server error")],
            isError=True
        )
```

### 验证标准
- [ ] 日志系统正确配置并输出到文件和控制台
- [ ] 错误处理装饰器可以捕获和格式化异常
- [ ] 参数验证装饰器可以正确验证输入

### 输出结果
```
错误处理和日志系统包含：
1. 通用工具模块 (utils/common.py)
2. 装饰器支持的错误处理
3. 结构化的异常类型
4. 完整的日志配置
```

---

## 第6步：测试和验证

### 输入要求
- 完整的MCP Server实现
- 测试用例需求

### 执行步骤

#### 6.1 创建测试基础设施

**文件：`tests/conftest.py`**
```python
import pytest
import asyncio
from src.{project_name}.config.settings import Settings
from src.{project_name}.server import {ClassName}Server

@pytest.fixture
def settings():
    \"\"\"测试配置\"\"\"
    return Settings(
        api_base_url="https://api.example.com",
        api_key="test-key",
        debug=True,
        cache_enabled=False
    )

@pytest.fixture
async def server(settings):
    \"\"\"测试服务器实例\"\"\"
    server = {ClassName}Server()
    server.settings = settings
    return server

@pytest.fixture
def event_loop():
    \"\"\"事件循环\"\"\"
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()
```

#### 6.2 创建工具测试

**文件：`tests/test_tools.py`**
```python
import pytest
from mcp.types import CallToolRequest, Tool
from src.{project_name}.tools import ToolManager
from src.{project_name}.tools.base import ExampleTool

@pytest.mark.asyncio
async def test_tool_manager_initialization(settings):
    \"\"\"测试工具管理器初始化\"\"\"
    manager = ToolManager(settings)
    tools = manager.list_tools()
    assert len(tools) > 0
    assert isinstance(tools[0], Tool)

@pytest.mark.asyncio 
async def test_example_tool_execution(settings):
    \"\"\"测试示例工具执行\"\"\"
    tool = ExampleTool(settings)
    
    # 测试工具定义
    definition = tool.get_tool_definition()
    assert definition.name == "example_tool"
    assert "param1" in str(definition.inputSchema)
    
    # 测试工具执行（需要模拟API响应）
    # 这里需要使用mock来模拟API调用
    
@pytest.mark.asyncio
async def test_tool_error_handling(settings):
    \"\"\"测试工具错误处理\"\"\"
    manager = ToolManager(settings)
    
    # 测试不存在的工具
    request = CallToolRequest(
        params={"name": "nonexistent_tool", "arguments": {}}
    )
    result = await manager.call_tool(request)
    assert result.isError is True
```

#### 6.3 创建集成测试

**文件：`tests/test_integration.py`**
```python
import pytest
from mcp.types import (
    ListToolsRequest, ListPromptsRequest, ListResourcesRequest,
    CallToolRequest, GetPromptRequest, ReadResourceRequest
)

@pytest.mark.asyncio
async def test_server_initialization(server):
    \"\"\"测试服务器初始化\"\"\"
    assert server.tool_manager is not None
    assert server.prompt_manager is not None 
    assert server.resource_manager is not None

@pytest.mark.asyncio
async def test_full_mcp_workflow(server):
    \"\"\"测试完整的MCP工作流程\"\"\"
    
    # 测试工具列表
    tools = server.tool_manager.list_tools()
    assert len(tools) > 0
    
    # 测试提示词列表
    prompts = server.prompt_manager.list_prompts()
    assert len(prompts) > 0
    
    # 测试资源列表
    resources = server.resource_manager.list_resources()
    assert len(resources) > 0
    
    # 测试工具调用
    if tools:
        # 这里需要根据实际工具进行测试
        pass
    
    # 测试提示词获取
    if prompts:
        # 这里需要根据实际提示词进行测试
        pass
    
    # 测试资源读取
    if resources:
        # 这里需要根据实际资源进行测试
        pass
```

#### 6.4 创建性能测试

**文件：`tests/test_performance.py`**
```python
import pytest
import time
import asyncio

@pytest.mark.asyncio
async def test_tool_execution_performance(server):
    \"\"\"测试工具执行性能\"\"\"
    tools = server.tool_manager.list_tools()
    if not tools:
        pytest.skip("No tools available for testing")
    
    # 测试单次执行时间
    start_time = time.time()
    # 执行工具调用
    execution_time = time.time() - start_time
    
    assert execution_time < 5.0, "Tool execution too slow"

@pytest.mark.asyncio
async def test_concurrent_requests(server):
    \"\"\"测试并发请求处理\"\"\"
    async def make_request():
        return server.tool_manager.list_tools()
    
    # 并发执行10个请求
    tasks = [make_request() for _ in range(10)]
    results = await asyncio.gather(*tasks)
    
    assert len(results) == 10
    assert all(len(result) > 0 for result in results)
```

### 验证标准
- [ ] 所有测试用例通过
- [ ] 测试覆盖率达到80%以上
- [ ] 性能测试满足预期标准

### 输出结果
```
测试系统包含：
1. 测试基础设施 (tests/conftest.py)
2. 单元测试 (tests/test_tools.py等)
3. 集成测试 (tests/test_integration.py)
4. 性能测试 (tests/test_performance.py)
```

---

## 第7步：部署配置

### 输入要求
- 通过测试的MCP Server
- 部署环境要求

### 执行步骤

#### 7.1 创建部署脚本

**文件：`deploy.py`**
```python
#!/usr/bin/env python3
import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    \"\"\"检查部署要求\"\"\"
    print("Checking deployment requirements...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        raise Exception("Python 3.8+ required")
        
    # 检查必需文件
    required_files = [
        "requirements.txt",
        "src/{project_name}/server.py", 
        ".env.example"
    ]
    
    for file in required_files:
        if not Path(file).exists():
            raise Exception(f"Required file missing: {file}")
    
    print("✓ Requirements check passed")

def install_dependencies():
    \"\"\"安装依赖\"\"\"
    print("Installing dependencies...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
    print("✓ Dependencies installed")

def setup_environment():
    \"\"\"设置环境\"\"\"
    print("Setting up environment...")
    
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print("✓ .env file created from template")
            print("⚠ Please edit .env file with your configuration")
        else:
            print("⚠ .env.example not found, please create .env manually")

def run_tests():
    \"\"\"运行测试\"\"\"
    print("Running tests...")
    try:
        subprocess.run([sys.executable, "-m", "pytest", "tests/"], check=True)
        print("✓ All tests passed")
    except subprocess.CalledProcessError:
        print("✗ Tests failed")
        return False
    return True

def main():
    \"\"\"主部署流程\"\"\"
    try:
        check_requirements()
        install_dependencies()
        setup_environment()
        
        if run_tests():
            print("\\n🎉 Deployment completed successfully!")
            print("\\nNext steps:")
            print("1. Edit .env file with your configuration")
            print("2. Run: python -m src.{project_name}.server")
        else:
            print("\\n❌ Deployment failed - tests did not pass")
            sys.exit(1)
            
    except Exception as e:
        print(f"\\n❌ Deployment failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

#### 7.2 创建Docker支持（可选）

**文件：`Dockerfile`**
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY .env.example .env

# 创建非root用户
RUN useradd -m -u 1000 mcpuser && chown -R mcpuser:mcpuser /app
USER mcpuser

# 暴露端口（如果需要）
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD python -c "import src.{project_name}.server; print('OK')" || exit 1

# 启动命令
CMD ["python", "-m", "src.{project_name}.server"]
```

**文件：`docker-compose.yml`**
```yaml
version: '3.8'

services:
  mcp-server:
    build: .
    environment:
      - API_BASE_URL=\${API_BASE_URL}
      - API_KEY=\${API_KEY}
      - LOG_LEVEL=\${LOG_LEVEL:-INFO}
      - DEBUG=\${DEBUG:-false}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    
  # 可选：添加Redis缓存
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

#### 7.3 创建系统服务配置

**文件：`{project_name}.service`**
```ini
[Unit]
Description={project_name} MCP Server
After=network.target

[Service]
Type=simple
User=mcpuser
Group=mcpuser
WorkingDirectory=/opt/{project_name}
Environment=PATH=/opt/{project_name}/venv/bin
ExecStart=/opt/{project_name}/venv/bin/python -m src.{project_name}.server
Restart=always
RestartSec=10

# 安全设置
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/{project_name}/logs

[Install]
WantedBy=multi-user.target
```

### 验证标准
- [ ] 部署脚本可以成功执行
- [ ] Docker容器可以正确构建和运行
- [ ] 系统服务配置正确

### 输出结果
```
部署配置包含：
1. 自动化部署脚本 (deploy.py)
2. Docker容器化支持 (Dockerfile, docker-compose.yml)
3. 系统服务配置 ({project_name}.service)
4. 环境设置和依赖管理
```

---

## 完成检查清单

### 核心功能
- [ ] **MCP Server核心类**：正确实现协议处理程序
- [ ] **工具管理器**：支持工具注册、列出、调用
- [ ] **提示词管理器**：支持模板生成和参数处理
- [ ] **资源管理器**：支持多种资源类型和内容格式

### 质量保证
- [ ] **错误处理**：统一的异常处理和日志记录
- [ ] **参数验证**：输入参数的类型和必需性检查
- [ ] **测试覆盖**：单元测试、集成测试、性能测试
- [ ] **代码质量**：遵循Python最佳实践和MCP协议标准

### 部署就绪
- [ ] **环境配置**：完整的配置管理和环境变量支持
- [ ] **依赖管理**：明确的依赖列表和版本控制
- [ ] **部署自动化**：一键部署脚本和容器化支持
- [ ] **运维支持**：日志记录、健康检查、服务配置

## 下一步行动

✅ **Python开发完成后，请继续：**
- 集成测试 → `workflows/04-testing-deployment.md`
- 配置调试 → `workflows/05-configuration-debug.md`
- 文档生成 → `guides/documentation-gen.md`
# MCP 资源(Resources)开发模板

## 模板概述
本模板提供了开发MCP资源的标准化流程和代码框架，支持多种资源类型和数据格式。

---

## 通用开发流程

### 第1步：资源需求分析

#### 输入要求
- 资源数据源描述（文件、API、数据库等）
- 资源访问模式和频率
- 数据格式和结构要求

#### 分析步骤
```
1. 数据源分析：
   - 确定资源的数据来源
   - 分析数据获取方式
   - 评估数据更新频率

2. 访问模式设计：
   - 静态资源 vs 动态资源
   - 实时数据 vs 缓存数据
   - 单次获取 vs 流式传输

3. 格式标准化：
   - 选择合适的MIME类型
   - 定义数据结构和编码
   - 设计元数据信息
```

### 第2步：资源结构设计

#### URI 设计原则
```
资源URI遵循以下格式：
scheme://category/subcategory/identifier

示例：
- file://docs/readme.md
- api://users/profile/123
- config://database/connection
- cache://session/user_data
- stream://logs/application
```

#### 资源类型分类
```
1. 文本资源 (TextResourceContents)：
   - 配置文件、日志、文档
   - JSON、XML、YAML数据
   - 代码文件、脚本

2. 二进制资源 (BlobResourceContents)：
   - 图片、音频、视频文件
   - 压缩文件、可执行文件
   - 加密数据、证书文件
```

---

## Python 资源实现模板

### 基础资源类模板

```python
from typing import List, Dict, Any, Optional, Union
from mcp.types import Resource, TextResourceContents, BlobResourceContents, ResourceContents
import aiofiles
import json
import logging
from datetime import datetime
from ..config.settings import Settings
from ..utils.common import handle_errors

class {ResourceName}Resource:
    \"\"\"
    {资源功能描述}
    
    此资源提供：
    - 功能1描述
    - 功能2描述
    - 功能3描述
    
    支持的操作：
    - 读取操作
    - 缓存管理
    - 数据验证
    \"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self._cache: Dict[str, Any] = {}
        self._cache_ttl = settings.cache_ttl if settings.cache_enabled else 0
    
    def get_resource_definition(self) -> Resource:
        \"\"\"返回资源定义\"\"\"
        return Resource(
            uri="{resource_uri}",
            name="{资源显示名称}",
            description="{资源功能的详细描述}",
            mimeType="{mime_type}"  # text/plain, application/json, etc.
        )
    
    @handle_errors
    async def read_content(self) -> List[ResourceContents]:
        \"\"\"读取资源内容\"\"\"
        
        self.logger.info(f"Reading content for {self.__class__.__name__}")
        
        try:
            # 1. 检查缓存
            cached_content = await self._get_cached_content()
            if cached_content:
                self.logger.debug("Returning cached content")
                return cached_content
            
            # 2. 获取最新数据
            raw_data = await self._fetch_data()
            
            # 3. 处理和验证数据
            processed_data = await self._process_data(raw_data)
            
            # 4. 创建资源内容
            contents = await self._create_contents(processed_data)
            
            # 5. 缓存结果
            await self._cache_content(contents)
            
            self.logger.info(f"Successfully read {len(contents)} content items")
            return contents
            
        except Exception as e:
            self.logger.error(f"Error reading content: {str(e)}")
            raise
    
    async def _fetch_data(self) -> Any:
        \"\"\"获取原始数据\"\"\"
        # 子类需要实现具体的数据获取逻辑
        raise NotImplementedError("Subclasses must implement _fetch_data")
    
    async def _process_data(self, raw_data: Any) -> Any:
        \"\"\"处理原始数据\"\"\"
        # 默认不处理，直接返回
        return raw_data
    
    async def _create_contents(self, data: Any) -> List[ResourceContents]:
        \"\"\"创建资源内容\"\"\"
        # 子类需要实现具体的内容创建逻辑
        raise NotImplementedError("Subclasses must implement _create_contents")
    
    def _create_text_content(
        self, 
        uri: str, 
        text: str, 
        mime_type: str = "text/plain"
    ) -> TextResourceContents:
        \"\"\"创建文本内容\"\"\"
        return TextResourceContents(
            uri=uri,
            mimeType=mime_type,
            text=text
        )
    
    def _create_blob_content(
        self, 
        uri: str, 
        blob_data: bytes, 
        mime_type: str = "application/octet-stream"
    ) -> BlobResourceContents:
        \"\"\"创建二进制内容\"\"\"
        import base64
        return BlobResourceContents(
            uri=uri,
            mimeType=mime_type,
            blob=base64.b64encode(blob_data).decode('utf-8')
        )
    
    async def _get_cached_content(self) -> Optional[List[ResourceContents]]:
        \"\"\"获取缓存内容\"\"\"
        if not self.settings.cache_enabled:
            return None
        
        cache_key = f"{self.__class__.__name__}_content"
        if cache_key in self._cache:
            cached_item = self._cache[cache_key]
            if self._is_cache_valid(cached_item['timestamp']):
                return cached_item['content']
            else:
                del self._cache[cache_key]
        
        return None
    
    async def _cache_content(self, contents: List[ResourceContents]) -> None:
        \"\"\"缓存内容\"\"\"
        if not self.settings.cache_enabled:
            return
        
        cache_key = f"{self.__class__.__name__}_content"
        self._cache[cache_key] = {
            'content': contents,
            'timestamp': datetime.utcnow()
        }
    
    def _is_cache_valid(self, timestamp: datetime) -> bool:
        \"\"\"检查缓存是否有效\"\"\"
        if self._cache_ttl <= 0:
            return False
        
        age = (datetime.utcnow() - timestamp).total_seconds()
        return age < self._cache_ttl
    
    def _get_metadata(self) -> Dict[str, Any]:
        \"\"\"获取资源元数据\"\"\"
        return {
            "resource_type": self.__class__.__name__,
            "last_accessed": datetime.utcnow().isoformat() + "Z",
            "cache_enabled": self.settings.cache_enabled,
            "cache_ttl": self._cache_ttl
        }
```

### 专用资源类型模板

#### 文件系统资源
```python
import os
import aiofiles
from pathlib import Path

class FileSystemResource({ResourceName}Resource):
    \"\"\"文件系统资源\"\"\"
    
    def __init__(self, settings: Settings, base_path: str, allowed_extensions: List[str] = None):
        super().__init__(settings)
        self.base_path = Path(base_path)
        self.allowed_extensions = allowed_extensions or ['.txt', '.md', '.json', '.yaml', '.yml']
    
    def get_resource_definition(self) -> Resource:
        return Resource(
            uri=f"file://{self.base_path}",
            name=f"File System: {self.base_path.name}",
            description=f"Access to files in {self.base_path}",
            mimeType="text/plain"
        )
    
    async def _fetch_data(self) -> List[Path]:
        \"\"\"获取文件列表\"\"\"
        if not self.base_path.exists():
            raise FileNotFoundError(f"Base path does not exist: {self.base_path}")
        
        files = []
        for file_path in self.base_path.rglob("*"):
            if file_path.is_file() and self._is_allowed_file(file_path):
                files.append(file_path)
        
        return files
    
    async def _create_contents(self, files: List[Path]) -> List[ResourceContents]:
        \"\"\"创建文件内容\"\"\"
        contents = []
        
        for file_path in files:
            try:
                relative_path = file_path.relative_to(self.base_path)
                uri = f"file://{relative_path}"
                
                # 检查是否为二进制文件
                if self._is_binary_file(file_path):
                    # 读取二进制文件
                    async with aiofiles.open(file_path, 'rb') as f:
                        blob_data = await f.read()
                    
                    content = self._create_blob_content(
                        uri, 
                        blob_data, 
                        self._get_mime_type(file_path)
                    )
                else:
                    # 读取文本文件
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        text_data = await f.read()
                    
                    content = self._create_text_content(
                        uri, 
                        text_data, 
                        self._get_mime_type(file_path)
                    )
                
                contents.append(content)
                
            except Exception as e:
                self.logger.warning(f"Failed to read file {file_path}: {str(e)}")
                continue
        
        return contents
    
    def _is_allowed_file(self, file_path: Path) -> bool:
        \"\"\"检查文件是否允许访问\"\"\"
        return file_path.suffix.lower() in self.allowed_extensions
    
    def _is_binary_file(self, file_path: Path) -> bool:
        \"\"\"检查是否为二进制文件\"\"\"
        binary_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.zip', '.tar', '.gz']
        return file_path.suffix.lower() in binary_extensions
    
    def _get_mime_type(self, file_path: Path) -> str:
        \"\"\"获取文件MIME类型\"\"\"
        mime_types = {
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.json': 'application/json',
            '.yaml': 'application/yaml',
            '.yml': 'application/yaml',
            '.html': 'text/html',
            '.css': 'text/css',
            '.js': 'application/javascript',
            '.py': 'text/x-python',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.pdf': 'application/pdf'
        }
        
        return mime_types.get(file_path.suffix.lower(), 'application/octet-stream')
```

#### API数据资源
```python
import httpx

class ApiDataResource({ResourceName}Resource):
    \"\"\"API数据资源\"\"\"
    
    def __init__(self, settings: Settings, endpoint: str, data_key: str = None):
        super().__init__(settings)
        self.endpoint = endpoint
        self.data_key = data_key
        self.client = httpx.AsyncClient(
            base_url=settings.api_base_url,
            timeout=settings.api_timeout,
            headers=self._get_headers()
        )
    
    def get_resource_definition(self) -> Resource:
        return Resource(
            uri=f"api://{self.endpoint}",
            name=f"API Data: {self.endpoint}",
            description=f"Real-time data from {self.endpoint}",
            mimeType="application/json"
        )
    
    def _get_headers(self) -> Dict[str, str]:
        \"\"\"获取API请求头\"\"\"
        headers = {"Content-Type": "application/json"}
        if self.settings.api_key:
            headers["Authorization"] = f"Bearer {self.settings.api_key}"
        return headers
    
    async def _fetch_data(self) -> Dict[str, Any]:
        \"\"\"从API获取数据\"\"\"
        try:
            response = await self.client.get(self.endpoint)
            response.raise_for_status()
            data = response.json()
            
            # 如果指定了数据键，提取特定部分
            if self.data_key and self.data_key in data:
                return data[self.data_key]
            
            return data
            
        except httpx.HTTPError as e:
            raise Exception(f"API request failed: {str(e)}")
    
    async def _process_data(self, raw_data: Any) -> Dict[str, Any]:
        \"\"\"处理API数据\"\"\"
        processed_data = {
            "data": raw_data,
            "metadata": {
                "endpoint": self.endpoint,
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "data_type": type(raw_data).__name__,
                "record_count": len(raw_data) if isinstance(raw_data, (list, dict)) else 1
            }
        }
        
        return processed_data
    
    async def _create_contents(self, data: Dict[str, Any]) -> List[ResourceContents]:
        \"\"\"创建API数据内容\"\"\"
        content = self._create_text_content(
            f"api://{self.endpoint}",
            json.dumps(data, indent=2, ensure_ascii=False),
            "application/json"
        )
        
        return [content]
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
```

#### 配置资源
```python
class ConfigurationResource({ResourceName}Resource):
    \"\"\"配置资源\"\"\"
    
    def __init__(self, settings: Settings, config_sections: List[str] = None):
        super().__init__(settings)
        self.config_sections = config_sections or ['all']
    
    def get_resource_definition(self) -> Resource:
        return Resource(
            uri="config://application",
            name="Application Configuration",
            description="Current application configuration and settings",
            mimeType="application/json"
        )
    
    async def _fetch_data(self) -> Dict[str, Any]:
        \"\"\"获取配置数据\"\"\"
        config = {}
        
        if 'all' in self.config_sections or 'application' in self.config_sections:
            config['application'] = {
                'name': self.settings.app_name,
                'debug': self.settings.debug,
                'log_level': self.settings.log_level
            }
        
        if 'all' in self.config_sections or 'api' in self.config_sections:
            config['api'] = {
                'base_url': self.settings.api_base_url,
                'timeout': self.settings.api_timeout,
                'has_key': bool(self.settings.api_key)  # 不暴露实际密钥
            }
        
        if 'all' in self.config_sections or 'cache' in self.config_sections:
            config['cache'] = {
                'enabled': self.settings.cache_enabled,
                'ttl': self.settings.cache_ttl
            }
        
        return config
    
    async def _create_contents(self, config: Dict[str, Any]) -> List[ResourceContents]:
        \"\"\"创建配置内容\"\"\"
        contents = []
        
        # 创建完整配置内容
        full_config = self._create_text_content(
            "config://application/full",
            json.dumps(config, indent=2),
            "application/json"
        )
        contents.append(full_config)
        
        # 为每个配置节创建单独的内容
        for section, section_data in config.items():
            section_content = self._create_text_content(
                f"config://application/{section}",
                json.dumps(section_data, indent=2),
                "application/json"
            )
            contents.append(section_content)
        
        return contents
```

---

## TypeScript 资源实现模板

### 基础资源类模板

```typescript
import { 
  Resource, 
  ResourceContents, 
  TextResourceContents, 
  BlobResourceContents 
} from '@modelcontextprotocol/sdk/types.js';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { handleErrors, cached } from '../utils/common.js';

export interface ResourceMetadata {
  resourceType: string;
  lastAccessed: string;
  cacheEnabled: boolean;
  cacheTtl: number;
}

export abstract class BaseResource {
  protected cache: Map<string, { content: ResourceContents[]; timestamp: Date }> = new Map();

  constructor(protected settings: Settings) {}

  abstract getResourceDefinition(): Resource;
  
  protected abstract fetchData(): Promise<unknown>;
  
  protected abstract createContents(data: unknown): Promise<ResourceContents[]>;

  @handleErrors
  @cached(300000) // 5分钟缓存
  async readContent(): Promise<ResourceContents[]> {
    logger.info(`Reading content for ${this.constructor.name}`);

    try {
      // 1. 检查缓存
      const cachedContent = this.getCachedContent();
      if (cachedContent) {
        logger.debug('Returning cached content');
        return cachedContent;
      }

      // 2. 获取最新数据
      const rawData = await this.fetchData();

      // 3. 处理数据
      const processedData = await this.processData(rawData);

      // 4. 创建内容
      const contents = await this.createContents(processedData);

      // 5. 缓存结果
      this.cacheContent(contents);

      logger.info(`Successfully read ${contents.length} content items`);
      return contents;

    } catch (error) {
      logger.error(`Error reading content:`, error);
      throw error;
    }
  }

  protected async processData(rawData: unknown): Promise<unknown> {
    // 默认不处理，子类可以覆盖
    return rawData;
  }

  protected createTextContent(
    uri: string, 
    text: string, 
    mimeType: string = 'text/plain'
  ): TextResourceContents {
    return {
      uri,
      mimeType,
      text
    };
  }

  protected createBlobContent(
    uri: string, 
    blobData: Uint8Array, 
    mimeType: string = 'application/octet-stream'
  ): BlobResourceContents {
    const base64 = Buffer.from(blobData).toString('base64');
    return {
      uri,
      mimeType,
      blob: base64
    };
  }

  private getCachedContent(): ResourceContents[] | null {
    if (!this.settings.cacheEnabled) {
      return null;
    }

    const cacheKey = `${this.constructor.name}_content`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && this.isCacheValid(cached.timestamp)) {
      return cached.content;
    }

    if (cached) {
      this.cache.delete(cacheKey);
    }

    return null;
  }

  private cacheContent(contents: ResourceContents[]): void {
    if (!this.settings.cacheEnabled) {
      return;
    }

    const cacheKey = `${this.constructor.name}_content`;
    this.cache.set(cacheKey, {
      content: contents,
      timestamp: new Date()
    });
  }

  private isCacheValid(timestamp: Date): boolean {
    if (this.settings.cacheTtl <= 0) {
      return false;
    }

    const age = Date.now() - timestamp.getTime();
    return age < this.settings.cacheTtl * 1000;
  }

  protected getMetadata(): ResourceMetadata {
    return {
      resourceType: this.constructor.name,
      lastAccessed: new Date().toISOString(),
      cacheEnabled: this.settings.cacheEnabled,
      cacheTtl: this.settings.cacheTtl
    };
  }
}
```

### 专用资源类型模板

#### 文件系统资源
```typescript
import fs from 'fs/promises';
import path from 'path';

export class FileSystemResource extends BaseResource {
  constructor(
    settings: Settings,
    private basePath: string,
    private allowedExtensions: string[] = ['.txt', '.md', '.json', '.yaml', '.yml']
  ) {
    super(settings);
  }

  getResourceDefinition(): Resource {
    return {
      uri: `file://${this.basePath}`,
      name: `File System: ${path.basename(this.basePath)}`,
      description: `Access to files in ${this.basePath}`,
      mimeType: 'text/plain'
    };
  }

  protected async fetchData(): Promise<string[]> {
    const files: string[] = [];
    
    try {
      await this.collectFiles(this.basePath, files);
      return files;
    } catch (error) {
      throw new Error(`Failed to read directory ${this.basePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async collectFiles(dir: string, files: string[]): Promise<void> {
    const entries = await fs.readdir(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      if (entry.isDirectory()) {
        await this.collectFiles(fullPath, files);
      } else if (entry.isFile() && this.isAllowedFile(fullPath)) {
        files.push(fullPath);
      }
    }
  }

  protected async createContents(files: string[]): Promise<ResourceContents[]> {
    const contents: ResourceContents[] = [];

    for (const filePath of files) {
      try {
        const relativePath = path.relative(this.basePath, filePath);
        const uri = `file://${relativePath}`;

        if (this.isBinaryFile(filePath)) {
          // 读取二进制文件
          const blobData = await fs.readFile(filePath);
          const content = this.createBlobContent(
            uri,
            new Uint8Array(blobData),
            this.getMimeType(filePath)
          );
          contents.push(content);
        } else {
          // 读取文本文件
          const textData = await fs.readFile(filePath, 'utf-8');
          const content = this.createTextContent(
            uri,
            textData,
            this.getMimeType(filePath)
          );
          contents.push(content);
        }
      } catch (error) {
        logger.warn(`Failed to read file ${filePath}:`, error);
        continue;
      }
    }

    return contents;
  }

  private isAllowedFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return this.allowedExtensions.includes(ext);
  }

  private isBinaryFile(filePath: string): boolean {
    const binaryExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.zip', '.tar', '.gz'];
    const ext = path.extname(filePath).toLowerCase();
    return binaryExtensions.includes(ext);
  }

  private getMimeType(filePath: string): string {
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.json': 'application/json',
      '.yaml': 'application/yaml',
      '.yml': 'application/yaml',
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.ts': 'application/typescript',
      '.py': 'text/x-python',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.pdf': 'application/pdf'
    };

    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
  }
}
```

#### API数据资源
```typescript
import axios, { AxiosInstance } from 'axios';

export class ApiDataResource extends BaseResource {
  private client: AxiosInstance;

  constructor(
    settings: Settings,
    private endpoint: string,
    private dataKey?: string
  ) {
    super(settings);
    
    this.client = axios.create({
      baseURL: settings.apiBaseUrl,
      timeout: settings.apiTimeout,
      headers: this.getHeaders()
    });
  }

  getResourceDefinition(): Resource {
    return {
      uri: `api://${this.endpoint}`,
      name: `API Data: ${this.endpoint}`,
      description: `Real-time data from ${this.endpoint}`,
      mimeType: 'application/json'
    };
  }

  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.settings.apiKey) {
      headers['Authorization'] = `Bearer ${this.settings.apiKey}`;
    }

    return headers;
  }

  protected async fetchData(): Promise<unknown> {
    try {
      const response = await this.client.get(this.endpoint);
      let data = response.data;

      // 如果指定了数据键，提取特定部分
      if (this.dataKey && typeof data === 'object' && data !== null && this.dataKey in data) {
        data = (data as Record<string, unknown>)[this.dataKey];
      }

      return data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(`API request failed: ${error.response?.data?.message || error.message}`);
      }
      throw error;
    }
  }

  protected async processData(rawData: unknown): Promise<Record<string, unknown>> {
    return {
      data: rawData,
      metadata: {
        endpoint: this.endpoint,
        timestamp: new Date().toISOString(),
        dataType: typeof rawData,
        recordCount: Array.isArray(rawData) ? rawData.length : 
                    (typeof rawData === 'object' && rawData !== null ? Object.keys(rawData).length : 1)
      }
    };
  }

  protected async createContents(data: Record<string, unknown>): Promise<ResourceContents[]> {
    const content = this.createTextContent(
      `api://${this.endpoint}`,
      JSON.stringify(data, null, 2),
      'application/json'
    );

    return [content];
  }
}
```

---

## 资源注册模板

### Python 注册代码
```python
# 在 resources/__init__.py 中添加
from .{resource_file} import {ResourceName}Resource

def _register_resources(self):
    \"\"\"注册所有资源\"\"\"
    
    # 注册{资源名称}
    {resource_var} = {ResourceName}Resource(self.settings)
    self.register_resource(
        {resource_var}.get_resource_definition(),
        {resource_var}.read_content
    )
    
    logger.info(f"Registered resource: {resource_var}.get_resource_definition().uri")
```

### TypeScript 注册代码
```typescript
// 在 resources/index.ts 中添加
import { {ResourceName}Resource } from './{resource_file}.js';

private registerResources(): void {
  // 注册{资源名称}
  const {resourceVar} = new {ResourceName}Resource(this.settings);
  this.registerResource({
    resource: {resourceVar}.getResourceDefinition(),
    reader: {resourceVar}.readContent.bind({resourceVar})
  });

  logger.info(`Registered resource: ${JSON.stringify({resourceVar}.getResourceDefinition().uri)}`);
}
```

---

## 测试模板

### Python 测试代码
```python
import pytest
from unittest.mock import AsyncMock, patch, mock_open
from src.{project_name}.resources.{resource_file} import {ResourceName}Resource
from src.{project_name}.config.settings import Settings

@pytest.fixture
def resource(settings):
    return {ResourceName}Resource(settings)

@pytest.mark.asyncio
async def test_{resource_name}_definition(resource):
    \"\"\"测试资源定义\"\"\"
    definition = resource.get_resource_definition()
    
    assert definition.uri
    assert definition.name
    assert definition.description
    assert definition.mimeType

@pytest.mark.asyncio
async def test_{resource_name}_read_content(resource):
    \"\"\"测试资源内容读取\"\"\"
    
    # Mock数据获取
    with patch.object(resource, '_fetch_data') as mock_fetch:
        mock_fetch.return_value = {"test": "data"}
        
        contents = await resource.read_content()
        
        assert len(contents) > 0
        assert hasattr(contents[0], 'uri')
        assert hasattr(contents[0], 'mimeType')

@pytest.mark.asyncio
async def test_{resource_name}_caching(resource):
    \"\"\"测试缓存机制\"\"\"
    
    with patch.object(resource, '_fetch_data') as mock_fetch:
        mock_fetch.return_value = {"test": "data"}
        
        # 第一次调用
        contents1 = await resource.read_content()
        
        # 第二次调用应该使用缓存
        contents2 = await resource.read_content()
        
        assert contents1 == contents2
        assert mock_fetch.call_count == 1  # 只调用一次

@pytest.mark.asyncio
async def test_{resource_name}_error_handling(resource):
    \"\"\"测试错误处理\"\"\"
    
    with patch.object(resource, '_fetch_data') as mock_fetch:
        mock_fetch.side_effect = Exception("Test error")
        
        with pytest.raises(Exception) as exc_info:
            await resource.read_content()
        
        assert "Test error" in str(exc_info.value)

# 文件系统资源特定测试
@pytest.mark.asyncio
async def test_file_system_resource_file_reading():
    \"\"\"测试文件系统资源的文件读取\"\"\"
    
    settings = Settings(api_base_url="https://test.com")
    resource = FileSystemResource(settings, "/test/path")
    
    with patch('aiofiles.open', mock_open(read_data="test content")):
        with patch('pathlib.Path.rglob') as mock_rglob:
            mock_file = AsyncMock()
            mock_file.is_file.return_value = True
            mock_file.suffix = '.txt'
            mock_file.relative_to.return_value = 'test.txt'
            mock_rglob.return_value = [mock_file]
            
            contents = await resource.read_content()
            
            assert len(contents) == 1
            assert contents[0].text == "test content"
```

### TypeScript 测试代码
```typescript
import { {ResourceName}Resource } from '../src/resources/{resource_file}.js';
import { createTestSettings } from './helpers.js';

describe('{ResourceName}Resource', () => {
  let resource: {ResourceName}Resource;
  let settings: ReturnType<typeof createTestSettings>;

  beforeEach(() => {
    settings = createTestSettings();
    resource = new {ResourceName}Resource(settings);
  });

  test('should have correct resource definition', () => {
    const definition = resource.getResourceDefinition();
    
    expect(definition.uri).toBeDefined();
    expect(definition.name).toBeDefined();
    expect(definition.description).toBeDefined();
    expect(definition.mimeType).toBeDefined();
  });

  test('should read content successfully', async () => {
    // Mock fetchData方法
    jest.spyOn(resource as any, 'fetchData').mockResolvedValue({ test: 'data' });

    const contents = await resource.readContent();

    expect(contents.length).toBeGreaterThan(0);
    expect(contents[0]).toHaveProperty('uri');
    expect(contents[0]).toHaveProperty('mimeType');
  });

  test('should handle caching correctly', async () => {
    const mockFetch = jest.spyOn(resource as any, 'fetchData').mockResolvedValue({ test: 'data' });

    // 第一次调用
    const contents1 = await resource.readContent();
    
    // 第二次调用应该使用缓存
    const contents2 = await resource.readContent();

    expect(contents1).toEqual(contents2);
    expect(mockFetch).toHaveBeenCalledTimes(1); // 只调用一次
  });

  test('should handle errors properly', async () => {
    jest.spyOn(resource as any, 'fetchData').mockRejectedValue(new Error('Test error'));

    await expect(resource.readContent()).rejects.toThrow('Test error');
  });
});
```

---

## 最佳实践

### 1. 数据获取
```
- 实现适当的错误处理和重试机制
- 使用连接池和资源复用
- 设置合理的超时时间
- 监控API调用性能和可用性
```

### 2. 缓存策略
```
- 根据数据更新频率设置TTL
- 实现缓存失效和刷新机制
- 考虑内存使用和清理策略
- 提供缓存统计和监控
```

### 3. 内容格式
```
- 选择合适的MIME类型
- 确保数据编码正确
- 提供结构化的元数据
- 支持多种内容格式
```

### 4. 性能优化
```
- 实现增量读取和流式传输
- 压缩大型资源内容
- 并行处理多个资源
- 优化内存使用和垃圾回收
```

### 5. 安全考虑
```
- 验证和清理文件路径
- 限制资源访问权限
- 不暴露敏感配置信息
- 实现适当的访问控制
```

---

## 检查清单

使用此模板开发资源时，请确保：

- [ ] **URI规范**：资源URI遵循约定格式
- [ ] **类型正确**：选择合适的内容类型和MIME
- [ ] **数据完整**：资源内容完整且格式正确
- [ ] **缓存机制**：实现合适的缓存策略
- [ ] **错误处理**：妥善处理各种异常情况
- [ ] **性能合理**：资源读取效率在可接受范围
- [ ] **测试覆盖**：包含全面的单元测试
- [ ] **文档完整**：资源定义和使用说明清晰

此模板为MCP资源开发提供了完整的框架和最佳实践，确保开发出高质量、高性能的资源实现。
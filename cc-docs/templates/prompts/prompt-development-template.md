# MCP 提示词(Prompts)开发模板

## 模板概述
本模板提供了开发MCP提示词的标准化流程和代码框架，帮助创建高质量的提示词模板系统。

---

## 通用开发流程

### 第1步：提示词需求分析

#### 输入要求
- 提示词应用场景描述
- 目标用户群体和使用方式
- 预期输入参数和输出格式

#### 分析步骤
```
1. 场景分解：
   - 确定提示词的核心用途
   - 识别不同的使用场景
   - 定义输入参数类型和约束

2. 模板设计：
   - 设计系统提示词（system message）
   - 设计用户提示词模板（user message）
   - 考虑对话上下文（assistant message）

3. 参数化策略：
   - 识别可变参数
   - 设计条件逻辑
   - 定义默认值和验证规则
```

### 第2步：提示词结构设计

#### 参数定义模板
```json
{
  "name": "prompt_name",
  "description": "提示词功能的清晰描述",
  "arguments": [
    {
      "name": "required_param",
      "description": "必需参数的描述",
      "required": true
    },
    {
      "name": "optional_param", 
      "description": "可选参数的描述",
      "required": false
    },
    {
      "name": "enum_param",
      "description": "枚举参数的描述",
      "required": false
    }
  ]
}
```

#### 消息结构设计原则
```
1. 系统消息（System Message）：
   - 定义AI助手的角色和能力
   - 设置输出格式和约束
   - 提供背景知识和上下文

2. 用户消息（User Message）：
   - 包含具体的任务描述
   - 整合用户提供的参数
   - 清晰表达期望结果

3. 助手消息（Assistant Message，可选）：
   - 提供示例响应
   - 引导对话方向
   - 设置交互模式
```

---

## Python 提示词实现模板

### 基础提示词类模板

```python
from typing import List, Dict, Any, Optional
from mcp.types import Prompt, PromptMessage, PromptArgument, TextContent
from ..config.settings import Settings
from ..utils.common import validate_arguments, handle_errors
import logging

class {PromptName}Prompt:
    \"\"\"
    {提示词功能描述}
    
    此提示词用于：
    - 功能1描述
    - 功能2描述 
    - 功能3描述
    
    适用场景：
    - 场景1
    - 场景2
    - 场景3
    \"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
    
    def get_prompt_definition(self) -> Prompt:
        \"\"\"返回提示词定义\"\"\"
        return Prompt(
            name="{prompt_name}",
            description="{提示词功能的详细描述}",
            arguments=[
                PromptArgument(
                    name="param1",
                    description="参数1的描述",
                    required=True
                ),
                PromptArgument(
                    name="param2", 
                    description="参数2的描述",
                    required=False
                ),
                PromptArgument(
                    name="style",
                    description="输出风格（formal, casual, technical）",
                    required=False
                )
            ]
        )
    
    @handle_errors
    @validate_arguments({
        "type": "object",
        "properties": {
            "param1": {"type": "string"},
            "param2": {"type": "string"},
            "style": {"type": "string", "enum": ["formal", "casual", "technical"]}
        },
        "required": ["param1"]
    })
    async def generate_messages(self, arguments: Dict[str, Any]) -> List[PromptMessage]:
        \"\"\"生成提示词消息列表\"\"\"
        
        # 参数提取和验证
        param1 = arguments["param1"]
        param2 = arguments.get("param2", "")
        style = arguments.get("style", "casual")
        
        self.logger.info(f"Generating {self.__class__.__name__} with style={style}")
        
        try:
            messages = []
            
            # 1. 生成系统消息
            system_message = self._create_system_message(style)
            if system_message:
                messages.append(system_message)
            
            # 2. 生成用户消息
            user_message = self._create_user_message(param1, param2, style)
            messages.append(user_message)
            
            # 3. 生成助手消息（如果需要）
            assistant_message = self._create_assistant_message(style)
            if assistant_message:
                messages.append(assistant_message)
            
            self.logger.info(f"Generated {len(messages)} messages")
            return messages
            
        except Exception as e:
            self.logger.error(f"Error generating messages: {str(e)}")
            raise
    
    def _create_system_message(self, style: str) -> Optional[PromptMessage]:
        \"\"\"创建系统消息\"\"\"
        
        style_instructions = {
            "formal": "请使用正式、专业的语言风格，确保准确性和权威性。",
            "casual": "请使用轻松、友好的语言风格，注重可读性和易理解性。",
            "technical": "请使用技术性语言，包含专业术语和详细说明。"
        }
        
        system_content = f\"\"\"你是一个专业的{领域}专家。{style_instructions.get(style, style_instructions["casual"])}

请遵循以下指导原则：
1. 提供准确、有用的信息
2. 结构化组织内容
3. 使用清晰的例子说明概念
4. 保持客观和专业的态度

输出格式要求：
- 使用markdown格式
- 包含适当的标题和列表
- 提供具体的实例
- 总结关键要点\"\"\"
        
        return PromptMessage(
            role="system",
            content=TextContent(
                type="text",
                text=system_content
            )
        )
    
    def _create_user_message(self, param1: str, param2: str, style: str) -> PromptMessage:
        \"\"\"创建用户消息\"\"\"
        
        user_content = f\"\"\"请基于以下信息提供专业建议：

主要内容：{param1}\"\"\"
        
        if param2:
            user_content += f\"\"\"
附加信息：{param2}\"\"\"
        
        user_content += f\"\"\"

请使用{style}风格回答，确保内容全面且实用。\"\"\"
        
        return PromptMessage(
            role="user",
            content=TextContent(
                type="text", 
                text=user_content
            )
        )
    
    def _create_assistant_message(self, style: str) -> Optional[PromptMessage]:
        \"\"\"创建助手消息（用于少样本学习）\"\"\"
        
        # 大多数情况下不需要助手消息，除非需要展示特定格式
        return None
    
    def _validate_style(self, style: str) -> str:
        \"\"\"验证和标准化风格参数\"\"\"
        valid_styles = ["formal", "casual", "technical"]
        if style not in valid_styles:
            self.logger.warning(f"Invalid style '{style}', using 'casual'")
            return "casual"
        return style
```

### 专用提示词类型模板

#### 内容生成提示词
```python
class ContentGenerationPrompt({PromptName}Prompt):
    \"\"\"内容生成专用提示词\"\"\"
    
    def get_prompt_definition(self) -> Prompt:
        return Prompt(
            name="content_generation",
            description="生成各种类型的内容（文章、报告、说明等）",
            arguments=[
                PromptArgument(
                    name="content_type",
                    description="内容类型（article, report, manual, email等）",
                    required=True
                ),
                PromptArgument(
                    name="topic",
                    description="内容主题",
                    required=True
                ),
                PromptArgument(
                    name="target_audience",
                    description="目标受众",
                    required=False
                ),
                PromptArgument(
                    name="length",
                    description="内容长度（short, medium, long）",
                    required=False
                ),
                PromptArgument(
                    name="tone",
                    description="语调风格（professional, friendly, persuasive）",
                    required=False
                )
            ]
        )
    
    async def generate_messages(self, arguments: Dict[str, Any]) -> List[PromptMessage]:
        content_type = arguments["content_type"]
        topic = arguments["topic"]
        audience = arguments.get("target_audience", "一般读者")
        length = arguments.get("length", "medium")
        tone = arguments.get("tone", "professional")
        
        # 根据内容类型定制指令
        type_instructions = {
            "article": "撰写一篇信息丰富的文章",
            "report": "编制一份详细的报告",
            "manual": "创建一份操作手册",
            "email": "撰写一封专业邮件"
        }
        
        length_requirements = {
            "short": "保持简洁，1-2段落",
            "medium": "适中长度，3-5段落",
            "long": "详细展开，5段落以上"
        }
        
        system_content = f\"\"\"你是一位专业的内容创作者。请{type_instructions.get(content_type, "创建内容")}，主题是"{topic}"。

目标受众：{audience}
内容长度：{length_requirements[length]}
语调风格：{tone}

请确保内容：
1. 结构清晰，逻辑合理
2. 信息准确，表达清楚
3. 符合目标受众的需求
4. 使用适当的格式和标题\"\"\"
        
        user_content = f\"\"\"请为"{topic}"这个主题创作{content_type}内容。

要求：
- 面向{audience}
- 采用{tone}语调
- 长度控制在{length}范围内

请开始创作。\"\"\"
        
        return [
            PromptMessage(
                role="system",
                content=TextContent(type="text", text=system_content)
            ),
            PromptMessage(
                role="user", 
                content=TextContent(type="text", text=user_content)
            )
        ]
```

#### 分析总结提示词
```python
class AnalysisSummaryPrompt({PromptName}Prompt):
    \"\"\"分析总结专用提示词\"\"\"
    
    def get_prompt_definition(self) -> Prompt:
        return Prompt(
            name="analysis_summary",
            description="分析和总结各种类型的内容或数据",
            arguments=[
                PromptArgument(
                    name="content",
                    description="需要分析的内容或数据",
                    required=True
                ),
                PromptArgument(
                    name="analysis_type",
                    description="分析类型（summary, trend, comparison, insight）",
                    required=True
                ),
                PromptArgument(
                    name="focus_areas",
                    description="重点关注的方面（逗号分隔）",
                    required=False
                ),
                PromptArgument(
                    name="output_format",
                    description="输出格式（bullet_points, paragraphs, table）",
                    required=False
                )
            ]
        )
    
    async def generate_messages(self, arguments: Dict[str, Any]) -> List[PromptMessage]:
        content = arguments["content"]
        analysis_type = arguments["analysis_type"]
        focus_areas = arguments.get("focus_areas", "")
        output_format = arguments.get("output_format", "paragraphs")
        
        analysis_instructions = {
            "summary": "提供简洁的总结概述",
            "trend": "识别趋势和模式",
            "comparison": "进行对比分析",
            "insight": "深入洞察和建议"
        }
        
        format_instructions = {
            "bullet_points": "使用要点列表格式",
            "paragraphs": "使用段落叙述格式", 
            "table": "使用表格结构化格式"
        }
        
        system_content = f\"\"\"你是一位专业的数据分析师。请对提供的内容进行{analysis_instructions[analysis_type]}。

分析要求：
- 保持客观和准确
- {format_instructions[output_format]}
- 提供清晰的结论\"\"\"
        
        if focus_areas:
            system_content += f\"\\n- 重点关注：{focus_areas}\"
        
        user_content = f\"\"\"请分析以下内容：

{content}

分析类型：{analysis_type}
输出格式：{output_format}\"\"\"
        
        if focus_areas:
            user_content += f\"\\n重点关注领域：{focus_areas}\"
        
        return [
            PromptMessage(
                role="system",
                content=TextContent(type="text", text=system_content)
            ),
            PromptMessage(
                role="user",
                content=TextContent(type="text", text=user_content)
            )
        ]
```

---

## TypeScript 提示词实现模板

### 基础提示词类模板

```typescript
import { 
  Prompt, 
  PromptMessage, 
  PromptArgument,
  TextContent 
} from '@modelcontextprotocol/sdk/types.js';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { handleErrors, validateSchema } from '../utils/common.js';

export interface {PromptName}Arguments {
  param1: string;
  param2?: string;
  style?: 'formal' | 'casual' | 'technical';
}

export class {PromptName}Prompt {
  constructor(private settings: Settings) {}

  getPromptDefinition(): Prompt {
    return {
      name: '{prompt_name}',
      description: '{提示词功能的详细描述}',
      arguments: [
        {
          name: 'param1',
          description: '参数1的描述',
          required: true
        } as PromptArgument,
        {
          name: 'param2',
          description: '参数2的描述',
          required: false
        } as PromptArgument,
        {
          name: 'style',
          description: '输出风格（formal, casual, technical）',
          required: false
        } as PromptArgument
      ]
    };
  }

  @handleErrors
  @validateSchema({
    type: 'object',
    properties: {
      param1: { type: 'string' },
      param2: { type: 'string' },
      style: { 
        type: 'string', 
        enum: ['formal', 'casual', 'technical']
      }
    },
    required: ['param1']
  })
  async generateMessages(arguments_: Record<string, unknown>): Promise<PromptMessage[]> {
    const args = arguments_ as {PromptName}Arguments;
    const { param1, param2 = '', style = 'casual' } = args;

    logger.info(`Generating ${this.constructor.name}`, { style });

    try {
      const messages: PromptMessage[] = [];

      // 1. 生成系统消息
      const systemMessage = this.createSystemMessage(style);
      if (systemMessage) {
        messages.push(systemMessage);
      }

      // 2. 生成用户消息
      const userMessage = this.createUserMessage(param1, param2, style);
      messages.push(userMessage);

      // 3. 生成助手消息（如果需要）
      const assistantMessage = this.createAssistantMessage(style);
      if (assistantMessage) {
        messages.push(assistantMessage);
      }

      logger.info(`Generated ${messages.length} messages`);
      return messages;

    } catch (error) {
      logger.error(`Error generating messages:`, error);
      throw error;
    }
  }

  private createSystemMessage(style: string): PromptMessage | null {
    const styleInstructions = {
      formal: '请使用正式、专业的语言风格，确保准确性和权威性。',
      casual: '请使用轻松、友好的语言风格，注重可读性和易理解性。',
      technical: '请使用技术性语言，包含专业术语和详细说明。'
    };

    const systemContent = `你是一个专业的{领域}专家。${styleInstructions[style as keyof typeof styleInstructions] || styleInstructions.casual}

请遵循以下指导原则：
1. 提供准确、有用的信息
2. 结构化组织内容
3. 使用清晰的例子说明概念
4. 保持客观和专业的态度

输出格式要求：
- 使用markdown格式
- 包含适当的标题和列表
- 提供具体的实例
- 总结关键要点`;

    return {
      role: 'system',
      content: {
        type: 'text',
        text: systemContent
      } as TextContent
    };
  }

  private createUserMessage(param1: string, param2: string, style: string): PromptMessage {
    let userContent = `请基于以下信息提供专业建议：

主要内容：${param1}`;

    if (param2) {
      userContent += `
附加信息：${param2}`;
    }

    userContent += `

请使用${style}风格回答，确保内容全面且实用。`;

    return {
      role: 'user',
      content: {
        type: 'text',
        text: userContent
      } as TextContent
    };
  }

  private createAssistantMessage(style: string): PromptMessage | null {
    // 大多数情况下不需要助手消息，除非需要展示特定格式
    return null;
  }

  private validateStyle(style: string): string {
    const validStyles = ['formal', 'casual', 'technical'];
    if (!validStyles.includes(style)) {
      logger.warn(`Invalid style '${style}', using 'casual'`);
      return 'casual';
    }
    return style;
  }
}
```

### 专用提示词类型模板

#### 代码生成提示词
```typescript
export interface CodeGenerationArguments {
  language: string;
  task: string;
  framework?: string;
  style_guide?: string;
  complexity?: 'simple' | 'moderate' | 'complex';
}

export class CodeGenerationPrompt {
  constructor(private settings: Settings) {}

  getPromptDefinition(): Prompt {
    return {
      name: 'code_generation',
      description: '生成指定编程语言的代码',
      arguments: [
        {
          name: 'language',
          description: '编程语言（javascript, python, typescript等）',
          required: true
        } as PromptArgument,
        {
          name: 'task',
          description: '代码应实现的功能描述',
          required: true
        } as PromptArgument,
        {
          name: 'framework',
          description: '使用的框架或库（可选）',
          required: false
        } as PromptArgument,
        {
          name: 'style_guide',
          description: '遵循的代码风格指南（可选）',
          required: false
        } as PromptArgument,
        {
          name: 'complexity',
          description: '代码复杂度（simple, moderate, complex）',
          required: false
        } as PromptArgument
      ]
    };
  }

  async generateMessages(arguments_: Record<string, unknown>): Promise<PromptMessage[]> {
    const {
      language,
      task,
      framework,
      style_guide,
      complexity = 'moderate'
    } = arguments_ as CodeGenerationArguments;

    let systemContent = `你是一个专业的${language}开发工程师。请生成高质量、可维护的代码。`;

    if (framework) {
      systemContent += ` 使用${framework}框架。`;
    }

    if (style_guide) {
      systemContent += ` 遵循${style_guide}编码规范。`;
    }

    const complexityInstructions = {
      simple: '代码应简洁明了，适合初学者理解',
      moderate: '代码应平衡简洁性和功能性',
      complex: '代码可以使用高级特性和复杂架构'
    };

    systemContent += `

代码要求：
- ${complexityInstructions[complexity]}
- 包含必要的注释和文档
- 实现适当的错误处理
- 遵循${language}最佳实践
- 提供使用示例

输出格式：
- 使用代码块格式
- 包含文件名和路径
- 添加简要说明`;

    const userContent = `请为以下任务生成${language}代码：

任务描述：${task}

要求：
- 编程语言：${language}
- 复杂度：${complexity}
${framework ? `- 使用框架：${framework}` : ''}
${style_guide ? `- 代码风格：${style_guide}` : ''}

请提供完整的实现代码。`;

    return [
      {
        role: 'system',
        content: { type: 'text', text: systemContent } as TextContent
      },
      {
        role: 'user',
        content: { type: 'text', text: userContent } as TextContent
      }
    ];
  }
}
```

---

## 提示词注册模板

### Python 注册代码
```python
# 在 prompts/__init__.py 中添加
from .{prompt_file} import {PromptName}Prompt

def _register_prompts(self):
    \"\"\"注册所有提示词\"\"\"
    
    # 注册{提示词名称}
    {prompt_var} = {PromptName}Prompt(self.settings)
    self.register_prompt(
        {prompt_var}.get_prompt_definition(),
        {prompt_var}.generate_messages
    )
    
    logger.info(f"Registered prompt: {prompt_var}.get_prompt_definition().name")
```

### TypeScript 注册代码
```typescript
// 在 prompts/index.ts 中添加
import { {PromptName}Prompt } from './{prompt_file}.js';

private registerPrompts(): void {
  // 注册{提示词名称}
  const {promptVar} = new {PromptName}Prompt(this.settings);
  this.registerPrompt({
    prompt: {promptVar}.getPromptDefinition(),
    generator: {promptVar}.generateMessages.bind({promptVar})
  });

  logger.info(`Registered prompt: ${JSON.stringify({promptVar}.getPromptDefinition().name)}`);
}
```

---

## 测试模板

### Python 测试代码
```python
import pytest
from src.{project_name}.prompts.{prompt_file} import {PromptName}Prompt
from src.{project_name}.config.settings import Settings

@pytest.fixture
def prompt(settings):
    return {PromptName}Prompt(settings)

@pytest.mark.asyncio
async def test_{prompt_name}_definition(prompt):
    \"\"\"测试提示词定义\"\"\"
    definition = prompt.get_prompt_definition()
    
    assert definition.name == "{prompt_name}"
    assert definition.description
    assert len(definition.arguments) > 0

@pytest.mark.asyncio
async def test_{prompt_name}_message_generation(prompt):
    \"\"\"测试消息生成\"\"\"
    
    arguments = {
        "param1": "test_value",
        "style": "casual"
    }
    
    messages = await prompt.generate_messages(arguments)
    
    assert len(messages) > 0
    assert messages[0].role in ["system", "user"]
    assert messages[0].content.text

@pytest.mark.asyncio
async def test_{prompt_name}_parameter_validation(prompt):
    \"\"\"测试参数验证\"\"\"
    
    # 测试缺少必需参数
    with pytest.raises(Exception):
        await prompt.generate_messages({})
    
    # 测试无效的枚举值
    with pytest.raises(Exception):
        await prompt.generate_messages({
            "param1": "test",
            "style": "invalid_style"
        })

@pytest.mark.asyncio
async def test_{prompt_name}_different_styles(prompt):
    \"\"\"测试不同风格\"\"\"
    
    styles = ["formal", "casual", "technical"]
    
    for style in styles:
        messages = await prompt.generate_messages({
            "param1": "test_content",
            "style": style
        })
        
        assert len(messages) > 0
        # 验证系统消息包含风格相关内容
        system_message = next((m for m in messages if m.role == "system"), None)
        if system_message:
            assert style in system_message.content.text or \
                   any(keyword in system_message.content.text.lower() 
                       for keyword in ["正式", "友好", "技术"])
```

### TypeScript 测试代码
```typescript
import { {PromptName}Prompt } from '../src/prompts/{prompt_file}.js';
import { createTestSettings } from './helpers.js';

describe('{PromptName}Prompt', () => {
  let prompt: {PromptName}Prompt;
  let settings: ReturnType<typeof createTestSettings>;

  beforeEach(() => {
    settings = createTestSettings();
    prompt = new {PromptName}Prompt(settings);
  });

  test('should have correct prompt definition', () => {
    const definition = prompt.getPromptDefinition();
    
    expect(definition.name).toBe('{prompt_name}');
    expect(definition.description).toBeDefined();
    expect(definition.arguments.length).toBeGreaterThan(0);
  });

  test('should generate messages with valid arguments', async () => {
    const arguments_ = {
      param1: 'test_value',
      style: 'casual'
    };

    const messages = await prompt.generateMessages(arguments_);

    expect(messages.length).toBeGreaterThan(0);
    expect(['system', 'user']).toContain(messages[0].role);
    expect(messages[0].content.text).toBeDefined();
  });

  test('should validate required parameters', async () => {
    await expect(prompt.generateMessages({})).rejects.toThrow();
  });

  test('should handle different styles', async () => {
    const styles = ['formal', 'casual', 'technical'];

    for (const style of styles) {
      const messages = await prompt.generateMessages({
        param1: 'test_content',
        style
      });

      expect(messages.length).toBeGreaterThan(0);
      
      const systemMessage = messages.find(m => m.role === 'system');
      if (systemMessage) {
        expect(systemMessage.content.text).toContain(style);
      }
    }
  });
});
```

---

## 最佳实践

### 1. 消息设计
```
- 系统消息要清晰定义角色和规则
- 用户消息要包含所有必要信息
- 保持消息之间的逻辑连贯性
- 使用合适的消息顺序
```

### 2. 参数化
```
- 提供合理的默认值
- 验证参数的有效性
- 支持灵活的配置选项
- 处理边界情况
```

### 3. 内容质量
```
- 使用清晰、准确的描述
- 提供具体的指导和约束
- 包含相关的背景信息
- 保持专业和一致的语调
```

### 4. 可维护性
```
- 模块化设计，便于复用
- 充分的代码注释和文档
- 统一的命名和结构约定
- 版本控制和更新管理
```

### 5. 测试覆盖
```
- 测试不同的参数组合
- 验证输出格式和内容
- 检查错误处理机制
- 性能和稳定性测试
```

---

## 检查清单

使用此模板开发提示词时，请确保：

- [ ] **功能明确**：提示词用途和效果清晰定义
- [ ] **参数完整**：所有必要参数都有定义和验证
- [ ] **消息结构**：系统、用户消息逻辑合理
- [ ] **风格适配**：支持不同的输出风格和格式
- [ ] **错误处理**：妥善处理参数验证和生成错误
- [ ] **测试覆盖**：包含全面的单元测试
- [ ] **文档完整**：使用说明和示例清晰
- [ ] **性能合理**：消息生成效率在可接受范围

此模板为MCP提示词开发提供了完整的框架和最佳实践，确保开发出高质量、可维护的提示词实现。
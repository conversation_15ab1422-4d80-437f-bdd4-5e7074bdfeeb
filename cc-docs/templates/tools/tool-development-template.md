# MCP 工具(Tools)开发模板

## 模板概述
本模板提供了开发MCP工具的标准化流程和代码框架，适用于Python和TypeScript两种语言。

---

## 通用开发流程

### 第1步：工具需求分析

#### 输入要求
- API接口文档或服务说明
- 工具预期功能描述
- 参数和返回值规范

#### 分析步骤
```
1. 功能分解：
   - 确定工具的核心功能
   - 识别必需参数和可选参数
   - 定义返回数据结构

2. API接口映射：
   - 分析目标API的认证方式
   - 确定HTTP方法和端点
   - 理解请求/响应格式

3. 错误场景规划：
   - 识别可能的错误情况
   - 设计错误处理策略
   - 定义用户友好的错误消息
```

### 第2步：工具规范设计

#### JSON Schema 设计模板
```json
{
  "name": "tool_name",
  "description": "工具功能的清晰描述",
  "inputSchema": {
    "type": "object",
    "properties": {
      "required_param": {
        "type": "string",
        "description": "必需参数的描述"
      },
      "optional_param": {
        "type": "integer",
        "description": "可选参数的描述",
        "default": 10,
        "minimum": 1,
        "maximum": 100
      },
      "enum_param": {
        "type": "string",
        "enum": ["option1", "option2", "option3"],
        "description": "枚举参数"
      }
    },
    "required": ["required_param"],
    "additionalProperties": false
  }
}
```

#### 参数设计原则
```
1. 命名约定：
   - 使用snake_case命名
   - 名称要具有描述性
   - 避免缩写和歧义

2. 类型选择：
   - string: 文本、ID、路径
   - number/integer: 数值、计数、索引
   - boolean: 开关、标志
   - array: 列表、集合
   - object: 复杂结构

3. 验证规则：
   - 使用format约束（email, uri, date等）
   - 设置合理的minimum/maximum
   - 提供enum选项当适用时
   - 添加pattern正则表达式验证
```

---

## Python 工具实现模板

### 基础工具类模板

```python
from typing import Dict, Any, Optional, List
from mcp.types import Tool
import httpx
import logging
from ..config.settings import Settings
from ..utils.common import validate_arguments, handle_errors

class {ToolName}Tool:
    \"\"\"
    {工具功能描述}
    
    此工具用于：
    - 功能1描述
    - 功能2描述
    - 功能3描述
    \"\"\"
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.client = httpx.AsyncClient(
            base_url=settings.api_base_url,
            timeout=settings.api_timeout,
            headers=self._get_headers()
        )
    
    def _get_headers(self) -> Dict[str, str]:
        \"\"\"获取API请求头\"\"\"
        headers = {"Content-Type": "application/json"}
        if self.settings.api_key:
            headers["Authorization"] = f"Bearer {self.settings.api_key}"
        return headers
    
    def get_tool_definition(self) -> Tool:
        \"\"\"返回工具定义\"\"\"
        return Tool(
            name="{tool_name}",
            description="{工具功能的详细描述}",
            inputSchema={
                "type": "object",
                "properties": {
                    "param1": {
                        "type": "string",
                        "description": "参数1的描述"
                    },
                    "param2": {
                        "type": "integer",
                        "description": "参数2的描述",
                        "default": 10
                    }
                },
                "required": ["param1"]
            }
        )
    
    @handle_errors
    @validate_arguments({
        "type": "object",
        "properties": {
            "param1": {"type": "string"},
            "param2": {"type": "integer"}
        },
        "required": ["param1"]
    })
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        \"\"\"执行工具逻辑\"\"\"
        
        # 参数提取和验证
        param1 = arguments["param1"]
        param2 = arguments.get("param2", 10)
        
        self.logger.info(f"Executing {self.__class__.__name__} with param1={param1}, param2={param2}")
        
        try:
            # 1. 预处理（如果需要）
            processed_data = await self._preprocess(param1, param2)
            
            # 2. API调用
            api_result = await self._call_api(processed_data)
            
            # 3. 后处理
            final_result = await self._postprocess(api_result)
            
            # 4. 返回结果
            return {
                "success": True,
                "data": final_result,
                "metadata": {
                    "tool": "{tool_name}",
                    "timestamp": self._get_timestamp(),
                    "parameters_used": {
                        "param1": param1,
                        "param2": param2
                    }
                }
            }
            
        except httpx.HTTPError as e:
            self.logger.error(f"HTTP error in {self.__class__.__name__}: {str(e)}")
            raise Exception(f"API请求失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"Error in {self.__class__.__name__}: {str(e)}")
            raise
    
    async def _preprocess(self, param1: str, param2: int) -> Dict[str, Any]:
        \"\"\"预处理输入数据\"\"\"
        return {
            "processed_param1": param1.strip().lower(),
            "processed_param2": max(1, min(param2, 100))  # 限制范围
        }
    
    async def _call_api(self, data: Dict[str, Any]) -> Dict[str, Any]:
        \"\"\"调用外部API\"\"\"
        response = await self.client.post(
            "/api/endpoint",
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    async def _postprocess(self, api_result: Dict[str, Any]) -> Dict[str, Any]:
        \"\"\"后处理API结果\"\"\"
        # 根据需要转换或过滤数据
        return {
            "processed_result": api_result.get("data"),
            "status": api_result.get("status", "unknown")
        }
    
    def _get_timestamp(self) -> str:
        \"\"\"获取当前时间戳\"\"\"
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
```

### 专用工具类型模板

#### 数据查询工具
```python
class DataQueryTool({ToolName}Tool):
    \"\"\"数据查询专用工具\"\"\"
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        query = arguments.get("query", "")
        filters = arguments.get("filters", {})
        limit = arguments.get("limit", 50)
        offset = arguments.get("offset", 0)
        
        # 构建查询参数
        params = {
            "q": query,
            "limit": limit,
            "offset": offset,
            **filters
        }
        
        # 执行查询
        response = await self.client.get("/api/search", params=params)
        data = response.json()
        
        return {
            "success": True,
            "data": {
                "results": data.get("results", []),
                "total": data.get("total", 0),
                "has_more": len(data.get("results", [])) == limit
            },
            "query_info": {
                "query": query,
                "filters_applied": filters,
                "limit": limit,
                "offset": offset
            }
        }
```

#### 数据操作工具
```python
class DataManipulationTool({ToolName}Tool):
    \"\"\"数据操作专用工具\"\"\"
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        operation = arguments["operation"]  # create, update, delete
        data = arguments.get("data", {})
        resource_id = arguments.get("id")
        
        if operation == "create":
            result = await self._create_resource(data)
        elif operation == "update":
            result = await self._update_resource(resource_id, data)
        elif operation == "delete":
            result = await self._delete_resource(resource_id)
        else:
            raise ValueError(f"Unsupported operation: {operation}")
        
        return {
            "success": True,
            "operation": operation,
            "result": result
        }
    
    async def _create_resource(self, data: Dict[str, Any]) -> Dict[str, Any]:
        response = await self.client.post("/api/resources", json=data)
        return response.json()
    
    async def _update_resource(self, resource_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        response = await self.client.put(f"/api/resources/{resource_id}", json=data)
        return response.json()
    
    async def _delete_resource(self, resource_id: str) -> Dict[str, Any]:
        response = await self.client.delete(f"/api/resources/{resource_id}")
        return {"deleted": True, "id": resource_id}
```

---

## TypeScript 工具实现模板

### 基础工具类模板

```typescript
import { Tool } from '@modelcontextprotocol/sdk/types.js';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { Settings } from '../config/settings.js';
import { logger } from '../utils/common.js';
import { handleErrors, validateSchema, retry } from '../utils/common.js';

export interface {ToolName}Arguments {
  param1: string;
  param2?: number;
}

export interface {ToolName}Result {
  success: boolean;
  data?: unknown;
  metadata?: {
    tool: string;
    timestamp: string;
    parameters_used: {ToolName}Arguments;
  };
}

export class {ToolName}Tool {
  private client: AxiosInstance;

  constructor(private settings: Settings) {
    this.client = axios.create({
      baseURL: settings.apiBaseUrl,
      timeout: settings.apiTimeout,
      headers: this.getHeaders()
    });

    this.setupInterceptors();
  }

  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.settings.apiKey) {
      headers['Authorization'] = `Bearer ${this.settings.apiKey}`;
    }

    return headers;
  }

  private setupInterceptors(): void {
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params
        });
        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`API Response: ${response.status}`, { data: response.data });
        return response;
      },
      (error) => {
        logger.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  getToolDefinition(): Tool {
    return {
      name: '{tool_name}',
      description: '{工具功能的详细描述}',
      inputSchema: {
        type: 'object',
        properties: {
          param1: {
            type: 'string',
            description: '参数1的描述'
          },
          param2: {
            type: 'number',
            description: '参数2的描述',
            default: 10,
            minimum: 1,
            maximum: 100
          }
        },
        required: ['param1'],
        additionalProperties: false
      }
    };
  }

  @handleErrors
  @validateSchema({
    type: 'object',
    properties: {
      param1: { type: 'string' },
      param2: { type: 'number' }
    },
    required: ['param1']
  })
  @retry(3, 1000)
  async execute(arguments_: Record<string, unknown>): Promise<{ToolName}Result> {
    const args = arguments_ as {ToolName}Arguments;
    const { param1, param2 = 10 } = args;

    logger.info(`Executing ${this.constructor.name}`, { param1, param2 });

    try {
      // 1. 预处理
      const processedData = await this.preprocess(param1, param2);

      // 2. API调用
      const apiResult = await this.callApi(processedData);

      // 3. 后处理
      const finalResult = await this.postprocess(apiResult);

      // 4. 返回结果
      return {
        success: true,
        data: finalResult,
        metadata: {
          tool: '{tool_name}',
          timestamp: new Date().toISOString(),
          parameters_used: args
        }
      };

    } catch (error) {
      logger.error(`Error in ${this.constructor.name}:`, error);
      
      if (axios.isAxiosError(error)) {
        throw new Error(`API请求失败: ${error.response?.data?.message || error.message}`);
      }
      
      throw error;
    }
  }

  private async preprocess(param1: string, param2: number): Promise<Record<string, any>> {
    return {
      processed_param1: param1.trim().toLowerCase(),
      processed_param2: Math.max(1, Math.min(param2, 100))
    };
  }

  private async callApi(data: Record<string, any>): Promise<Record<string, any>> {
    const response: AxiosResponse = await this.client.post('/api/endpoint', data);
    return response.data;
  }

  private async postprocess(apiResult: Record<string, any>): Promise<Record<string, any>> {
    return {
      processed_result: apiResult.data,
      status: apiResult.status || 'unknown'
    };
  }
}
```

### 专用工具类型模板

#### 数据查询工具
```typescript
export interface QueryArguments {
  query: string;
  filters?: Record<string, any>;
  limit?: number;
  offset?: number;
}

export class DataQueryTool extends BaseTool {
  getToolDefinition(): Tool {
    return {
      name: 'data_query',
      description: '查询数据库或API中的数据',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: '查询关键词或条件'
          },
          filters: {
            type: 'object',
            description: '过滤条件',
            additionalProperties: true
          },
          limit: {
            type: 'number',
            description: '返回结果数量限制',
            default: 50,
            minimum: 1,
            maximum: 1000
          },
          offset: {
            type: 'number',
            description: '结果偏移量',
            default: 0,
            minimum: 0
          }
        },
        required: ['query']
      }
    };
  }

  async execute(arguments_: Record<string, unknown>): Promise<any> {
    const { query, filters = {}, limit = 50, offset = 0 } = arguments_ as QueryArguments;

    const params = {
      q: query,
      limit,
      offset,
      ...filters
    };

    const response = await this.client.get('/api/search', { params });
    const data = response.data;

    return {
      success: true,
      data: {
        results: data.results || [],
        total: data.total || 0,
        has_more: (data.results || []).length === limit
      },
      query_info: {
        query,
        filters_applied: filters,
        limit,
        offset
      }
    };
  }
}
```

---

## 工具注册模板

### Python 注册代码
```python
# 在 tools/__init__.py 中添加
from .{tool_file} import {ToolName}Tool

def _register_tools(self):
    \"\"\"注册所有工具\"\"\"
    
    # 注册{工具名称}工具
    {tool_var} = {ToolName}Tool(self.settings)
    self.register_tool(
        {tool_var}.get_tool_definition(),
        {tool_var}.execute
    )
    
    logger.info(f"Registered tool: {tool_var}.get_tool_definition().name")
```

### TypeScript 注册代码
```typescript
// 在 tools/index.ts 中添加
import { {ToolName}Tool } from './{tool_file}.js';

private registerTools(): void {
  // 注册{工具名称}工具
  const {toolVar} = new {ToolName}Tool(this.settings);
  this.registerTool({
    tool: {toolVar}.getToolDefinition(),
    handler: {toolVar}.execute.bind({toolVar})
  });

  logger.info(`Registered tool: ${JSON.stringify({toolVar}.getToolDefinition().name)}`);
}
```

---

## 测试模板

### Python 测试代码
```python
import pytest
from unittest.mock import AsyncMock, patch
from src.{project_name}.tools.{tool_file} import {ToolName}Tool
from src.{project_name}.config.settings import Settings

@pytest.fixture
def tool(settings):
    return {ToolName}Tool(settings)

@pytest.mark.asyncio
async def test_{tool_name}_definition(tool):
    \"\"\"测试工具定义\"\"\"
    definition = tool.get_tool_definition()
    
    assert definition.name == "{tool_name}"
    assert definition.description
    assert "properties" in definition.inputSchema
    assert "param1" in definition.inputSchema["properties"]

@pytest.mark.asyncio
async def test_{tool_name}_execution_success(tool):
    \"\"\"测试工具成功执行\"\"\"
    
    # Mock API响应
    with patch.object(tool.client, 'post') as mock_post:
        mock_response = AsyncMock()
        mock_response.json.return_value = {"status": "success", "data": "test_result"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        result = await tool.execute({"param1": "test_value"})
        
        assert result["success"] is True
        assert "data" in result
        assert "metadata" in result

@pytest.mark.asyncio
async def test_{tool_name}_execution_error(tool):
    \"\"\"测试工具执行错误处理\"\"\"
    
    with patch.object(tool.client, 'post') as mock_post:
        mock_post.side_effect = Exception("API Error")
        
        with pytest.raises(Exception) as exc_info:
            await tool.execute({"param1": "test_value"})
        
        assert "API Error" in str(exc_info.value)

@pytest.mark.asyncio 
async def test_{tool_name}_validation_error(tool):
    \"\"\"测试参数验证错误\"\"\"
    
    with pytest.raises(Exception) as exc_info:
        await tool.execute({})  # 缺少必需参数
    
    assert "required" in str(exc_info.value).lower()
```

### TypeScript 测试代码
```typescript
import { {ToolName}Tool } from '../src/tools/{tool_file}.js';
import { createTestSettings } from './helpers.js';

describe('{ToolName}Tool', () => {
  let tool: {ToolName}Tool;
  let settings: ReturnType<typeof createTestSettings>;

  beforeEach(() => {
    settings = createTestSettings();
    tool = new {ToolName}Tool(settings);
  });

  test('should have correct tool definition', () => {
    const definition = tool.getToolDefinition();
    
    expect(definition.name).toBe('{tool_name}');
    expect(definition.description).toBeDefined();
    expect(definition.inputSchema).toBeDefined();
    expect(definition.inputSchema.properties).toHaveProperty('param1');
  });

  test('should execute successfully with valid parameters', async () => {
    // Mock axios response
    const mockResponse = { data: { status: 'success', data: 'test_result' } };
    jest.spyOn(tool as any, 'callApi').mockResolvedValue(mockResponse.data);

    const result = await tool.execute({ param1: 'test_value' });

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.metadata).toBeDefined();
  });

  test('should handle execution errors', async () => {
    jest.spyOn(tool as any, 'callApi').mockRejectedValue(new Error('API Error'));

    await expect(tool.execute({ param1: 'test_value' })).rejects.toThrow('API Error');
  });

  test('should validate required parameters', async () => {
    await expect(tool.execute({})).rejects.toThrow();
  });
});
```

---

## 最佳实践

### 1. 错误处理
```
- 使用结构化的异常类型
- 提供用户友好的错误消息
- 记录详细的错误日志
- 实现重试机制（适当时）
```

### 2. 参数验证
```
- 验证所有输入参数
- 提供合理的默认值
- 限制参数范围和格式
- 清理和标准化输入数据
```

### 3. 响应格式
```
- 使用一致的响应结构
- 包含元数据信息
- 提供操作状态指示
- 包含时间戳和追踪信息
```

### 4. 性能优化
```
- 实现适当的缓存策略
- 使用连接池和重用
- 设置合理的超时时间
- 监控API调用性能
```

### 5. 安全考虑
```
- 验证和清理所有输入
- 不在日志中记录敏感信息
- 使用安全的API认证方式
- 实现适当的访问控制
```

---

## 检查清单

使用此模板开发工具时，请确保：

- [ ] **功能完整性**：工具实现了所有预期功能
- [ ] **参数验证**：所有输入参数都经过验证
- [ ] **错误处理**：妥善处理各种错误情况
- [ ] **文档完整**：工具定义和使用说明清晰
- [ ] **测试覆盖**：包含单元测试和集成测试
- [ ] **性能合理**：响应时间在可接受范围内
- [ ] **安全合规**：遵循安全最佳实践
- [ ] **日志记录**：包含适当的日志信息

此模板为MCP工具开发提供了完整的框架和最佳实践，确保开发出高质量、可维护的工具实现。
# MCP Server 文档生成指南

## 任务概述
指导AI助手自动生成完整、准确、用户友好的MCP Server文档。

---

## 第1步：文档生成策略

### 输入要求
- 完整的MCP Server实现
- 所有工具、提示词、资源的定义
- 配置和部署信息

### 执行步骤

#### 1.1 文档结构设计

**标准文档结构**
```
docs/
├── README.md                 # 项目概述和快速开始
├── api/                      # API参考文档
│   ├── tools.md             # 工具API文档
│   ├── prompts.md           # 提示词API文档
│   └── resources.md         # 资源API文档
├── guides/                   # 使用指南
│   ├── installation.md      # 安装指南
│   ├── configuration.md     # 配置指南
│   ├── integration.md       # 集成指南
│   └── troubleshooting.md   # 故障排除
├── examples/                 # 示例代码
│   ├── basic-usage.md       # 基础使用示例
│   ├── advanced-scenarios.md # 高级场景示例
│   └── client-examples/     # 客户端示例
├── deployment/               # 部署文档
│   ├── docker.md           # Docker部署
│   ├── kubernetes.md       # Kubernetes部署
│   └── monitoring.md       # 监控和维护
└── development/              # 开发文档
    ├── architecture.md      # 架构说明
    ├── contributing.md      # 贡献指南
    └── changelog.md         # 更新日志
```

#### 1.2 文档生成器实现

**Python文档生成器**
```python
# scripts/generate_docs.py
import os
import json
import inspect
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from jinja2 import Environment, FileSystemLoader
import importlib.util

@dataclass
class ToolDocumentation:
    name: str
    description: str
    parameters: Dict[str, Any]
    examples: List[Dict[str, Any]]
    returns: Dict[str, Any]
    errors: List[str]

@dataclass
class PromptDocumentation:
    name: str
    description: str
    arguments: List[Dict[str, Any]]
    examples: List[Dict[str, Any]]
    use_cases: List[str]

@dataclass
class ResourceDocumentation:
    uri: str
    name: str
    description: str
    mime_type: str
    examples: List[Dict[str, Any]]
    access_patterns: List[str]

class DocumentationGenerator:
    \"\"\"MCP Server文档生成器\"\"\"
    
    def __init__(self, project_root: str, output_dir: str = "docs"):
        self.project_root = Path(project_root)
        self.output_dir = Path(output_dir)
        self.templates_dir = self.project_root / "docs" / "templates"
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置Jinja2模板环境
        self.env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
    
    async def generate_all_docs(self):
        \"\"\"生成所有文档\"\"\"
        print("📚 Starting documentation generation...")
        
        # 1. 分析MCP Server组件
        server_info = await self._analyze_server()
        
        # 2. 生成API文档
        await self._generate_api_docs(server_info)
        
        # 3. 生成用户指南
        await self._generate_guides(server_info)
        
        # 4. 生成示例文档
        await self._generate_examples(server_info)
        
        # 5. 生成部署文档
        await self._generate_deployment_docs(server_info)
        
        # 6. 生成开发文档
        await self._generate_development_docs(server_info)
        
        # 7. 生成主README
        await self._generate_readme(server_info)
        
        print("✅ Documentation generation completed!")
    
    async def _analyze_server(self) -> Dict[str, Any]:
        \"\"\"分析MCP Server组件\"\"\"
        print("🔍 Analyzing MCP Server components...")
        
        # 动态加载服务器模块
        server_module = self._load_server_module()
        
        # 提取组件信息
        tools = await self._extract_tools_info(server_module)
        prompts = await self._extract_prompts_info(server_module)
        resources = await self._extract_resources_info(server_module)
        config = self._extract_config_info()
        
        return {
            "tools": tools,
            "prompts": prompts,
            "resources": resources,
            "config": config,
            "server_info": self._get_server_info()
        }
    
    def _load_server_module(self):
        \"\"\"动态加载服务器模块\"\"\"
        server_path = self.project_root / "src" / "server.py"
        if not server_path.exists():
            # 尝试其他可能的路径
            server_path = self.project_root / "main.py"
        
        if not server_path.exists():
            raise FileNotFoundError("Server module not found")
        
        spec = importlib.util.spec_from_file_location("server", server_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        return module
    
    async def _extract_tools_info(self, server_module) -> List[ToolDocumentation]:
        \"\"\"提取工具信息\"\"\"
        tools = []
        
        # 查找工具管理器
        tool_manager = getattr(server_module, 'tool_manager', None)
        if tool_manager:
            for tool_name, tool_def in tool_manager._tools.items():
                doc = ToolDocumentation(
                    name=tool_def.name,
                    description=tool_def.description,
                    parameters=tool_def.inputSchema.get('properties', {}),
                    examples=self._generate_tool_examples(tool_def),
                    returns={"type": "object", "description": "Tool execution result"},
                    errors=self._get_common_errors()
                )
                tools.append(doc)
        
        return tools
    
    async def _extract_prompts_info(self, server_module) -> List[PromptDocumentation]:
        \"\"\"提取提示词信息\"\"\"
        prompts = []
        
        prompt_manager = getattr(server_module, 'prompt_manager', None)
        if prompt_manager:
            for prompt_name, prompt_def in prompt_manager._prompts.items():
                doc = PromptDocumentation(
                    name=prompt_def.name,
                    description=prompt_def.description,
                    arguments=[arg.__dict__ for arg in prompt_def.arguments],
                    examples=self._generate_prompt_examples(prompt_def),
                    use_cases=self._get_prompt_use_cases(prompt_def)
                )
                prompts.append(doc)
        
        return prompts
    
    async def _extract_resources_info(self, server_module) -> List[ResourceDocumentation]:
        \"\"\"提取资源信息\"\"\"
        resources = []
        
        resource_manager = getattr(server_module, 'resource_manager', None)
        if resource_manager:
            for resource_uri, resource_def in resource_manager._resources.items():
                doc = ResourceDocumentation(
                    uri=resource_def.uri,
                    name=resource_def.name,
                    description=resource_def.description,
                    mime_type=resource_def.mimeType,
                    examples=self._generate_resource_examples(resource_def),
                    access_patterns=self._get_resource_access_patterns(resource_def)
                )
                resources.append(doc)
        
        return resources
    
    def _extract_config_info(self) -> Dict[str, Any]:
        \"\"\"提取配置信息\"\"\"
        config_file = self.project_root / "config" / "default.json"
        if config_file.exists():
            with open(config_file, 'r') as f:
                return json.load(f)
        return {}
    
    def _get_server_info(self) -> Dict[str, Any]:
        \"\"\"获取服务器基本信息\"\"\"
        package_json = self.project_root / "package.json"
        pyproject_toml = self.project_root / "pyproject.toml"
        
        info = {
            "name": "MCP Server",
            "version": "1.0.0",
            "description": "Model Context Protocol Server",
            "language": "Unknown"
        }
        
        if package_json.exists():
            with open(package_json, 'r') as f:
                data = json.load(f)
                info.update({
                    "name": data.get("name", info["name"]),
                    "version": data.get("version", info["version"]),
                    "description": data.get("description", info["description"]),
                    "language": "TypeScript/JavaScript"
                })
        elif pyproject_toml.exists():
            import tomllib
            with open(pyproject_toml, 'rb') as f:
                data = tomllib.load(f)
                project = data.get("project", {})
                info.update({
                    "name": project.get("name", info["name"]),
                    "version": project.get("version", info["version"]),
                    "description": project.get("description", info["description"]),
                    "language": "Python"
                })
        
        return info
    
    def _generate_tool_examples(self, tool_def) -> List[Dict[str, Any]]:
        \"\"\"生成工具使用示例\"\"\"
        examples = []
        
        # 基于输入模式生成示例
        properties = tool_def.inputSchema.get('properties', {})
        required = tool_def.inputSchema.get('required', [])
        
        if properties:
            example_args = {}
            for param_name, param_def in properties.items():
                if param_def.get('type') == 'string':
                    example_args[param_name] = f"example_{param_name}"
                elif param_def.get('type') == 'integer':
                    example_args[param_name] = 42
                elif param_def.get('type') == 'boolean':
                    example_args[param_name] = True
                elif param_def.get('enum'):
                    example_args[param_name] = param_def['enum'][0]
            
            examples.append({
                "title": "Basic Usage",
                "description": f"Basic example of using {tool_def.name}",
                "request": {
                    "method": "tools/call",
                    "params": {
                        "name": tool_def.name,
                        "arguments": example_args
                    }
                },
                "response": {
                    "content": [
                        {
                            "type": "text",
                            "text": "Example successful response"
                        }
                    ]
                }
            })
        
        return examples
    
    def _generate_prompt_examples(self, prompt_def) -> List[Dict[str, Any]]:
        \"\"\"生成提示词使用示例\"\"\"
        examples = []
        
        # 生成基础示例
        example_args = {}
        for arg in prompt_def.arguments:
            if arg.name and arg.required:
                example_args[arg.name] = f"example_{arg.name}_value"
        
        examples.append({
            "title": "Basic Usage",
            "description": f"Basic example of using {prompt_def.name}",
            "request": {
                "method": "prompts/get",
                "params": {
                    "name": prompt_def.name,
                    "arguments": example_args
                }
            },
            "response": {
                "messages": [
                    {
                        "role": "system",
                        "content": {
                            "type": "text",
                            "text": "Example system message"
                        }
                    },
                    {
                        "role": "user",
                        "content": {
                            "type": "text",
                            "text": "Example user message"
                        }
                    }
                ]
            }
        })
        
        return examples
    
    def _generate_resource_examples(self, resource_def) -> List[Dict[str, Any]]:
        \"\"\"生成资源访问示例\"\"\"
        examples = []
        
        examples.append({
            "title": "Read Resource",
            "description": f"Read {resource_def.name} resource",
            "request": {
                "method": "resources/read",
                "params": {
                    "uri": resource_def.uri
                }
            },
            "response": {
                "contents": [
                    {
                        "uri": resource_def.uri,
                        "mimeType": resource_def.mimeType,
                        "text": "Example resource content" if "text" in resource_def.mimeType else None,
                        "blob": "base64encodeddata" if "text" not in resource_def.mimeType else None
                    }
                ]
            }
        })
        
        return examples
    
    def _get_common_errors(self) -> List[str]:
        \"\"\"获取常见错误列表\"\"\"
        return [
            "Invalid parameters",
            "Authentication failed",
            "Rate limit exceeded",
            "Resource not found",
            "Internal server error"
        ]
    
    def _get_prompt_use_cases(self, prompt_def) -> List[str]:
        \"\"\"获取提示词使用场景\"\"\"
        # 基于提示词名称和描述推断使用场景
        use_cases = []
        
        name_lower = prompt_def.name.lower()
        desc_lower = prompt_def.description.lower()
        
        if "content" in name_lower or "generate" in name_lower:
            use_cases.append("Content generation")
        if "analysis" in name_lower or "analyze" in name_lower:
            use_cases.append("Data analysis")
        if "code" in name_lower or "programming" in desc_lower:
            use_cases.append("Code generation")
        if "summary" in name_lower or "summarize" in desc_lower:
            use_cases.append("Text summarization")
        
        if not use_cases:
            use_cases.append("General purpose")
        
        return use_cases
    
    def _get_resource_access_patterns(self, resource_def) -> List[str]:
        \"\"\"获取资源访问模式\"\"\"
        patterns = ["Read-only access"]
        
        if "config" in resource_def.uri.lower():
            patterns.append("Configuration data")
        if "log" in resource_def.uri.lower():
            patterns.append("Log data streaming")
        if "api" in resource_def.uri.lower():
            patterns.append("Real-time API data")
        if "file" in resource_def.uri.lower():
            patterns.append("File system access")
        
        return patterns
    
    async def _generate_api_docs(self, server_info: Dict[str, Any]):
        \"\"\"生成API文档\"\"\"
        print("📄 Generating API documentation...")
        
        api_dir = self.output_dir / "api"
        api_dir.mkdir(exist_ok=True)
        
        # 生成工具API文档
        if server_info["tools"]:
            template = self.env.get_template("tools_api.md.j2")
            content = template.render(
                tools=server_info["tools"],
                server_info=server_info["server_info"]
            )
            
            with open(api_dir / "tools.md", 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 生成提示词API文档
        if server_info["prompts"]:
            template = self.env.get_template("prompts_api.md.j2")
            content = template.render(
                prompts=server_info["prompts"],
                server_info=server_info["server_info"]
            )
            
            with open(api_dir / "prompts.md", 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 生成资源API文档
        if server_info["resources"]:
            template = self.env.get_template("resources_api.md.j2")
            content = template.render(
                resources=server_info["resources"],
                server_info=server_info["server_info"]
            )
            
            with open(api_dir / "resources.md", 'w', encoding='utf-8') as f:
                f.write(content)
    
    async def _generate_guides(self, server_info: Dict[str, Any]):
        \"\"\"生成用户指南\"\"\"
        print("📖 Generating user guides...")
        
        guides_dir = self.output_dir / "guides"
        guides_dir.mkdir(exist_ok=True)
        
        # 安装指南
        template = self.env.get_template("installation.md.j2")
        content = template.render(server_info=server_info["server_info"])
        with open(guides_dir / "installation.md", 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 配置指南
        template = self.env.get_template("configuration.md.j2")
        content = template.render(
            config=server_info["config"],
            server_info=server_info["server_info"]
        )
        with open(guides_dir / "configuration.md", 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 集成指南
        template = self.env.get_template("integration.md.j2")
        content = template.render(
            tools=server_info["tools"],
            prompts=server_info["prompts"],
            resources=server_info["resources"],
            server_info=server_info["server_info"]
        )
        with open(guides_dir / "integration.md", 'w', encoding='utf-8') as f:
            f.write(content)
    
    async def _generate_examples(self, server_info: Dict[str, Any]):
        \"\"\"生成示例文档\"\"\"
        print("💡 Generating examples...")
        
        examples_dir = self.output_dir / "examples"
        examples_dir.mkdir(exist_ok=True)
        
        # 基础使用示例
        template = self.env.get_template("basic_usage.md.j2")
        content = template.render(
            tools=server_info["tools"][:3],  # 只展示前3个工具
            prompts=server_info["prompts"][:2],  # 只展示前2个提示词
            resources=server_info["resources"][:2],  # 只展示前2个资源
            server_info=server_info["server_info"]
        )
        with open(examples_dir / "basic-usage.md", 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 高级场景示例
        template = self.env.get_template("advanced_scenarios.md.j2")
        content = template.render(server_info=server_info)
        with open(examples_dir / "advanced-scenarios.md", 'w', encoding='utf-8') as f:
            f.write(content)
    
    async def _generate_deployment_docs(self, server_info: Dict[str, Any]):
        \"\"\"生成部署文档\"\"\"
        print("🚀 Generating deployment documentation...")
        
        deployment_dir = self.output_dir / "deployment"
        deployment_dir.mkdir(exist_ok=True)
        
        # Docker部署文档
        template = self.env.get_template("docker_deployment.md.j2")
        content = template.render(server_info=server_info["server_info"])
        with open(deployment_dir / "docker.md", 'w', encoding='utf-8') as f:
            f.write(content)
    
    async def _generate_development_docs(self, server_info: Dict[str, Any]):
        \"\"\"生成开发文档\"\"\"
        print("🛠️ Generating development documentation...")
        
        dev_dir = self.output_dir / "development"
        dev_dir.mkdir(exist_ok=True)
        
        # 架构文档
        template = self.env.get_template("architecture.md.j2")
        content = template.render(server_info=server_info)
        with open(dev_dir / "architecture.md", 'w', encoding='utf-8') as f:
            f.write(content)
    
    async def _generate_readme(self, server_info: Dict[str, Any]):
        \"\"\"生成主README文档\"\"\"
        print("📋 Generating README...")
        
        template = self.env.get_template("README.md.j2")
        content = template.render(
            server_info=server_info["server_info"],
            tools_count=len(server_info["tools"]),
            prompts_count=len(server_info["prompts"]),
            resources_count=len(server_info["resources"]),
            featured_tools=server_info["tools"][:3],
            featured_prompts=server_info["prompts"][:2]
        )
        
        with open(self.output_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(content)

async def main():
    \"\"\"主函数\"\"\"
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate MCP Server documentation")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--output-dir", default="docs", help="Output directory for documentation")
    
    args = parser.parse_args()
    
    generator = DocumentationGenerator(args.project_root, args.output_dir)
    await generator.generate_all_docs()

if __name__ == "__main__":
    asyncio.run(main())
```

### 验证标准
- [ ] 文档生成器可以正确分析MCP组件
- [ ] 生成的文档结构完整
- [ ] API文档准确反映实际接口
- [ ] 示例代码可以正常运行

### 输出结果
```
文档生成系统包含：
1. 自动化文档生成器 (DocumentationGenerator)
2. 组件分析和信息提取
3. 模板化文档生成
4. 多种文档类型支持
```

---

## 第2步：文档模板系统

### 输入要求
- 文档生成器实现
- 文档内容结构设计
- 品牌和样式要求

### 执行步骤

#### 2.1 Jinja2文档模板

**主README模板**
```jinja2
{# docs/templates/README.md.j2 #}
# {{ server_info.name }}

{{ server_info.description }}

[![Version](https://img.shields.io/badge/version-{{ server_info.version }}-blue.svg)](https://github.com/your-org/{{ server_info.name }})
[![Language](https://img.shields.io/badge/language-{{ server_info.language }}-green.svg)]()
[![MCP Protocol](https://img.shields.io/badge/MCP-compatible-orange.svg)](https://modelcontextprotocol.io)

## 🚀 快速开始

### 安装

{% if server_info.language == "Python" %}
```bash
pip install {{ server_info.name }}
```

### 运行
```bash
python -m {{ server_info.name }}
```
{% elif server_info.language == "TypeScript" or server_info.language == "JavaScript" %}
```bash
npm install {{ server_info.name }}
```

### 运行
```bash
npm start
# 或
node dist/index.js
```
{% endif %}

### 配置

创建 `.env` 文件：
```env
API_BASE_URL=https://your-api-endpoint.com
API_KEY=your-api-key
DEBUG=false
```

## 📋 功能特性

### 🛠️ 工具 ({{ tools_count }} 个)
{% for tool in featured_tools %}
- **{{ tool.name }}**: {{ tool.description }}
{% endfor %}
{% if tools_count > 3 %}
- ... 还有 {{ tools_count - 3 }} 个工具

查看 [完整工具列表](docs/api/tools.md)
{% endif %}

### 💬 提示词 ({{ prompts_count }} 个)
{% for prompt in featured_prompts %}
- **{{ prompt.name }}**: {{ prompt.description }}
{% endfor %}
{% if prompts_count > 2 %}
- ... 还有 {{ prompts_count - 2 }} 个提示词

查看 [完整提示词列表](docs/api/prompts.md)
{% endif %}

### 📁 资源 ({{ resources_count }} 个)
提供对各种数据源的访问，包括：
- 配置信息
- 实时数据
- 文件系统
- API端点

查看 [完整资源列表](docs/api/resources.md)

## 📖 文档

- [安装指南](docs/guides/installation.md)
- [配置指南](docs/guides/configuration.md) 
- [集成指南](docs/guides/integration.md)
- [API参考](docs/api/)
- [使用示例](docs/examples/)
- [部署指南](docs/deployment/)

## 🔧 开发

查看 [开发文档](docs/development/) 了解如何：
- 设置开发环境
- 添加新功能
- 运行测试
- 贡献代码

## 📄 许可证

本项目使用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献！请阅读 [贡献指南](docs/development/contributing.md)。

## 📞 支持

- 📧 Email: <EMAIL>
- 💬 Discord: [加入我们的社区](https://discord.gg/example)
- 🐛 问题报告: [GitHub Issues](https://github.com/your-org/{{ server_info.name }}/issues)
```

**工具API文档模板**
```jinja2
{# docs/templates/tools_api.md.j2 #}
# 工具 API 参考

{{ server_info.name }} 提供以下工具供客户端调用。

## 概述

工具是 MCP 协议中用于执行特定操作的函数。每个工具都有明确定义的输入参数和输出格式。

### 调用方式

所有工具都通过 MCP 协议的 `tools/call` 方法调用：

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "tool_name",
    "arguments": {
      "param1": "value1",
      "param2": "value2"
    }
  }
}
```

## 工具列表

{% for tool in tools %}
## {{ tool.name }}

{{ tool.description }}

### 参数

{% if tool.parameters %}
| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
{% for param_name, param_def in tool.parameters.items() %}
| `{{ param_name }}` | {{ param_def.type | default('string') }} | {{ '✓' if param_name in tool.required else '✗' }} | {{ param_def.description | default('无描述') }} |
{% endfor %}
{% else %}
此工具不需要参数。
{% endif %}

### 返回值

{{ tool.returns.description | default('返回工具执行结果') }}

```json
{
  "type": "{{ tool.returns.type | default('object') }}",
  "properties": {
    "success": {
      "type": "boolean",
      "description": "操作是否成功"
    },
    "data": {
      "type": "object", 
      "description": "返回的数据"
    },
    "message": {
      "type": "string",
      "description": "操作消息"
    }
  }
}
```

### 示例

{% for example in tool.examples %}
#### {{ example.title }}

{{ example.description }}

**请求:**
```json
{{ example.request | tojson(indent=2) }}
```

**响应:**
```json  
{{ example.response | tojson(indent=2) }}
```
{% endfor %}

### 可能的错误

{% for error in tool.errors %}
- {{ error }}
{% endfor %}

---

{% endfor %}

## 错误处理

当工具调用失败时，会返回包含错误信息的响应：

```json
{
  "content": [
    {
      "type": "text",
      "text": "错误描述"
    }
  ],
  "isError": true
}
```

## 最佳实践

1. **参数验证**: 始终验证输入参数的格式和值
2. **错误处理**: 妥善处理工具调用可能的错误
3. **超时设置**: 为工具调用设置合理的超时时间
4. **重试机制**: 对于临时性错误，实现适当的重试逻辑
5. **日志记录**: 记录工具调用的详细信息便于调试
```

**配置指南模板**
```jinja2
{# docs/templates/configuration.md.j2 #}
# 配置指南

{{ server_info.name }} 支持多种配置方式，以满足不同环境和使用场景的需求。

## 配置优先级

配置按以下优先级顺序加载（高优先级覆盖低优先级）：

1. 命令行参数
2. 环境变量
3. 配置文件
4. 默认值

## 环境变量

可以通过环境变量配置 {{ server_info.name }}：

{% if config %}
### 基础配置

| 环境变量 | 默认值 | 描述 |
|----------|--------|------|
{% for key, value in config.items() %}
| `{{ key | upper }}` | `{{ value }}` | {{ key | replace('_', ' ') | title }} |
{% endfor %}
{% endif %}

### 常用环境变量

```bash
# API配置
export API_BASE_URL="https://your-api.com"
export API_KEY="your-api-key"
export API_TIMEOUT="30"

# 缓存配置  
export CACHE_ENABLED="true"
export CACHE_TTL="300"

# 日志配置
export LOG_LEVEL="info"
export DEBUG="false"

# 性能配置
export MAX_CONCURRENT_REQUESTS="10"
```

## 配置文件

### 开发环境配置

创建 `config/development.json`：

```json
{
  "debug": true,
  "logLevel": "debug",
  "cacheEnabled": false,
  "apiTimeout": 10
}
```

### 生产环境配置

创建 `config/production.json`：

```json
{
  "debug": false,
  "logLevel": "info", 
  "cacheEnabled": true,
  "cacheTtl": 600,
  "maxConcurrentRequests": 50
}
```

### 配置文件位置

配置文件按以下顺序查找：

1. `./config/{environment}.json`
2. `./config/default.json`
3. `~/.{{ server_info.name }}/config.json`

## 命令行参数

{% if server_info.language == "Python" %}
```bash
python -m {{ server_info.name }} \
  --api-base-url https://api.example.com \
  --api-key your-key \
  --debug \
  --log-level info
```
{% else %}
```bash
node dist/index.js \
  --api-base-url https://api.example.com \
  --api-key your-key \
  --debug \
  --log-level info
```
{% endif %}

## Docker 配置

### 环境变量方式

```bash
docker run -e API_BASE_URL=https://api.example.com \
           -e API_KEY=your-key \
           {{ server_info.name }}
```

### 配置文件挂载

```bash
docker run -v ./config:/app/config \
           {{ server_info.name }}
```

### Docker Compose

```yaml
version: '3.8'
services:
  {{ server_info.name }}:
    image: {{ server_info.name }}:latest
    environment:
      - API_BASE_URL=https://api.example.com
      - API_KEY=\${API_KEY}
      - LOG_LEVEL=info
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
```

## 配置验证

启动时会自动验证配置。如果发现问题：

1. **缺少必需配置**: 
   ```
   Error: Required configuration missing: API_BASE_URL
   ```

2. **配置格式错误**:
   ```
   Error: Invalid API_BASE_URL format
   ```

3. **配置值超出范围**:
   ```
   Error: API_TIMEOUT must be between 1 and 300 seconds
   ```

## 高级配置

### 安全配置

```json
{
  "security": {
    "rateLimiting": {
      "enabled": true,
      "maxRequests": 100,
      "windowMs": 60000
    },
    "cors": {
      "enabled": true,
      "origins": ["https://trusted-domain.com"]
    }
  }
}
```

### 监控配置

```json
{
  "monitoring": {
    "metricsEnabled": true,
    "metricsPort": 8080,
    "healthCheckInterval": 30
  }
}
```

### 缓存配置

```json
{
  "cache": {
    "enabled": true,
    "ttl": 300,
    "maxSize": 1000,
    "compressionEnabled": true
  }
}
```

## 故障排除

### 配置不生效

1. 检查配置文件路径和权限
2. 验证JSON格式是否正确
3. 确认环境变量名称拼写正确
4. 查看启动日志中的配置加载信息

### 性能问题

- 调整 `maxConcurrentRequests` 控制并发
- 启用缓存减少重复请求
- 设置合适的 `apiTimeout` 避免长时间等待

### 内存使用

- 限制 `cache.maxSize` 控制缓存大小
- 定期重启清理内存泄漏
- 监控内存使用趋势

## 最佳实践

1. **环境分离**: 为不同环境使用不同的配置文件
2. **敏感信息**: 使用环境变量存储API密钥等敏感信息
3. **配置验证**: 启动前验证所有必需配置
4. **监控配置**: 启用监控了解系统运行状态
5. **备份配置**: 定期备份重要的配置文件
```

### 验证标准
- [ ] 模板语法正确无误
- [ ] 生成的文档格式整齐
- [ ] 内容准确反映实际功能
- [ ] 示例代码可以直接使用

### 输出结果
```
文档模板系统包含：
1. Jinja2模板引擎集成
2. 多种文档类型模板
3. 动态内容生成
4. 一致的文档风格
```

---

## 第3步：自动化文档更新

### 输入要求
- 完整的文档生成系统
- CI/CD流水线集成需求
- 版本控制和发布流程

### 执行步骤

#### 3.1 CI/CD集成

**GitHub Actions工作流**
```yaml
# .github/workflows/docs.yml
name: Generate Documentation

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  generate-docs:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
    
    - name: Install dependencies
      run: |
        pip install -r requirements-dev.txt
        npm install
    
    - name: Generate documentation
      run: |
        python scripts/generate_docs.py --output-dir docs-generated
    
    - name: Validate documentation
      run: |
        # 检查必需文件是否生成
        test -f docs-generated/README.md
        test -f docs-generated/api/tools.md
        test -f docs-generated/guides/installation.md
        
        # 检查文档质量
        python scripts/validate_docs.py docs-generated
    
    - name: Deploy to GitHub Pages
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs-generated
        destination_dir: docs
    
    - name: Update README
      if: github.ref == 'refs/heads/main'
      run: |
        cp docs-generated/README.md ./README.md
        git config user.name github-actions
        git config user.email <EMAIL>
        git add README.md
        git diff --staged --quiet || git commit -m "docs: auto-update README"
        git push
    
    - name: Create documentation artifact
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: docs-generated/
        retention-days: 30
```

**文档验证脚本**
```python
# scripts/validate_docs.py
import os
import re
import sys
from pathlib import Path
from typing import List, Tuple

class DocumentationValidator:
    \"\"\"文档质量验证器\"\"\"
    
    def __init__(self, docs_dir: str):
        self.docs_dir = Path(docs_dir)
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def validate_all(self) -> bool:
        \"\"\"验证所有文档\"\"\"
        print("🔍 Validating documentation...")
        
        # 检查文件结构
        self._check_file_structure()
        
        # 检查内容质量
        self._check_content_quality()
        
        # 检查链接有效性
        self._check_links()
        
        # 输出结果
        self._print_results()
        
        return len(self.errors) == 0
    
    def _check_file_structure(self):
        \"\"\"检查文件结构\"\"\"
        required_files = [
            "README.md",
            "api/tools.md",
            "guides/installation.md",
            "guides/configuration.md",
            "examples/basic-usage.md"
        ]
        
        for file_path in required_files:
            full_path = self.docs_dir / file_path
            if not full_path.exists():
                self.errors.append(f"Missing required file: {file_path}")
            elif full_path.stat().st_size == 0:
                self.errors.append(f"Empty file: {file_path}")
    
    def _check_content_quality(self):
        \"\"\"检查内容质量\"\"\"
        for md_file in self.docs_dir.rglob("*.md"):
            self._validate_markdown_file(md_file)
    
    def _validate_markdown_file(self, file_path: Path):
        \"\"\"验证单个Markdown文件\"\"\"
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            relative_path = file_path.relative_to(self.docs_dir)
            
            # 检查基本结构
            if not content.strip():
                self.errors.append(f"{relative_path}: Empty file")
                return
            
            # 检查标题
            if not re.search(r'^# .+', content, re.MULTILINE):
                self.warnings.append(f"{relative_path}: No main heading (# title)")
            
            # 检查代码块
            code_blocks = re.findall(r'```(\w+)?\\n([^`]+)```', content, re.MULTILINE | re.DOTALL)
            for lang, code in code_blocks:
                if lang in ['json', 'javascript', 'python']:
                    if not self._validate_code_block(code, lang):
                        self.warnings.append(f"{relative_path}: Invalid {lang} code block")
            
            # 检查链接格式
            links = re.findall(r'\\[([^\\]]+)\\]\\(([^)]+)\\)', content)
            for link_text, link_url in links:
                if not link_text.strip():
                    self.warnings.append(f"{relative_path}: Empty link text")
            
            # 检查图片
            images = re.findall(r'!\\[([^\\]]*)\\]\\(([^)]+)\\)', content)
            for alt_text, img_url in images:
                if not alt_text.strip():
                    self.warnings.append(f"{relative_path}: Missing image alt text")
        
        except Exception as e:
            self.errors.append(f"{relative_path}: Failed to read file - {str(e)}")
    
    def _validate_code_block(self, code: str, language: str) -> bool:
        \"\"\"验证代码块语法\"\"\"
        try:
            if language == 'json':
                import json
                json.loads(code)
            elif language == 'python':
                import ast
                ast.parse(code)
            # JavaScript验证较复杂，暂时跳过
            return True
        except:
            return False
    
    def _check_links(self):
        \"\"\"检查链接有效性\"\"\"
        for md_file in self.docs_dir.rglob("*.md"):
            self._check_file_links(md_file)
    
    def _check_file_links(self, file_path: Path):
        \"\"\"检查文件中的链接\"\"\"
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            relative_path = file_path.relative_to(self.docs_dir)
            
            # 查找内部链接
            internal_links = re.findall(r'\\]\\(([^)]+\\.md[^)]*)\\)', content)
            
            for link in internal_links:
                # 移除锚点
                clean_link = link.split('#')[0]
                if clean_link:
                    # 解析相对路径
                    if clean_link.startswith('/'):
                        target_path = self.docs_dir / clean_link.lstrip('/')
                    else:
                        target_path = file_path.parent / clean_link
                    
                    if not target_path.exists():
                        self.errors.append(f"{relative_path}: Broken link to {clean_link}")
        
        except Exception as e:
            self.errors.append(f"{relative_path}: Failed to check links - {str(e)}")
    
    def _print_results(self):
        \"\"\"输出验证结果\"\"\"
        if self.errors:
            print("\\n❌ Validation Errors:")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.warnings:
            print("\\n⚠️  Validation Warnings:")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        if not self.errors and not self.warnings:
            print("\\n✅ All documentation validation checks passed!")
        
        print(f"\\nSummary: {len(self.errors)} errors, {len(self.warnings)} warnings")

def main():
    if len(sys.argv) != 2:
        print("Usage: python validate_docs.py <docs_directory>")
        sys.exit(1)
    
    docs_dir = sys.argv[1]
    validator = DocumentationValidator(docs_dir)
    
    if validator.validate_all():
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
```

#### 3.2 版本化文档管理

**版本化文档脚本**
```python
# scripts/version_docs.py
import os
import shutil
import json
from pathlib import Path
from datetime import datetime
import subprocess

class DocumentationVersionManager:
    \"\"\"文档版本管理器\"\"\"
    
    def __init__(self, docs_dir: str = "docs", versions_dir: str = "versions"):
        self.docs_dir = Path(docs_dir)
        self.versions_dir = Path(versions_dir)
        self.versions_dir.mkdir(exist_ok=True)
    
    def create_version(self, version: str, description: str = ""):
        \"\"\"创建文档版本\"\"\"
        version_dir = self.versions_dir / version
        
        if version_dir.exists():
            print(f"Version {version} already exists")
            return False
        
        # 复制当前文档
        shutil.copytree(self.docs_dir, version_dir)
        
        # 创建版本信息
        version_info = {
            "version": version,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "git_commit": self._get_git_commit(),
            "files_count": len(list(version_dir.rglob("*.md")))
        }
        
        with open(version_dir / "version.json", 'w') as f:
            json.dump(version_info, f, indent=2)
        
        # 更新版本索引
        self._update_versions_index()
        
        print(f"✅ Created documentation version: {version}")
        return True
    
    def list_versions(self):
        \"\"\"列出所有版本\"\"\"
        versions = []
        
        for version_dir in self.versions_dir.iterdir():
            if version_dir.is_dir():
                version_file = version_dir / "version.json"
                if version_file.exists():
                    with open(version_file, 'r') as f:
                        versions.append(json.load(f))
        
        # 按创建时间排序
        versions.sort(key=lambda x: x['created_at'], reverse=True)
        return versions
    
    def _get_git_commit(self) -> str:
        \"\"\"获取当前Git提交\"\"\"
        try:
            result = subprocess.run(['git', 'rev-parse', 'HEAD'], 
                                  capture_output=True, text=True)
            return result.stdout.strip()
        except:
            return "unknown"
    
    def _update_versions_index(self):
        \"\"\"更新版本索引\"\"\"
        versions = self.list_versions()
        
        index_content = "# 文档版本历史\\n\\n"
        index_content += "| 版本 | 描述 | 创建时间 | 文件数 |\\n"
        index_content += "|------|------|----------|--------|\\n"
        
        for version in versions:
            created_at = datetime.fromisoformat(version['created_at']).strftime('%Y-%m-%d %H:%M')
            index_content += f"| [{version['version']}](./{version['version']}/) | {version['description']} | {created_at} | {version['files_count']} |\\n"
        
        with open(self.versions_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(index_content)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Manage documentation versions")
    parser.add_argument("command", choices=["create", "list"], help="Command to execute")
    parser.add_argument("--version", help="Version identifier")
    parser.add_argument("--description", help="Version description")
    
    args = parser.parse_args()
    
    manager = DocumentationVersionManager()
    
    if args.command == "create":
        if not args.version:
            print("Error: --version is required for create command")
            return
        
        manager.create_version(args.version, args.description or "")
    
    elif args.command == "list":
        versions = manager.list_versions()
        if versions:
            print("📚 Documentation Versions:")
            for version in versions:
                created_at = datetime.fromisoformat(version['created_at']).strftime('%Y-%m-%d %H:%M')
                print(f"  {version['version']} - {version['description']} ({created_at})")
        else:
            print("No versions found")

if __name__ == "__main__":
    main()
```

### 验证标准
- [ ] CI/CD流水线正确触发文档生成
- [ ] 文档验证捕获常见问题
- [ ] 版本管理功能正常工作
- [ ] 自动部署成功执行

### 输出结果
```
自动化文档系统包含：
1. CI/CD集成工作流
2. 文档质量验证
3. 版本化管理
4. 自动部署机制
```

---

## 完成检查清单

### 文档生成
- [ ] **组件分析**：正确提取MCP组件信息
- [ ] **模板系统**：灵活的文档模板框架
- [ ] **内容生成**：准确的API文档和示例
- [ ] **多格式支持**：支持多种文档类型

### 文档质量
- [ ] **结构完整**：标准化的文档结构
- [ ] **内容准确**：反映实际功能和接口
- [ ] **示例可用**：代码示例可以直接运行
- [ ] **链接有效**：内部链接正确无误

### 自动化管理
- [ ] **CI/CD集成**：自动触发文档更新
- [ ] **质量验证**：自动检查文档质量
- [ ] **版本控制**：文档版本管理机制
- [ ] **部署自动化**：自动发布到文档站点

## 下一步行动

✅ **文档生成完成后，整个MCP Server开发流程提示词系统已完成！**

系统包含以下核心组件：
- 📋 **项目初始化流程** - 需求分析到项目搭建
- 🐍 **Python开发工作流** - 完整的Python实现指南  
- 📘 **TypeScript开发工作流** - 完整的TypeScript实现指南
- 🛠️ **工具开发模板** - 标准化的工具实现框架
- 💬 **提示词开发模板** - 结构化的提示词生成系统
- 📁 **资源开发模板** - 多样化的资源访问模式
- 🧪 **测试部署指南** - 全面的测试和部署流程
- ⚙️ **配置调试指南** - 完善的配置管理和问题诊断
- 📚 **文档生成系统** - 自动化的文档生成和管理

此系统为AI助手提供了完整的MCP Server开发指导，确保能够快速构建功能完整、符合协议标准的服务器应用。
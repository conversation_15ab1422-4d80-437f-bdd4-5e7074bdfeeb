# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.venv/
venv/
ENV/
env/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*.sublime-project
*.sublime-workspace

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 数据库
*.db
*.sqlite
*.sqlite3
data/

# 临时文件
tmp/
temp/
.tmp/

# 测试覆盖率
.coverage
.pytest_cache/
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# 钉钉相关敏感文件
dingtalk_credentials.json
access_token.cache

# 备份文件
*.bak
*.backup

# 钉钉MCP服务器使用指南

## 概述

钉钉MCP服务器为<PERSON>提供了完整的钉钉企业功能集成，包括待办任务管理、工作通知发送和TB项目管理。

## 快速开始

### 1. 环境配置

首先配置钉钉应用凭证：

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env
```

在`.env`文件中填入您的钉钉应用信息：

```env
# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key_here
DINGTALK_APP_SECRET=your_app_secret_here
DINGTALK_CORP_ID=your_corp_id_here

# 安全配置
JWT_SECRET_KEY=your_jwt_secret_key_32_chars_min
ENCRYPTION_KEY=your_encryption_key_32_chars_min
```

### 2. 启动服务器

```bash
# 安装依赖
pip3 install -e .

# 启动MCP服务器
python3 -m src.server
```

### 3. <PERSON>集成

将以下配置添加到<PERSON> Desktop的配置文件中：

```json
{
  "mcpServers": {
    "dingtalk-mcp": {
      "command": "python3",
      "args": ["-m", "src.server"],
      "cwd": "/path/to/dingtalk-mcp",
      "env": {
        "DINGTALK_APP_KEY": "your_app_key",
        "DINGTALK_APP_SECRET": "your_app_secret",
        "DINGTALK_CORP_ID": "your_corp_id",
        "JWT_SECRET_KEY": "your_jwt_secret",
        "ENCRYPTION_KEY": "your_encryption_key"
      }
    }
  }
}
```

## 功能使用

### 待办任务管理

#### 创建待办任务

```
请帮我创建一个待办任务：
- 标题：完成项目文档编写
- 描述：需要完成API文档和用户指南
- 截止时间：下周五
- 执行人：张三、李四
- 优先级：高
```

#### 查询待办任务

```
查询我的待办任务列表，包括：
- 未完成的任务
- 我作为执行人的任务
- 按优先级排序
```

#### 更新任务状态

```
将任务ID为12345的待办任务标记为已完成
```

### 工作通知发送

#### 发送团队通知

```
发送工作通知给开发团队：
标题：系统维护通知
内容：
# 系统维护通知

各位同事，

明天晚上22:00-24:00将进行系统维护，期间可能影响以下服务：
- 钉钉API接口
- 文件上传功能

请提前做好准备。

谢谢配合！
```

#### 查询发送状态

```
查询刚才发送的通知状态，任务ID是67890
```

### TB项目管理

#### 创建项目

```
创建一个新的TB项目：
- 项目名称：钉钉MCP集成项目
- 负责人：项目经理
```

#### 添加项目成员

```
为项目ID为abc123的项目添加成员：
- 张三（开发工程师）
- 李四（测试工程师）
- 王五（产品经理）
```

#### 创建项目任务

```
在项目中创建任务：
- 项目ID：abc123
- 任务标题：实现用户认证功能
- 执行人：张三
- 开始时间：今天
- 截止时间：下周三
- 备注：需要集成OAuth2.0认证
```

## 高级功能

### 批量操作

```
批量创建待办任务：
1. 代码审查 - 执行人：张三 - 优先级：高
2. 单元测试 - 执行人：李四 - 优先级：中
3. 文档更新 - 执行人：王五 - 优先级：低
```

### 定时提醒

```
设置定时提醒：
- 每周一上午9点提醒团队周会
- 每月最后一天提醒月度总结
```

### 数据统计

```
生成本月的工作统计报告：
- 完成的待办任务数量
- 发送的工作通知数量
- 项目进度概览
```

## 最佳实践

### 1. 任务管理

- **明确的标题**：使用简洁明了的任务标题
- **详细的描述**：在描述中包含具体的要求和标准
- **合理的截止时间**：设置现实可行的时间目标
- **明确的责任人**：指定具体的执行人和参与人

### 2. 通知发送

- **结构化内容**：使用Markdown格式组织通知内容
- **及时性**：重要通知及时发送，避免延误
- **目标明确**：选择合适的接收人群
- **跟踪反馈**：查询发送状态确保消息到达

### 3. 项目管理

- **清晰的项目结构**：合理规划项目层级
- **定期更新**：及时更新项目进度和状态
- **团队协作**：充分利用成员管理功能
- **任务分解**：将大任务分解为可管理的小任务

## 故障排除

### 常见问题

#### 1. 认证失败

**问题**：API调用返回认证错误

**解决方案**：
- 检查钉钉应用凭证是否正确
- 确认应用权限是否充足
- 验证企业ID是否匹配

#### 2. 工具无响应

**问题**：MCP工具调用无响应

**解决方案**：
- 检查服务器是否正常启动
- 查看日志文件排查错误
- 重启MCP服务器

#### 3. 权限不足

**问题**：某些操作提示权限不足

**解决方案**：
- 确认钉钉应用权限配置
- 检查用户在企业中的角色
- 联系管理员授权

### 日志查看

```bash
# 查看服务器日志
tail -f logs/dingtalk_mcp.log

# 查看错误日志
grep "ERROR" logs/dingtalk_mcp.log
```

### 调试模式

在`.env`文件中启用调试模式：

```env
DEBUG_MODE=true
LOG_LEVEL=DEBUG
```

## 安全注意事项

1. **凭证保护**：妥善保管钉钉应用凭证，不要提交到版本控制
2. **权限最小化**：只授予必要的API权限
3. **定期更新**：定期更新依赖包和安全补丁
4. **访问控制**：限制MCP服务器的网络访问
5. **日志审计**：定期审查操作日志

## 性能优化

1. **缓存策略**：启用访问令牌缓存减少API调用
2. **并发控制**：合理设置并发请求数量
3. **超时设置**：配置合适的API超时时间
4. **资源监控**：监控服务器资源使用情况

## 扩展开发

如需添加新功能，请参考：

1. **API文档**：`docs/API.md`
2. **开发指南**：`docs/DEVELOPMENT.md`
3. **测试指南**：`tests/README.md`
4. **部署指南**：`docs/DEPLOYMENT.md`

## 支持与反馈

如遇到问题或有改进建议，请：

1. 查看项目文档和FAQ
2. 提交Issue到项目仓库
3. 联系技术支持团队

---

**版本**：v0.1.0  
**更新时间**：2025-07-30  
**维护团队**：钉钉MCP开发团队

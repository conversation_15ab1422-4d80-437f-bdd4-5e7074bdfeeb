"""
配置管理测试用例
"""

import pytest
import os
from unittest.mock import patch
from pydantic import ValidationError

from src.config import DingTalkConfig


class TestDingTalkConfig:
    """钉钉配置测试类"""
    
    def test_config_with_valid_env_vars(self):
        """测试有效环境变量的配置"""
        with patch.dict(os.environ, {
            'DINGTALK_APP_KEY': 'test_app_key',
            'DINGTALK_APP_SECRET': 'test_app_secret',
            'DINGTALK_CORP_ID': 'test_corp_id',
            'JWT_SECRET_KEY': 'test_jwt_secret',
            'ENCRYPTION_KEY': 'test_encryption_key'
        }):
            config = DingTalkConfig()
            
            assert config.dingtalk_app_key == 'test_app_key'
            assert config.dingtalk_app_secret == 'test_app_secret'
            assert config.dingtalk_corp_id == 'test_corp_id'
            assert config.jwt_secret_key == 'test_jwt_secret'
            assert config.encryption_key == 'test_encryption_key'
    
    def test_config_missing_required_fields(self):
        """测试缺少必填字段的配置"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValidationError):
                DingTalkConfig()
    
    def test_config_default_values(self):
        """测试默认值"""
        with patch.dict(os.environ, {
            'DINGTALK_APP_KEY': 'test_app_key',
            'DINGTALK_APP_SECRET': 'test_app_secret',
            'DINGTALK_CORP_ID': 'test_corp_id',
            'JWT_SECRET_KEY': 'test_jwt_secret',
            'ENCRYPTION_KEY': 'test_encryption_key'
        }):
            config = DingTalkConfig()
            
            assert config.app_name == "dingtalk-mcp"
            assert config.version == "0.1.0"
            assert config.debug == False
            assert config.log_level == "INFO"
            assert config.dingtalk_api_base_url == "https://oapi.dingtalk.com"
            assert config.dingtalk_api_timeout == 30
            assert config.cache_enabled == True
            assert config.cache_ttl == 300
    
    def test_log_level_validation(self):
        """测试日志级别验证"""
        with patch.dict(os.environ, {
            'DINGTALK_APP_KEY': 'test_app_key',
            'DINGTALK_APP_SECRET': 'test_app_secret',
            'DINGTALK_CORP_ID': 'test_corp_id',
            'JWT_SECRET_KEY': 'test_jwt_secret',
            'ENCRYPTION_KEY': 'test_encryption_key',
            'LOG_LEVEL': 'INVALID'
        }):
            with pytest.raises(ValidationError) as exc_info:
                DingTalkConfig()
            
            assert "日志级别必须是" in str(exc_info.value)
    
    def test_api_timeout_validation(self):
        """测试API超时时间验证"""
        with patch.dict(os.environ, {
            'DINGTALK_APP_KEY': 'test_app_key',
            'DINGTALK_APP_SECRET': 'test_app_secret',
            'DINGTALK_CORP_ID': 'test_corp_id',
            'JWT_SECRET_KEY': 'test_jwt_secret',
            'ENCRYPTION_KEY': 'test_encryption_key',
            'DINGTALK_API_TIMEOUT': '0'
        }):
            with pytest.raises(ValidationError) as exc_info:
                DingTalkConfig()
            
            assert "API超时时间必须在1-300秒之间" in str(exc_info.value)
    
    def test_cache_ttl_validation(self):
        """测试缓存TTL验证"""
        with patch.dict(os.environ, {
            'DINGTALK_APP_KEY': 'test_app_key',
            'DINGTALK_APP_SECRET': 'test_app_secret',
            'DINGTALK_CORP_ID': 'test_corp_id',
            'JWT_SECRET_KEY': 'test_jwt_secret',
            'ENCRYPTION_KEY': 'test_encryption_key',
            'CACHE_TTL': '-1'
        }):
            with pytest.raises(ValidationError) as exc_info:
                DingTalkConfig()
            
            assert "缓存TTL不能为负数" in str(exc_info.value)
    
    def test_get_proxies_with_proxy_settings(self):
        """测试代理配置"""
        with patch.dict(os.environ, {
            'DINGTALK_APP_KEY': 'test_app_key',
            'DINGTALK_APP_SECRET': 'test_app_secret',
            'DINGTALK_CORP_ID': 'test_corp_id',
            'JWT_SECRET_KEY': 'test_jwt_secret',
            'ENCRYPTION_KEY': 'test_encryption_key',
            'HTTP_PROXY': 'http://proxy.example.com:8080',
            'HTTPS_PROXY': 'https://proxy.example.com:8080'
        }):
            config = DingTalkConfig()
            proxies = config.get_proxies()
            
            assert proxies is not None
            assert proxies["http://"] == "http://proxy.example.com:8080"
            assert proxies["https://"] == "https://proxy.example.com:8080"
    
    def test_get_proxies_without_proxy_settings(self):
        """测试无代理配置"""
        with patch.dict(os.environ, {
            'DINGTALK_APP_KEY': 'test_app_key',
            'DINGTALK_APP_SECRET': 'test_app_secret',
            'DINGTALK_CORP_ID': 'test_corp_id',
            'JWT_SECRET_KEY': 'test_jwt_secret',
            'ENCRYPTION_KEY': 'test_encryption_key'
        }):
            config = DingTalkConfig()
            proxies = config.get_proxies()
            
            assert proxies is None

"""
pytest配置文件

提供测试用例的通用配置和fixture。
"""

import pytest
import asyncio
import os
from unittest.mock import patch


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_env_vars():
    """模拟环境变量"""
    env_vars = {
        'DINGTALK_APP_KEY': 'test_app_key',
        'DINGTALK_APP_SECRET': 'test_app_secret',
        'DINGTALK_CORP_ID': 'test_corp_id',
        'JWT_SECRET_KEY': 'test_jwt_secret_key',
        'ENCRYPTION_KEY': 'test_encryption_key',
        'LOG_LEVEL': 'DEBUG',
        'DEBUG_MODE': 'true',
        'CACHE_ENABLED': 'true',
        'CACHE_TTL': '300',
        'DINGTALK_API_TIMEOUT': '30',
        'ENABLE_REQUEST_LOGGING': 'false'
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars


@pytest.fixture
def mock_dingtalk_response():
    """模拟钉钉API响应"""
    return {
        "errcode": 0,
        "errmsg": "ok",
        "access_token": "test_access_token",
        "expires_in": 7200
    }


@pytest.fixture
def mock_todo_task():
    """模拟待办任务数据"""
    return {
        "id": "test_task_123",
        "subject": "测试任务",
        "description": "这是一个测试任务",
        "isDone": False,
        "dueTime": 1703750400000,
        "executorStatus": [
            {
                "id": "executor_123",
                "isDone": False
            }
        ],
        "creatorId": "creator_123",
        "createTime": 1703664000000
    }


@pytest.fixture
def mock_project_data():
    """模拟项目数据"""
    return {
        "id": "test_project_123",
        "name": "测试项目",
        "status": "进行中",
        "creatorId": "creator_123",
        "createTime": 1703664000000
    }


@pytest.fixture
def mock_notification_task():
    """模拟通知任务数据"""
    return {
        "task_id": 12345,
        "send_result": {
            "invalidUserIdList": [],
            "invalidDeptIdList": [],
            "forbiddenUserIdList": [],
            "failedUserIdList": [],
            "readUserIdList": ["user1", "user2"],
            "unreadUserIdList": ["user3"]
        },
        "progress": {
            "progress": 100,
            "status": 2,
            "status_text": "处理完毕"
        }
    }


@pytest.fixture
def mock_user_data():
    """模拟用户数据"""
    return {
        "userId": "test_user_123",
        "unionId": "test_union_123",
        "name": "测试用户",
        "mobile": "13800138000",
        "email": "<EMAIL>",
        "department": [1, 2]
    }


@pytest.fixture
def mock_department_data():
    """模拟部门数据"""
    return {
        "id": 1,
        "name": "技术部",
        "parentId": 0,
        "createDeptGroup": True,
        "autoAddUser": True
    }

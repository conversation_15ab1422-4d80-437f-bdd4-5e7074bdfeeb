"""
待办任务工具测试用例
"""

import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime

from src.tools.todo_tools import (
    create_todo_task,
    get_todo_tasks,
    update_todo_task,
    delete_todo_task
)
from src.utils.exceptions import ValidationError


class TestTodoTools:
    """待办任务工具测试类"""
    
    @pytest.mark.asyncio
    async def test_create_todo_task_success(self):
        """测试成功创建待办任务"""
        with patch('src.tools.todo_tools.todo_api.create_task', new_callable=AsyncMock) as mock_create:
            mock_create.return_value = "task_123"
            
            result = await create_todo_task(
                creator_id="user_123",
                subject="测试任务",
                description="这是一个测试任务",
                priority=50
            )
            
            assert "✅ 待办任务创建成功" in result
            assert "task_123" in result
            assert "测试任务" in result
            mock_create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_todo_task_empty_creator_id(self):
        """测试创建任务时创建人ID为空"""
        result = await create_todo_task(
            creator_id="",
            subject="测试任务"
        )
        
        assert "❌ 创建待办任务失败" in result
        assert "创建人UnionId不能为空" in result
    
    @pytest.mark.asyncio
    async def test_create_todo_task_empty_subject(self):
        """测试创建任务时标题为空"""
        result = await create_todo_task(
            creator_id="user_123",
            subject=""
        )
        
        assert "❌ 创建待办任务失败" in result
        assert "任务标题不能为空" in result
    
    @pytest.mark.asyncio
    async def test_create_todo_task_subject_too_long(self):
        """测试创建任务时标题过长"""
        long_subject = "x" * 1025  # 超过1024字符限制
        
        result = await create_todo_task(
            creator_id="user_123",
            subject=long_subject
        )
        
        assert "❌ 创建待办任务失败" in result
        assert "任务标题不能超过1024个字符" in result
    
    @pytest.mark.asyncio
    async def test_create_todo_task_description_too_long(self):
        """测试创建任务时描述过长"""
        long_description = "x" * 4097  # 超过4096字符限制
        
        result = await create_todo_task(
            creator_id="user_123",
            subject="测试任务",
            description=long_description
        )
        
        assert "❌ 创建待办任务失败" in result
        assert "任务描述不能超过4096个字符" in result
    
    @pytest.mark.asyncio
    async def test_create_todo_task_invalid_priority(self):
        """测试创建任务时优先级无效"""
        result = await create_todo_task(
            creator_id="user_123",
            subject="测试任务",
            priority=101  # 超过100的限制
        )
        
        assert "❌ 创建待办任务失败" in result
        assert "优先级必须在0-100之间" in result
    
    @pytest.mark.asyncio
    async def test_get_todo_tasks_success(self):
        """测试成功查询待办任务"""
        mock_tasks = {
            "tasks": [
                {
                    "id": "task_123",
                    "subject": "测试任务1",
                    "isDone": False,
                    "dueTime": 1703750400000,
                    "executorStatus": [{"isDone": False}]
                },
                {
                    "id": "task_456",
                    "subject": "测试任务2",
                    "isDone": True,
                    "executorStatus": []
                }
            ]
        }
        
        with patch('src.tools.todo_tools.todo_api.get_tasks', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_tasks
            
            result = await get_todo_tasks(
                union_id="user_123",
                is_done=False,
                max_results=10
            )
            
            assert "📝 待办任务列表" in result
            assert "测试任务1" in result
            assert "测试任务2" in result
            assert "task_123" in result
            mock_get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_todo_tasks_empty_union_id(self):
        """测试查询任务时用户ID为空"""
        result = await get_todo_tasks(union_id="")
        
        assert "❌ 查询待办任务失败" in result
        assert "用户UnionId不能为空" in result
    
    @pytest.mark.asyncio
    async def test_get_todo_tasks_invalid_max_results(self):
        """测试查询任务时最大结果数无效"""
        result = await get_todo_tasks(
            union_id="user_123",
            max_results=51  # 超过50的限制
        )
        
        assert "❌ 查询待办任务失败" in result
        assert "最大返回数量必须在1-50之间" in result
    
    @pytest.mark.asyncio
    async def test_get_todo_tasks_invalid_role_types(self):
        """测试查询任务时角色类型无效"""
        result = await get_todo_tasks(
            union_id="user_123",
            role_types=["invalid_role"]
        )
        
        assert "❌ 查询待办任务失败" in result
        assert "无效的角色类型" in result
    
    @pytest.mark.asyncio
    async def test_get_todo_tasks_no_tasks(self):
        """测试查询任务时无任务"""
        with patch('src.tools.todo_tools.todo_api.get_tasks', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = {"tasks": []}
            
            result = await get_todo_tasks(union_id="user_123")
            
            assert "📝 暂无全部的待办任务" in result
    
    @pytest.mark.asyncio
    async def test_update_todo_task_success(self):
        """测试成功更新待办任务"""
        with patch('src.tools.todo_tools.todo_api.update_task', new_callable=AsyncMock) as mock_update:
            mock_update.return_value = True
            
            result = await update_todo_task(
                union_id="user_123",
                task_id="task_123",
                subject="更新后的任务",
                is_done=True
            )
            
            assert "✅ 待办任务更新成功" in result
            assert "task_123" in result
            assert "更新后的任务" in result
            mock_update.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_todo_task_empty_union_id(self):
        """测试更新任务时用户ID为空"""
        result = await update_todo_task(
            union_id="",
            task_id="task_123",
            subject="更新后的任务"
        )
        
        assert "❌ 更新待办任务失败" in result
        assert "用户UnionId不能为空" in result
    
    @pytest.mark.asyncio
    async def test_update_todo_task_empty_task_id(self):
        """测试更新任务时任务ID为空"""
        result = await update_todo_task(
            union_id="user_123",
            task_id="",
            subject="更新后的任务"
        )
        
        assert "❌ 更新待办任务失败" in result
        assert "任务ID不能为空" in result
    
    @pytest.mark.asyncio
    async def test_delete_todo_task_success(self):
        """测试成功删除待办任务"""
        with patch('src.tools.todo_tools.todo_api.delete_task', new_callable=AsyncMock) as mock_delete:
            mock_delete.return_value = True
            
            result = await delete_todo_task(
                union_id="user_123",
                task_id="task_123"
            )
            
            assert "✅ 待办任务删除成功" in result
            assert "task_123" in result
            mock_delete.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_todo_task_empty_union_id(self):
        """测试删除任务时用户ID为空"""
        result = await delete_todo_task(
            union_id="",
            task_id="task_123"
        )
        
        assert "❌ 删除待办任务失败" in result
        assert "用户UnionId不能为空" in result
    
    @pytest.mark.asyncio
    async def test_delete_todo_task_empty_task_id(self):
        """测试删除任务时任务ID为空"""
        result = await delete_todo_task(
            union_id="user_123",
            task_id=""
        )
        
        assert "❌ 删除待办任务失败" in result
        assert "任务ID不能为空" in result

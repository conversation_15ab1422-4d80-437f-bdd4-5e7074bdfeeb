# 钉钉MCP服务器API文档

本文档详细介绍钉钉MCP服务器提供的所有工具和功能。

## 工具概览

钉钉MCP服务器提供以下三大类工具：

1. **待办任务管理** - 创建、查询、更新、删除待办任务
2. **工作通知** - 发送工作通知、查询发送状态
3. **TB项目管理** - 项目和任务管理

## 待办任务管理工具

### dingtalk_create_todo

创建钉钉待办任务。

**参数：**
- `creator_id` (string, 必填): 创建人UnionId
- `subject` (string, 必填): 任务标题，最大1024字符
- `description` (string, 可选): 任务描述，最大4096字符
- `due_time` (integer, 可选): 截止时间，Unix时间戳（毫秒）
- `executor_ids` (array, 可选): 执行人UnionId列表
- `participant_ids` (array, 可选): 参与人UnionId列表
- `priority` (integer, 可选): 优先级，0-100，默认0

**返回：**
创建结果的格式化字符串，包含任务ID和详细信息。

**示例：**
```json
{
  "creator_id": "user123",
  "subject": "完成项目文档",
  "description": "编写技术文档和用户手册",
  "due_time": 1703750400000,
  "executor_ids": ["executor123"],
  "priority": 80
}
```

### dingtalk_get_todos

查询钉钉待办任务列表。

**参数：**
- `union_id` (string, 必填): 用户UnionId
- `is_done` (boolean, 可选): 完成状态过滤，null=全部，true=已完成，false=未完成
- `role_types` (array, 可选): 角色类型过滤，支持"executor"、"creator"、"participant"
- `max_results` (integer, 可选): 最大返回数量，1-50，默认10

**返回：**
任务列表的格式化字符串，包含任务详情和状态。

**示例：**
```json
{
  "union_id": "user123",
  "is_done": false,
  "role_types": ["creator", "executor"],
  "max_results": 20
}
```

### dingtalk_update_todo

更新钉钉待办任务。

**参数：**
- `union_id` (string, 必填): 用户UnionId
- `task_id` (string, 必填): 任务ID
- `subject` (string, 可选): 新标题
- `description` (string, 可选): 新描述
- `due_time` (integer, 可选): 新截止时间，Unix时间戳（毫秒）
- `is_done` (boolean, 可选): 新完成状态

**返回：**
更新结果的格式化字符串。

### dingtalk_delete_todo

删除钉钉待办任务。

**参数：**
- `union_id` (string, 必填): 用户UnionId
- `task_id` (string, 必填): 任务ID

**返回：**
删除结果的格式化字符串。

## 工作通知工具

### dingtalk_send_notification

发送钉钉工作通知。

**参数：**
- `agent_id` (string, 必填): 钉钉应用的AgentId
- `title` (string, 必填): 消息标题，最大200字符
- `content` (string, 必填): 消息内容，支持Markdown格式，最大5000字符
- `to_all_user` (boolean, 可选): 是否发送给全员，默认false
- `userid_list` (string, 可选): 接收人用户ID列表，逗号分隔，最多5000人
- `dept_id_list` (string, 可选): 接收部门ID列表，逗号分隔

**返回：**
发送结果的格式化字符串，包含任务ID。

**示例：**
```json
{
  "agent_id": "123456789",
  "title": "项目进度更新",
  "content": "## 本周进度\n\n- ✅ 完成需求分析\n- 🔄 正在进行开发",
  "userid_list": "user1,user2,user3"
}
```

### dingtalk_get_notification_result

查询钉钉工作通知发送结果。

**参数：**
- `agent_id` (string, 必填): 钉钉应用的AgentId
- `task_id` (integer, 必填): 发送任务ID

**返回：**
发送结果的格式化字符串，包含发送统计和失败详情。

### dingtalk_get_notification_progress

查询钉钉工作通知发送进度。

**参数：**
- `agent_id` (string, 必填): 钉钉应用的AgentId
- `task_id` (integer, 必填): 发送任务ID

**返回：**
发送进度的格式化字符串，包含进度百分比和状态。

### dingtalk_recall_notification

撤回钉钉工作通知。

**参数：**
- `agent_id` (string, 必填): 钉钉应用的AgentId
- `msg_task_id` (integer, 必填): 要撤回的消息任务ID

**返回：**
撤回结果的格式化字符串。

## TB项目管理工具

### dingtalk_create_project

创建钉钉TB项目。

**参数：**
- `user_id` (string, 必填): 操作者userId
- `name` (string, 必填): 项目名称，最大100字符

**返回：**
创建结果的格式化字符串，包含项目ID。

**示例：**
```json
{
  "user_id": "creator123",
  "name": "新产品开发项目"
}
```

### dingtalk_search_projects

搜索钉钉TB项目。

**参数：**
- `user_id` (string, 必填): 操作者userId
- `name` (string, 必填): 项目名称关键词，模糊搜索
- `max_results` (integer, 可选): 最大返回数量，1-50，默认10

**返回：**
搜索结果的格式化字符串，包含项目列表。

### dingtalk_get_project_members

查询钉钉TB项目成员。

**参数：**
- `user_id` (string, 必填): 操作者userId
- `project_id` (string, 必填): 项目ID
- `max_results` (integer, 可选): 最大返回数量，1-100，默认20

**返回：**
项目成员列表的格式化字符串。

### dingtalk_add_project_members

添加钉钉TB项目成员。

**参数：**
- `user_id` (string, 必填): 操作者userId
- `project_id` (string, 必填): 项目ID
- `member_user_ids` (array, 必填): 要添加的成员userId列表，最多10个

**返回：**
添加结果的格式化字符串。

### dingtalk_create_project_task

创建钉钉TB项目任务。

**参数：**
- `user_id` (string, 必填): 操作者userId
- `project_id` (string, 必填): 项目ID
- `content` (string, 必填): 任务标题，最大200字符
- `executor_id` (string, 可选): 执行人userId
- `note` (string, 可选): 任务备注，最大1000字符
- `start_date` (string, 可选): 开始时间，ISO格式：2023-12-01T10:00:00Z
- `due_date` (string, 可选): 截止时间，ISO格式：2023-12-31T18:00:00Z

**返回：**
创建结果的格式化字符串，包含任务ID。

**示例：**
```json
{
  "user_id": "creator123",
  "project_id": "project456",
  "content": "设计产品原型",
  "executor_id": "designer123",
  "due_date": "2023-12-31T18:00:00Z"
}
```

## 错误处理

所有工具都包含完善的错误处理机制：

### 参数验证错误
- 必填参数缺失
- 参数格式不正确
- 参数长度超限

### API调用错误
- 认证失败
- 权限不足
- 网络错误
- 限流错误

### 业务逻辑错误
- 资源不存在
- 状态不允许操作
- 数据冲突

## 最佳实践

### 1. 错误处理
```python
# 检查返回结果
result = await dingtalk_create_todo(...)
if "❌" in result:
    # 处理错误
    print(f"创建失败: {result}")
else:
    # 处理成功
    print(f"创建成功: {result}")
```

### 2. 批量操作
```python
# 分批处理大量数据
user_ids = ["user1", "user2", ..., "user100"]
batch_size = 10

for i in range(0, len(user_ids), batch_size):
    batch = user_ids[i:i+batch_size]
    await dingtalk_add_project_members(
        user_id="admin",
        project_id="project123",
        member_user_ids=batch
    )
```

### 3. 时间处理
```python
from datetime import datetime, timedelta

# 设置截止时间为明天18:00
due_time = datetime.now() + timedelta(days=1)
due_time = due_time.replace(hour=18, minute=0, second=0, microsecond=0)
due_timestamp = int(due_time.timestamp() * 1000)

await dingtalk_create_todo(
    creator_id="user123",
    subject="明天的任务",
    due_time=due_timestamp
)
```

### 4. Markdown格式
```python
# 使用Markdown格式发送通知
content = """
## 📊 项目进度报告

### ✅ 已完成
- 需求分析
- 技术选型
- 原型设计

### 🔄 进行中
- 后端开发
- 前端开发

### ⏳ 待开始
- 测试
- 部署

**下周计划：** 完成开发并开始测试
"""

await dingtalk_send_notification(
    agent_id="123456789",
    title="项目进度报告",
    content=content,
    userid_list="user1,user2,user3"
)
```

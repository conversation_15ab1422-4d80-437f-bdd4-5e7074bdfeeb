# 钉钉MCP服务器部署指南

本文档详细介绍如何部署和配置钉钉MCP服务器。

## 部署方式

### 1. 本地开发部署

#### 环境要求
- Python 3.11+
- pip 或 poetry
- 钉钉企业应用权限

#### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd dingtalk-mcp
```

2. **安装依赖**
```bash
# 使用pip
pip install -e .

# 或使用poetry
poetry install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，填入钉钉应用信息
```

4. **测试运行**
```bash
python -m src.server
```

### 2. Docker部署

#### 使用Docker Compose（推荐）

1. **准备配置文件**
```bash
cp .env.example .env
# 编辑.env文件
```

2. **启动服务**
```bash
docker-compose -f configs/docker-compose.yml up -d
```

3. **查看日志**
```bash
docker-compose -f configs/docker-compose.yml logs -f dingtalk-mcp
```

4. **停止服务**
```bash
docker-compose -f configs/docker-compose.yml down
```

#### 使用Docker

1. **构建镜像**
```bash
docker build -f configs/Dockerfile -t dingtalk-mcp:latest .
```

2. **运行容器**
```bash
docker run -d \
  --name dingtalk-mcp \
  --env-file .env \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  dingtalk-mcp:latest
```

### 3. 生产环境部署

#### 使用systemd服务

1. **创建服务文件**
```bash
sudo tee /etc/systemd/system/dingtalk-mcp.service > /dev/null <<EOF
[Unit]
Description=DingTalk MCP Server
After=network.target

[Service]
Type=simple
User=dingtalk
Group=dingtalk
WorkingDirectory=/opt/dingtalk-mcp
Environment=PATH=/opt/dingtalk-mcp/venv/bin
ExecStart=/opt/dingtalk-mcp/venv/bin/python -m src.server
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
```

2. **启动服务**
```bash
sudo systemctl daemon-reload
sudo systemctl enable dingtalk-mcp
sudo systemctl start dingtalk-mcp
```

3. **查看状态**
```bash
sudo systemctl status dingtalk-mcp
sudo journalctl -u dingtalk-mcp -f
```

## Claude Desktop配置

### macOS配置

编辑文件：`~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "dingtalk-mcp": {
      "command": "python",
      "args": ["-m", "src.server"],
      "cwd": "/path/to/dingtalk-mcp",
      "env": {
        "DINGTALK_APP_KEY": "your_app_key",
        "DINGTALK_APP_SECRET": "your_app_secret",
        "DINGTALK_CORP_ID": "your_corp_id",
        "JWT_SECRET_KEY": "your_jwt_secret",
        "ENCRYPTION_KEY": "your_encryption_key"
      }
    }
  }
}
```

### Windows配置

编辑文件：`%APPDATA%\Claude\claude_desktop_config.json`

配置内容同macOS，但路径使用Windows格式：

```json
{
  "mcpServers": {
    "dingtalk-mcp": {
      "command": "python",
      "args": ["-m", "src.server"],
      "cwd": "C:\\path\\to\\dingtalk-mcp",
      "env": {
        "DINGTALK_APP_KEY": "your_app_key",
        "DINGTALK_APP_SECRET": "your_app_secret",
        "DINGTALK_CORP_ID": "your_corp_id",
        "JWT_SECRET_KEY": "your_jwt_secret",
        "ENCRYPTION_KEY": "your_encryption_key"
      }
    }
  }
}
```

### Linux配置

编辑文件：`~/.config/Claude/claude_desktop_config.json`

配置内容同macOS。

## 环境变量配置

### 必需配置

```bash
# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key_here
DINGTALK_APP_SECRET=your_app_secret_here
DINGTALK_CORP_ID=your_corp_id_here

# 安全配置
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here
```

### 可选配置

```bash
# 服务器配置
MCP_SERVER_NAME=dingtalk-mcp
MCP_SERVER_VERSION=0.1.0
LOG_LEVEL=INFO
DEBUG_MODE=false

# API配置
DINGTALK_API_BASE_URL=https://oapi.dingtalk.com
DINGTALK_API_TIMEOUT=30
ENABLE_REQUEST_LOGGING=false

# 缓存配置
CACHE_ENABLED=true
CACHE_TTL=300

# 数据库配置
DATABASE_URL=sqlite:///./data/dingtalk_mcp.db

# 代理配置（如需要）
HTTP_PROXY=http://proxy.example.com:8080
HTTPS_PROXY=https://proxy.example.com:8080
```

## 钉钉应用配置

### 1. 创建企业内部应用

1. 登录[钉钉开放平台](https://open.dingtalk.com/)
2. 进入"应用开发" -> "企业内部开发"
3. 点击"创建应用"
4. 填写应用信息并提交

### 2. 获取应用凭证

在应用详情页面获取：
- AppKey
- AppSecret
- CorpId（企业ID）

### 3. 配置应用权限

确保应用具有以下权限：

#### 待办任务权限
- 待办任务管理
- 待办任务查询

#### 工作通知权限
- 工作通知发送
- 消息管理

#### 项目管理权限
- TB项目管理
- 项目任务管理

#### 基础权限
- 用户信息读取
- 部门信息读取

### 4. 配置IP白名单

在应用配置中添加服务器IP到白名单。

## 监控和日志

### 日志配置

日志文件位置：
- 开发环境：控制台输出
- 生产环境：`logs/dingtalk_mcp.log`

日志级别：
- DEBUG：调试信息
- INFO：一般信息
- WARNING：警告信息
- ERROR：错误信息
- CRITICAL：严重错误

### 监控指标

建议监控以下指标：
- 服务可用性
- API响应时间
- 错误率
- 内存使用率
- CPU使用率

### 健康检查

Docker容器包含健康检查：
```bash
# 检查容器健康状态
docker ps
docker inspect dingtalk-mcp | grep Health
```

## 故障排除

### 常见问题

1. **认证失败**
   - 检查AppKey、AppSecret、CorpId是否正确
   - 确认应用权限配置
   - 检查IP白名单

2. **API调用失败**
   - 检查网络连接
   - 确认API权限
   - 查看详细错误日志

3. **Claude Desktop连接失败**
   - 检查配置文件路径
   - 确认环境变量设置
   - 重启Claude Desktop

### 调试模式

启用调试模式：
```bash
DEBUG_MODE=true LOG_LEVEL=DEBUG python -m src.server
```

### 日志分析

查看错误日志：
```bash
# Docker环境
docker logs dingtalk-mcp

# systemd环境
sudo journalctl -u dingtalk-mcp -f

# 本地环境
tail -f logs/dingtalk_mcp.log
```

## 安全建议

1. **环境变量安全**
   - 使用强密码生成JWT密钥
   - 定期轮换密钥
   - 不要在代码中硬编码密钥

2. **网络安全**
   - 使用HTTPS
   - 配置防火墙
   - 限制访问IP

3. **应用安全**
   - 定期更新依赖
   - 监控安全漏洞
   - 使用最小权限原则

## 性能优化

1. **缓存配置**
   - 启用API响应缓存
   - 调整缓存TTL
   - 监控缓存命中率

2. **并发配置**
   - 调整HTTP客户端超时
   - 配置连接池大小
   - 使用异步处理

3. **资源限制**
   - 设置内存限制
   - 配置CPU限制
   - 监控资源使用

## 备份和恢复

### 数据备份

```bash
# 备份SQLite数据库
cp data/dingtalk_mcp.db backup/dingtalk_mcp_$(date +%Y%m%d_%H%M%S).db

# 备份配置文件
cp .env backup/env_$(date +%Y%m%d_%H%M%S).backup
```

### 数据恢复

```bash
# 恢复数据库
cp backup/dingtalk_mcp_20231201_120000.db data/dingtalk_mcp.db

# 恢复配置
cp backup/env_20231201_120000.backup .env
```

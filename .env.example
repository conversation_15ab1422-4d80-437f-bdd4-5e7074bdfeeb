# 钉钉MCP服务器环境配置示例
# 复制此文件为 .env 并填入真实的配置值

# 基础配置
NODE_ENV=development
LOG_LEVEL=INFO
MCP_SERVER_NAME=dingtalk-mcp
MCP_SERVER_VERSION=0.1.0

# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key_here
DINGTALK_APP_SECRET=your_app_secret_here
DINGTALK_CORP_ID=your_corp_id_here

# 钉钉API配置
DINGTALK_API_BASE_URL=https://oapi.dingtalk.com
DINGTALK_API_TIMEOUT=30

# 数据库配置（可选，用于缓存）
DATABASE_URL=sqlite:///./data/dingtalk_mcp.db

# 缓存配置
CACHE_ENABLED=true
CACHE_TTL=300

# 安全配置
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# 代理配置（如需要）
HTTP_PROXY=
HTTPS_PROXY=

# 调试配置
DEBUG_MODE=false
ENABLE_REQUEST_LOGGING=false

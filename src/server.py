"""
钉钉MCP服务器主文件

基于FastMCP框架的钉钉集成服务器，提供待办管理、工作通知、TB项目管理等功能。
"""

import asyncio
from typing import List, Optional

# 尝试导入FastMCP，如果失败则使用模拟实现
try:
    from fastmcp import FastMCP
except ImportError:
    from src.fastmcp_mock import MockMCP as FastMCP

from src.config import config
from src.utils.logger import setup_logger
from src.utils.auth import auth_manager
from src.utils.dingtalk_client import dingtalk_client
from src.tools.todo_tools import (
    create_todo_task,
    get_todo_tasks,
    update_todo_task,
    delete_todo_task
)
from src.tools.notification_tools import (
    send_work_notification,
    get_notification_send_result,
    get_notification_send_progress,
    recall_work_notification
)
from src.tools.project_tools import (
    create_project,
    search_projects,
    get_project_members,
    add_project_members,
    remove_project_members,
    create_project_task
)

# 设置日志
logger = setup_logger("dingtalk_mcp_server")

# 创建MCP服务器实例
mcp = FastMCP("钉钉MCP服务器")


# ==================== 待办任务管理工具 ====================

@mcp.tool()
async def dingtalk_create_todo(
    creator_id: str,
    subject: str,
    description: Optional[str] = None,
    due_time: Optional[int] = None,
    executor_ids: Optional[List[str]] = None,
    participant_ids: Optional[List[str]] = None,
    priority: int = 0
) -> str:
    """
    创建钉钉待办任务
    
    Args:
        creator_id: 创建人UnionId（必填）
        subject: 任务标题（必填，最大1024字符）
        description: 任务描述（可选，最大4096字符）
        due_time: 截止时间（Unix时间戳，毫秒）
        executor_ids: 执行人UnionId列表
        participant_ids: 参与人UnionId列表
        priority: 优先级（0-100，默认0）
    
    Returns:
        创建结果的格式化字符串
    """
    return await create_todo_task(
        creator_id=creator_id,
        subject=subject,
        description=description,
        due_time=due_time,
        executor_ids=executor_ids,
        participant_ids=participant_ids,
        priority=priority
    )


@mcp.tool()
async def dingtalk_get_todos(
    union_id: str,
    is_done: Optional[bool] = None,
    role_types: Optional[List[str]] = None,
    max_results: int = 10
) -> str:
    """
    查询钉钉待办任务列表
    
    Args:
        union_id: 用户UnionId（必填）
        is_done: 完成状态过滤（None=全部，True=已完成，False=未完成）
        role_types: 角色类型过滤（executor=执行人，creator=创建人，participant=参与人）
        max_results: 最大返回数量（1-50，默认10）
    
    Returns:
        任务列表的格式化字符串
    """
    return await get_todo_tasks(
        union_id=union_id,
        is_done=is_done,
        role_types=role_types,
        max_results=max_results
    )


@mcp.tool()
async def dingtalk_update_todo(
    union_id: str,
    task_id: str,
    subject: Optional[str] = None,
    description: Optional[str] = None,
    due_time: Optional[int] = None,
    is_done: Optional[bool] = None
) -> str:
    """
    更新钉钉待办任务
    
    Args:
        union_id: 用户UnionId（必填）
        task_id: 任务ID（必填）
        subject: 新标题（可选）
        description: 新描述（可选）
        due_time: 新截止时间（Unix时间戳，毫秒）
        is_done: 新完成状态（可选）
    
    Returns:
        更新结果的格式化字符串
    """
    return await update_todo_task(
        union_id=union_id,
        task_id=task_id,
        subject=subject,
        description=description,
        due_time=due_time,
        is_done=is_done
    )


@mcp.tool()
async def dingtalk_delete_todo(union_id: str, task_id: str) -> str:
    """
    删除钉钉待办任务
    
    Args:
        union_id: 用户UnionId（必填）
        task_id: 任务ID（必填）
    
    Returns:
        删除结果的格式化字符串
    """
    return await delete_todo_task(union_id=union_id, task_id=task_id)


# ==================== 工作通知工具 ====================

@mcp.tool()
async def dingtalk_send_notification(
    agent_id: str,
    title: str,
    content: str,
    to_all_user: bool = False,
    userid_list: Optional[str] = None,
    dept_id_list: Optional[str] = None
) -> str:
    """
    发送钉钉工作通知
    
    Args:
        agent_id: 钉钉应用的AgentId（必填）
        title: 消息标题（必填，最大200字符）
        content: 消息内容（必填，支持Markdown格式，最大5000字符）
        to_all_user: 是否发送给全员（默认False）
        userid_list: 接收人用户ID列表，逗号分隔，最多5000人
        dept_id_list: 接收部门ID列表，逗号分隔
    
    Returns:
        发送结果的格式化字符串
    """
    return await send_work_notification(
        agent_id=agent_id,
        title=title,
        content=content,
        to_all_user=to_all_user,
        userid_list=userid_list,
        dept_id_list=dept_id_list
    )


@mcp.tool()
async def dingtalk_get_notification_result(agent_id: str, task_id: int) -> str:
    """
    查询钉钉工作通知发送结果
    
    Args:
        agent_id: 钉钉应用的AgentId（必填）
        task_id: 发送任务ID（必填）
    
    Returns:
        发送结果的格式化字符串
    """
    return await get_notification_send_result(agent_id=agent_id, task_id=task_id)


@mcp.tool()
async def dingtalk_get_notification_progress(agent_id: str, task_id: int) -> str:
    """
    查询钉钉工作通知发送进度
    
    Args:
        agent_id: 钉钉应用的AgentId（必填）
        task_id: 发送任务ID（必填）
    
    Returns:
        发送进度的格式化字符串
    """
    return await get_notification_send_progress(agent_id=agent_id, task_id=task_id)


@mcp.tool()
async def dingtalk_recall_notification(agent_id: str, msg_task_id: int) -> str:
    """
    撤回钉钉工作通知
    
    Args:
        agent_id: 钉钉应用的AgentId（必填）
        msg_task_id: 要撤回的消息任务ID（必填）
    
    Returns:
        撤回结果的格式化字符串
    """
    return await recall_work_notification(agent_id=agent_id, msg_task_id=msg_task_id)


# ==================== TB项目管理工具 ====================

@mcp.tool()
async def dingtalk_create_project(user_id: str, name: str) -> str:
    """
    创建钉钉TB项目
    
    Args:
        user_id: 操作者userId（必填）
        name: 项目名称（必填，最大100字符）
    
    Returns:
        创建结果的格式化字符串
    """
    return await create_project(user_id=user_id, name=name)


@mcp.tool()
async def dingtalk_search_projects(
    user_id: str, 
    name: str, 
    max_results: int = 10
) -> str:
    """
    搜索钉钉TB项目
    
    Args:
        user_id: 操作者userId（必填）
        name: 项目名称关键词（必填，模糊搜索）
        max_results: 最大返回数量（1-50，默认10）
    
    Returns:
        搜索结果的格式化字符串
    """
    return await search_projects(
        user_id=user_id,
        name=name,
        max_results=max_results
    )


@mcp.tool()
async def dingtalk_get_project_members(
    user_id: str, 
    project_id: str, 
    max_results: int = 20
) -> str:
    """
    查询钉钉TB项目成员
    
    Args:
        user_id: 操作者userId（必填）
        project_id: 项目ID（必填）
        max_results: 最大返回数量（1-100，默认20）
    
    Returns:
        项目成员列表的格式化字符串
    """
    return await get_project_members(
        user_id=user_id,
        project_id=project_id,
        max_results=max_results
    )


@mcp.tool()
async def dingtalk_add_project_members(
    user_id: str, 
    project_id: str, 
    member_user_ids: List[str]
) -> str:
    """
    添加钉钉TB项目成员
    
    Args:
        user_id: 操作者userId（必填）
        project_id: 项目ID（必填）
        member_user_ids: 要添加的成员userId列表（必填，最多10个）
    
    Returns:
        添加结果的格式化字符串
    """
    return await add_project_members(
        user_id=user_id,
        project_id=project_id,
        member_user_ids=member_user_ids
    )


@mcp.tool()
async def dingtalk_create_project_task(
    user_id: str,
    project_id: str,
    content: str,
    executor_id: Optional[str] = None,
    note: Optional[str] = None,
    start_date: Optional[str] = None,
    due_date: Optional[str] = None
) -> str:
    """
    创建钉钉TB项目任务
    
    Args:
        user_id: 操作者userId（必填）
        project_id: 项目ID（必填）
        content: 任务标题（必填，最大200字符）
        executor_id: 执行人userId（可选）
        note: 任务备注（可选，最大1000字符）
        start_date: 开始时间（可选，ISO格式：2023-12-01T10:00:00Z）
        due_date: 截止时间（可选，ISO格式：2023-12-31T18:00:00Z）
    
    Returns:
        创建结果的格式化字符串
    """
    return await create_project_task(
        user_id=user_id,
        project_id=project_id,
        content=content,
        executor_id=executor_id,
        note=note,
        start_date=start_date,
        due_date=due_date
    )


# ==================== 服务器生命周期管理 ====================

async def cleanup():
    """清理资源"""
    try:
        await auth_manager.close()
        await dingtalk_client.close()
        logger.info("钉钉MCP服务器资源清理完成")
    except Exception as e:
        logger.error(f"清理资源时发生错误: {e}")


def main():
    """主函数"""
    try:
        logger.info("启动钉钉MCP服务器...")
        logger.info(f"服务器版本: {config.version}")
        logger.info(f"调试模式: {config.debug}")
        
        # 运行MCP服务器
        mcp.run()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
        raise
    finally:
        # 清理资源
        if hasattr(asyncio, 'run'):
            asyncio.run(cleanup())
        else:
            loop = asyncio.get_event_loop()
            try:
                loop.run_until_complete(cleanup())
            finally:
                loop.close()


if __name__ == "__main__":
    main()

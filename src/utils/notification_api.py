"""
钉钉工作通知API模块

提供钉钉工作通知的发送、查询等功能。
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from src.utils.dingtalk_client import dingtalk_client
from src.utils.logger import get_logger
from src.utils.exceptions import ValidationError

logger = get_logger(__name__)


class NotificationMessage(BaseModel):
    """工作通知消息模型"""
    
    title: str = Field(..., description="消息标题")
    content: str = Field(..., description="消息内容（支持Markdown）")
    msg_type: str = Field(default="markdown", description="消息类型")
    to_all_user: bool = Field(default=False, description="是否发送给全员")
    userid_list: Optional[str] = Field(None, description="接收人用户ID列表，逗号分隔")
    dept_id_list: Optional[str] = Field(None, description="接收部门ID列表，逗号分隔")


class NotificationAPI:
    """钉钉工作通知API"""
    
    def __init__(self, agent_id: str):
        """
        初始化通知API
        
        Args:
            agent_id: 钉钉应用的AgentId
        """
        self.agent_id = agent_id
    
    async def send_markdown_message(
        self,
        title: str,
        content: str,
        to_all_user: bool = False,
        userid_list: Optional[str] = None,
        dept_id_list: Optional[str] = None
    ) -> int:
        """
        发送Markdown格式的工作通知
        
        Args:
            title: 消息标题
            content: 消息内容（Markdown格式）
            to_all_user: 是否发送给全员
            userid_list: 接收人用户ID列表，逗号分隔，最多5000人
            dept_id_list: 接收部门ID列表，逗号分隔
        
        Returns:
            任务ID
        """
        logger.info(f"发送工作通知: {title}")
        
        # 构建消息数据
        msg_data = {
            "msgtype": "markdown",
            "markdown": {
                "title": title,
                "text": content
            }
        }
        
        # 构建请求数据
        data = {
            "agent_id": self.agent_id,
            "msg": msg_data,
            "to_all_user": to_all_user
        }
        
        # 添加接收人
        if userid_list:
            data["userid_list"] = userid_list
        
        if dept_id_list:
            data["dept_id_list"] = dept_id_list
        
        # 验证接收人设置
        if not to_all_user and not userid_list and not dept_id_list:
            raise ValidationError("必须指定接收人：to_all_user、userid_list或dept_id_list")
        
        try:
            response = await dingtalk_client.post("/topapi/message/corpconversation/asyncsend_v2", data=data)
            task_id = response.get("task_id")
            
            if not task_id:
                raise ValidationError("发送通知失败：未返回任务ID")
            
            logger.info(f"工作通知发送成功，任务ID: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"发送工作通知失败: {e}")
            raise
    
    async def get_send_result(self, task_id: int) -> Dict[str, Any]:
        """
        查询工作通知发送结果
        
        Args:
            task_id: 发送任务ID
        
        Returns:
            发送结果统计
        """
        logger.info(f"查询通知发送结果: {task_id}")
        
        data = {
            "agent_id": self.agent_id,
            "task_id": task_id
        }
        
        try:
            response = await dingtalk_client.post("/topapi/message/corpconversation/getsendresult", data=data)
            
            send_result = response.get("send_result", {})
            logger.info(f"通知发送结果: {send_result}")
            
            return send_result
            
        except Exception as e:
            logger.error(f"查询通知发送结果失败: {e}")
            raise
    
    async def get_send_progress(self, task_id: int) -> Dict[str, Any]:
        """
        查询工作通知发送进度
        
        Args:
            task_id: 发送任务ID
        
        Returns:
            发送进度信息
        """
        logger.info(f"查询通知发送进度: {task_id}")
        
        data = {
            "agent_id": self.agent_id,
            "task_id": task_id
        }
        
        try:
            response = await dingtalk_client.post("/topapi/message/corpconversation/getsendprogress", data=data)
            
            progress_in_percent = response.get("progress_in_percent", 0)
            status = response.get("status", 0)  # 0:未开始 1:处理中 2:处理完毕
            
            result = {
                "progress": progress_in_percent,
                "status": status,
                "status_text": self._get_status_text(status)
            }
            
            logger.info(f"通知发送进度: {result}")
            return result
            
        except Exception as e:
            logger.error(f"查询通知发送进度失败: {e}")
            raise
    
    async def recall_message(self, msg_task_id: int) -> bool:
        """
        撤回工作通知消息
        
        Args:
            msg_task_id: 要撤回的消息任务ID
        
        Returns:
            是否撤回成功
        """
        logger.info(f"撤回工作通知: {msg_task_id}")
        
        data = {
            "agent_id": self.agent_id,
            "msg_task_id": msg_task_id
        }
        
        try:
            await dingtalk_client.post("/topapi/message/corpconversation/recall", data=data)
            logger.info(f"工作通知撤回成功: {msg_task_id}")
            return True
            
        except Exception as e:
            logger.error(f"撤回工作通知失败: {e}")
            raise
    
    def _get_status_text(self, status: int) -> str:
        """获取状态文本描述"""
        status_map = {
            0: "未开始",
            1: "处理中", 
            2: "处理完毕"
        }
        return status_map.get(status, "未知状态")


def create_notification_api(agent_id: str) -> NotificationAPI:
    """
    创建通知API实例
    
    Args:
        agent_id: 钉钉应用的AgentId
    
    Returns:
        通知API实例
    """
    return NotificationAPI(agent_id)

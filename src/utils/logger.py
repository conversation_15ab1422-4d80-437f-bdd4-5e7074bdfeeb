"""
日志配置模块

提供统一的日志配置和管理功能。
"""

import logging
import sys
from typing import Optional
from src.config import config


def setup_logger(
    name: str, 
    level: Optional[str] = None,
    format_string: Optional[str] = None
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别，默认使用配置中的级别
        format_string: 日志格式，默认使用标准格式
    
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复配置
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = level or config.log_level
    logger.setLevel(getattr(logging, log_level))
    
    # 创建控制台处理器（使用stderr避免干扰STDIO通信）
    handler = logging.StreamHandler(sys.stderr)
    handler.setLevel(getattr(logging, log_level))
    
    # 设置日志格式
    if format_string is None:
        format_string = (
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    formatter = logging.Formatter(format_string)
    handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(handler)
    
    # 防止日志向上传播
    logger.propagate = False
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
    
    Returns:
        日志记录器
    """
    return setup_logger(name)


# 创建默认日志记录器
default_logger = setup_logger("dingtalk_mcp")

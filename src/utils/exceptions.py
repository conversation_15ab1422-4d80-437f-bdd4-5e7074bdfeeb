"""
自定义异常类

定义钉钉MCP服务器的所有自定义异常。
"""


class DingTalkMCPError(Exception):
    """钉钉MCP服务器基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class DingTalkAPIError(DingTalkMCPError):
    """钉钉API调用异常"""
    
    def __init__(self, message: str, error_code: str = None, status_code: int = None):
        super().__init__(message, error_code)
        self.status_code = status_code


class AuthenticationError(DingTalkMCPError):
    """认证异常"""
    pass


class ConfigurationError(DingTalkMCPError):
    """配置异常"""
    pass


class ValidationError(DingTalkMCPError):
    """数据验证异常"""
    pass


class CacheError(DingTalkMCPError):
    """缓存异常"""
    pass


class TaskNotFoundError(DingTalkMCPError):
    """任务未找到异常"""
    pass


class ProjectNotFoundError(DingTalkMCPError):
    """项目未找到异常"""
    pass


class UserNotFoundError(DingTalkMCPError):
    """用户未找到异常"""
    pass


class DepartmentNotFoundError(DingTalkMCPError):
    """部门未找到异常"""
    pass


class RateLimitError(DingTalkMCPError):
    """API限流异常"""
    pass


class NetworkError(DingTalkMCPError):
    """网络异常"""
    pass

"""
钉钉API客户端

提供钉钉API的统一调用接口。
"""

import json
from typing import Dict, Any, Optional, List
import httpx

from src.config import config
from src.utils.logger import get_logger
from src.utils.auth import auth_manager
from src.utils.exceptions import DingTalkAPIError, NetworkError, RateLimitError

logger = get_logger(__name__)


class DingTalkClient:
    """钉钉API客户端"""
    
    def __init__(self):
        self.base_url = config.dingtalk_api_base_url
        self.timeout = config.dingtalk_api_timeout
        
        # HTTP客户端
        self._client = httpx.AsyncClient(
            timeout=self.timeout,
            proxies=config.get_proxies()
        )
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        need_token: bool = True
    ) -> Dict[str, Any]:
        """
        发起API请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            params: 查询参数
            need_token: 是否需要访问令牌
        
        Returns:
            API响应数据
        
        Raises:
            DingTalkAPIError: API调用失败
            NetworkError: 网络错误
            RateLimitError: 限流错误
        """
        url = f"{self.base_url}{endpoint}"
        
        # 添加访问令牌
        if need_token:
            access_token = await auth_manager.get_access_token()
            if params is None:
                params = {}
            params["access_token"] = access_token
        
        # 记录请求日志
        if config.enable_request_logging:
            logger.debug(f"API请求: {method} {url}")
            logger.debug(f"参数: {params}")
            logger.debug(f"数据: {data}")
        
        try:
            # 发起请求
            if method.upper() == "GET":
                response = await self._client.get(url, params=params)
            elif method.upper() == "POST":
                if data:
                    response = await self._client.post(
                        url, 
                        params=params, 
                        json=data,
                        headers={"Content-Type": "application/json"}
                    )
                else:
                    response = await self._client.post(url, params=params)
            else:
                raise DingTalkAPIError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            result = response.json()
            
            # 记录响应日志
            if config.enable_request_logging:
                logger.debug(f"API响应: {result}")
            
            # 检查钉钉API错误码
            errcode = result.get("errcode", 0)
            if errcode != 0:
                errmsg = result.get("errmsg", "未知错误")
                
                # 特殊错误处理
                if errcode in [40001, 40014, 42001]:  # 访问令牌相关错误
                    logger.warning("访问令牌无效，尝试刷新")
                    # 刷新令牌并重试一次
                    access_token = await auth_manager.get_access_token(force_refresh=True)
                    if params:
                        params["access_token"] = access_token
                    return await self._make_request(method, endpoint, data, params, False)
                
                elif errcode == 90018:  # 接口调用频率限制
                    raise RateLimitError(f"API调用频率限制: {errmsg}", error_code=str(errcode))
                
                else:
                    raise DingTalkAPIError(
                        f"钉钉API错误: {errmsg}",
                        error_code=str(errcode),
                        status_code=response.status_code
                    )
            
            return result
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP状态错误: {e.response.status_code}")
            raise DingTalkAPIError(
                f"HTTP错误: {e.response.status_code}",
                status_code=e.response.status_code
            )
        except httpx.RequestError as e:
            logger.error(f"网络请求错误: {e}")
            raise NetworkError(f"网络错误: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            raise DingTalkAPIError(f"响应格式错误: {e}")
        except Exception as e:
            logger.error(f"未知错误: {e}")
            raise DingTalkAPIError(f"未知错误: {e}")
    
    async def get(
        self, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """GET请求"""
        return await self._make_request("GET", endpoint, params=params)
    
    async def post(
        self, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """POST请求"""
        return await self._make_request("POST", endpoint, data=data, params=params)
    
    async def close(self):
        """关闭HTTP客户端"""
        await self._client.aclose()


# 全局客户端实例
dingtalk_client = DingTalkClient()

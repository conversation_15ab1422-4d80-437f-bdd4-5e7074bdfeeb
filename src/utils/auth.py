"""
钉钉认证工具模块

处理钉钉API的认证和访问令牌管理。
"""

import time
import hashlib
import hmac
import base64
import json
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import httpx

from src.config import config
from src.utils.logger import get_logger
from src.utils.exceptions import AuthenticationError, DingTalkAPIError

logger = get_logger(__name__)


class DingTalkAuth:
    """钉钉认证管理器"""
    
    def __init__(self):
        self.app_key = config.dingtalk_app_key
        self.app_secret = config.dingtalk_app_secret
        self.corp_id = config.dingtalk_corp_id
        self.base_url = config.dingtalk_api_base_url
        
        # 访问令牌缓存
        self._access_token: Optional[str] = None
        self._token_expires_at: Optional[datetime] = None
        
        # HTTP客户端
        self._client = httpx.AsyncClient(
            timeout=config.dingtalk_api_timeout,
            proxies=config.get_proxies()
        )
    
    async def get_access_token(self, force_refresh: bool = False) -> str:
        """
        获取访问令牌
        
        Args:
            force_refresh: 是否强制刷新令牌
        
        Returns:
            访问令牌
        
        Raises:
            AuthenticationError: 认证失败
        """
        # 检查缓存的令牌是否有效
        if not force_refresh and self._is_token_valid():
            logger.debug("使用缓存的访问令牌")
            return self._access_token
        
        logger.info("获取新的访问令牌")
        
        try:
            url = f"{self.base_url}/gettoken"
            params = {
                "appkey": self.app_key,
                "appsecret": self.app_secret
            }
            
            response = await self._client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get("errcode") != 0:
                error_msg = data.get("errmsg", "未知错误")
                raise AuthenticationError(
                    f"获取访问令牌失败: {error_msg}",
                    error_code=str(data.get("errcode"))
                )
            
            # 缓存令牌
            self._access_token = data["access_token"]
            # 钉钉访问令牌有效期为2小时，我们提前5分钟刷新
            expires_in = data.get("expires_in", 7200) - 300
            self._token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            logger.info("访问令牌获取成功")
            return self._access_token
            
        except httpx.HTTPError as e:
            logger.error(f"获取访问令牌网络错误: {e}")
            raise AuthenticationError(f"网络错误: {e}")
        except Exception as e:
            logger.error(f"获取访问令牌失败: {e}")
            raise AuthenticationError(f"获取访问令牌失败: {e}")
    
    def _is_token_valid(self) -> bool:
        """检查令牌是否有效"""
        if not self._access_token or not self._token_expires_at:
            return False
        return datetime.now() < self._token_expires_at
    
    def generate_signature(self, timestamp: int, secret: str) -> str:
        """
        生成钉钉签名
        
        Args:
            timestamp: 时间戳
            secret: 密钥
        
        Returns:
            签名字符串
        """
        string_to_sign = f"{timestamp}\n{secret}"
        hmac_code = hmac.new(
            secret.encode("utf-8"),
            string_to_sign.encode("utf-8"),
            digestmod=hashlib.sha256
        ).digest()
        sign = base64.b64encode(hmac_code).decode("utf-8")
        return sign
    
    async def close(self):
        """关闭HTTP客户端"""
        await self._client.aclose()


# 全局认证实例
auth_manager = DingTalkAuth()

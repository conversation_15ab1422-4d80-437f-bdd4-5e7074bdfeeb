"""
钉钉待办任务API模块

提供钉钉待办任务的创建、查询、更新、删除等功能。
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from src.utils.dingtalk_client import dingtalk_client
from src.utils.logger import get_logger
from src.utils.exceptions import TaskNotFoundError, ValidationError

logger = get_logger(__name__)


class TodoTask(BaseModel):
    """待办任务模型"""
    
    task_id: Optional[str] = Field(None, description="任务ID")
    subject: str = Field(..., description="任务标题", max_length=1024)
    description: Optional[str] = Field(None, description="任务描述", max_length=4096)
    due_time: Optional[int] = Field(None, description="截止时间（Unix时间戳，毫秒）")
    executor_ids: List[str] = Field(default_factory=list, description="执行人UnionId列表")
    participant_ids: List[str] = Field(default_factory=list, description="参与人UnionId列表")
    creator_id: str = Field(..., description="创建人UnionId")
    is_done: bool = Field(default=False, description="是否完成")
    priority: int = Field(default=0, description="优先级（0-100）")
    source_id: Optional[str] = Field(None, description="来源ID")


class TodoAPI:
    """钉钉待办任务API"""
    
    async def create_task(self, task: TodoTask) -> str:
        """
        创建待办任务
        
        Args:
            task: 待办任务对象
        
        Returns:
            任务ID
        
        Raises:
            ValidationError: 参数验证失败
            DingTalkAPIError: API调用失败
        """
        logger.info(f"创建待办任务: {task.subject}")
        
        # 构建请求数据
        data = {
            "unionId": task.creator_id,
            "subject": task.subject,
        }
        
        # 可选字段
        if task.description:
            data["description"] = task.description
        
        if task.due_time:
            data["dueTime"] = task.due_time
        
        if task.executor_ids:
            data["executorIds"] = task.executor_ids
        
        if task.participant_ids:
            data["participantIds"] = task.participant_ids
        
        try:
            response = await dingtalk_client.post("/v1.0/todo/users/tasks", data=data)
            task_id = response.get("id")
            
            if not task_id:
                raise ValidationError("创建任务失败：未返回任务ID")
            
            logger.info(f"待办任务创建成功: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建待办任务失败: {e}")
            raise
    
    async def get_tasks(
        self, 
        union_id: str,
        is_done: Optional[bool] = None,
        role_types: Optional[List[str]] = None,
        next_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询待办任务列表
        
        Args:
            union_id: 用户UnionId
            is_done: 完成状态过滤
            role_types: 角色类型过滤 ["executor", "creator", "participant"]
            next_token: 分页标记
        
        Returns:
            任务列表和分页信息
        """
        logger.info(f"查询用户待办任务: {union_id}")
        
        data = {
            "unionId": union_id
        }
        
        if is_done is not None:
            data["isDone"] = is_done
        
        if role_types:
            # 钉钉API要求的格式：[[\"executor\"], [\"creator\"]]
            data["roleTypes"] = [[role] for role in role_types]
        
        if next_token:
            data["nextToken"] = next_token
        
        try:
            response = await dingtalk_client.post("/v1.0/todo/users/tasks/list", data=data)
            
            tasks = response.get("todoCards", [])
            next_token = response.get("nextToken")
            
            logger.info(f"查询到 {len(tasks)} 个待办任务")
            
            return {
                "tasks": tasks,
                "next_token": next_token,
                "has_more": bool(next_token)
            }
            
        except Exception as e:
            logger.error(f"查询待办任务失败: {e}")
            raise
    
    async def update_task(
        self, 
        union_id: str,
        task_id: str,
        subject: Optional[str] = None,
        description: Optional[str] = None,
        due_time: Optional[int] = None,
        is_done: Optional[bool] = None,
        executor_ids: Optional[List[str]] = None,
        participant_ids: Optional[List[str]] = None
    ) -> bool:
        """
        更新待办任务
        
        Args:
            union_id: 用户UnionId
            task_id: 任务ID
            subject: 新标题
            description: 新描述
            due_time: 新截止时间
            is_done: 新完成状态
            executor_ids: 新执行人列表
            participant_ids: 新参与人列表
        
        Returns:
            是否更新成功
        """
        logger.info(f"更新待办任务: {task_id}")
        
        data = {
            "unionId": union_id,
            "taskId": task_id,
            "subject": subject or "待办任务"  # 标题是必填字段
        }
        
        # 可选字段
        if description is not None:
            data["description"] = description
        
        if due_time is not None:
            data["dueTime"] = due_time
        
        if is_done is not None:
            data["done"] = is_done
        
        if executor_ids is not None:
            data["executorIds"] = executor_ids
        
        if participant_ids is not None:
            data["participantIds"] = participant_ids
        
        try:
            await dingtalk_client.post("/v1.0/todo/users/tasks/update", data=data)
            logger.info(f"待办任务更新成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新待办任务失败: {e}")
            raise
    
    async def delete_task(self, union_id: str, task_id: str) -> bool:
        """
        删除待办任务
        
        Args:
            union_id: 用户UnionId
            task_id: 任务ID
        
        Returns:
            是否删除成功
        """
        logger.info(f"删除待办任务: {task_id}")
        
        data = {
            "unionId": union_id,
            "taskId": task_id
        }
        
        try:
            await dingtalk_client.post("/v1.0/todo/users/tasks/delete", data=data)
            logger.info(f"待办任务删除成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除待办任务失败: {e}")
            raise
    
    async def update_executor_status(
        self,
        union_id: str,
        task_id: str,
        executor_status_list: List[Dict[str, Any]]
    ) -> bool:
        """
        更新执行人任务状态
        
        Args:
            union_id: 任务所属用户UnionId
            task_id: 任务ID
            executor_status_list: 执行者状态列表
                格式: [{"id": "unionId", "isDone": true}]
        
        Returns:
            是否更新成功
        """
        logger.info(f"更新执行人任务状态: {task_id}")
        
        data = {
            "unionId": union_id,
            "taskId": task_id,
            "executorStatusList": executor_status_list
        }
        
        try:
            await dingtalk_client.post(
                "/v1.0/todo/users/tasks/executorStatus", 
                data=data
            )
            logger.info(f"执行人任务状态更新成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新执行人任务状态失败: {e}")
            raise


# 全局待办API实例
todo_api = TodoAPI()

"""
钉钉TB项目管理API模块

提供钉钉TB项目的创建、查询、任务管理等功能。
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from src.utils.dingtalk_client import dingtalk_client
from src.utils.logger import get_logger
from src.utils.exceptions import ProjectNotFoundError, ValidationError

logger = get_logger(__name__)


class Project(BaseModel):
    """项目模型"""
    
    project_id: Optional[str] = Field(None, description="项目ID")
    name: str = Field(..., description="项目名称")
    description: Optional[str] = Field(None, description="项目描述")
    creator_id: str = Field(..., description="创建人userId")
    status: Optional[str] = Field(None, description="项目状态")


class ProjectTask(BaseModel):
    """项目任务模型"""
    
    task_id: Optional[str] = Field(None, description="任务ID")
    project_id: str = Field(..., description="项目ID")
    content: str = Field(..., description="任务标题")
    note: Optional[str] = Field(None, description="任务备注")
    executor_id: Optional[str] = Field(None, description="执行人userId")
    start_date: Optional[str] = Field(None, description="开始时间")
    due_date: Optional[str] = Field(None, description="截止时间")
    creator_id: str = Field(..., description="创建人userId")


class ProjectAPI:
    """钉钉TB项目管理API"""
    
    async def create_project(self, user_id: str, name: str) -> str:
        """
        创建项目
        
        Args:
            user_id: 操作者userId
            name: 项目名称
        
        Returns:
            项目ID
        """
        logger.info(f"创建项目: {name}")
        
        data = {
            "userId": user_id,
            "name": name
        }
        
        try:
            response = await dingtalk_client.post("/v1.0/teamwork/projects", data=data)
            project_id = response.get("result")
            
            if not project_id:
                raise ValidationError("创建项目失败：未返回项目ID")
            
            logger.info(f"项目创建成功: {project_id}")
            return project_id
            
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            raise
    
    async def search_projects(
        self, 
        user_id: str, 
        name: str,
        max_results: int = 10,
        next_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        根据项目名称模糊查询项目
        
        Args:
            user_id: 操作者userId
            name: 项目名称（模糊查询）
            max_results: 最大返回数量
            next_token: 分页标记
        
        Returns:
            项目列表和分页信息
        """
        logger.info(f"搜索项目: {name}")
        
        data = {
            "userId": user_id,
            "name": name,
            "maxResults": max_results
        }
        
        if next_token:
            data["nextToken"] = next_token
        
        try:
            response = await dingtalk_client.post("/v1.0/teamwork/projects/search", data=data)
            
            projects = response.get("result", [])
            next_token = response.get("nextToken")
            
            logger.info(f"搜索到 {len(projects)} 个项目")
            
            return {
                "projects": projects,
                "next_token": next_token,
                "has_more": bool(next_token)
            }
            
        except Exception as e:
            logger.error(f"搜索项目失败: {e}")
            raise
    
    async def get_project_members(
        self, 
        user_id: str, 
        project_id: str,
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """
        查询项目成员
        
        Args:
            user_id: 操作者userId
            project_id: 项目ID
            max_results: 最大返回数量
        
        Returns:
            项目成员列表
        """
        logger.info(f"查询项目成员: {project_id}")
        
        data = {
            "userId": user_id,
            "projectId": project_id,
            "maxResults": max_results
        }
        
        try:
            response = await dingtalk_client.post("/v1.0/teamwork/projects/members", data=data)
            members = response.get("result", [])
            
            logger.info(f"项目成员数量: {len(members)}")
            return members
            
        except Exception as e:
            logger.error(f"查询项目成员失败: {e}")
            raise
    
    async def add_project_members(
        self, 
        user_id: str, 
        project_id: str, 
        user_ids: List[str]
    ) -> bool:
        """
        添加项目成员
        
        Args:
            user_id: 操作者userId
            project_id: 项目ID
            user_ids: 要添加的用户userId列表
        
        Returns:
            是否添加成功
        """
        logger.info(f"添加项目成员: {project_id}, 成员: {user_ids}")
        
        data = {
            "userId": user_id,
            "projectId": project_id,
            "userIds": user_ids
        }
        
        try:
            await dingtalk_client.post("/v1.0/teamwork/projects/members/add", data=data)
            logger.info(f"项目成员添加成功: {project_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加项目成员失败: {e}")
            raise
    
    async def remove_project_members(
        self, 
        user_id: str, 
        project_id: str, 
        user_ids: List[str]
    ) -> bool:
        """
        移除项目成员
        
        Args:
            user_id: 操作者userId
            project_id: 项目ID
            user_ids: 要移除的用户userId列表
        
        Returns:
            是否移除成功
        """
        logger.info(f"移除项目成员: {project_id}, 成员: {user_ids}")
        
        data = {
            "userId": user_id,
            "projectId": project_id,
            "userIds": user_ids
        }
        
        try:
            await dingtalk_client.post("/v1.0/teamwork/projects/members/remove", data=data)
            logger.info(f"项目成员移除成功: {project_id}")
            return True
            
        except Exception as e:
            logger.error(f"移除项目成员失败: {e}")
            raise
    
    async def create_project_task(
        self,
        user_id: str,
        project_id: str,
        content: str,
        executor_id: Optional[str] = None,
        note: Optional[str] = None,
        start_date: Optional[str] = None,
        due_date: Optional[str] = None
    ) -> str:
        """
        创建项目任务
        
        Args:
            user_id: 操作者userId
            project_id: 项目ID
            content: 任务标题
            executor_id: 执行人userId
            note: 任务备注
            start_date: 开始时间（ISO格式）
            due_date: 截止时间（ISO格式）
        
        Returns:
            任务ID
        """
        logger.info(f"创建项目任务: {content}")
        
        data = {
            "userId": user_id,
            "projectId": project_id,
            "content": content
        }
        
        # 可选字段
        if executor_id:
            data["executorId"] = executor_id
        if note:
            data["note"] = note
        if start_date:
            data["startDate"] = start_date
        if due_date:
            data["dueDate"] = due_date
        
        try:
            response = await dingtalk_client.post("/v1.0/teamwork/projects/tasks", data=data)
            task_id = response.get("result")
            
            if not task_id:
                raise ValidationError("创建任务失败：未返回任务ID")
            
            logger.info(f"项目任务创建成功: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建项目任务失败: {e}")
            raise


# 全局项目API实例
project_api = ProjectAPI()

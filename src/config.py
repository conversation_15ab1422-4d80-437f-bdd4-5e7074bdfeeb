"""
配置管理模块

管理钉钉MCP服务器的所有配置项，包括环境变量、API配置、安全设置等。
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field, validator


class DingTalkConfig(BaseSettings):
    """钉钉MCP服务器配置"""
    
    # 基础配置
    app_name: str = Field(default="dingtalk-mcp", env="MCP_SERVER_NAME")
    version: str = Field(default="0.1.0", env="MCP_SERVER_VERSION")
    debug: bool = Field(default=False, env="DEBUG_MODE")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # 钉钉应用配置
    dingtalk_app_key: str = Field(..., env="DINGTALK_APP_KEY")
    dingtalk_app_secret: str = Field(..., env="DINGTALK_APP_SECRET")
    dingtalk_corp_id: str = Field(..., env="DINGTALK_CORP_ID")
    
    # 钉钉API配置
    dingtalk_api_base_url: str = Field(
        default="https://oapi.dingtalk.com", 
        env="DINGTALK_API_BASE_URL"
    )
    dingtalk_api_timeout: int = Field(default=30, env="DINGTALK_API_TIMEOUT")
    
    # 数据库配置
    database_url: str = Field(
        default="sqlite:///./data/dingtalk_mcp.db", 
        env="DATABASE_URL"
    )
    
    # 缓存配置
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_ttl: int = Field(default=300, env="CACHE_TTL")
    
    # 安全配置
    jwt_secret_key: str = Field(..., env="JWT_SECRET_KEY")
    encryption_key: str = Field(..., env="ENCRYPTION_KEY")
    
    # 代理配置
    http_proxy: Optional[str] = Field(default=None, env="HTTP_PROXY")
    https_proxy: Optional[str] = Field(default=None, env="HTTPS_PROXY")
    
    # 请求配置
    enable_request_logging: bool = Field(default=False, env="ENABLE_REQUEST_LOGGING")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是: {valid_levels}')
        return v.upper()
    
    @validator('dingtalk_api_timeout')
    def validate_timeout(cls, v):
        """验证API超时时间"""
        if v <= 0 or v > 300:
            raise ValueError('API超时时间必须在1-300秒之间')
        return v
    
    @validator('cache_ttl')
    def validate_cache_ttl(cls, v):
        """验证缓存TTL"""
        if v < 0:
            raise ValueError('缓存TTL不能为负数')
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        
    def get_proxies(self) -> Optional[dict]:
        """获取代理配置"""
        if self.http_proxy or self.https_proxy:
            return {
                "http://": self.http_proxy,
                "https://": self.https_proxy,
            }
        return None


# 全局配置实例
config = DingTalkConfig()

"""
FastMCP模拟实现

用于验证和测试目的的简化FastMCP实现。
"""

import json
import sys
import asyncio
from typing import Any, Callable, Dict, List, Optional
from functools import wraps


class MockMCP:
    """模拟的MCP服务器类"""
    
    def __init__(self, name: str = "dingtalk-mcp"):
        self.name = name
        self.tools: Dict[str, Dict[str, Any]] = {}
        self.prompts: Dict[str, Dict[str, Any]] = {}
        self.resources: Dict[str, Dict[str, Any]] = {}
    
    def tool(self, name: Optional[str] = None, description: Optional[str] = None):
        """工具装饰器"""
        def decorator(func: Callable):
            tool_name = name or func.__name__
            
            # 获取函数签名信息
            import inspect
            sig = inspect.signature(func)
            
            # 构建工具定义
            tool_def = {
                "name": tool_name,
                "description": description or func.__doc__ or f"Tool: {tool_name}",
                "function": func,
                "parameters": {}
            }
            
            # 解析参数
            for param_name, param in sig.parameters.items():
                param_info = {
                    "type": "string",  # 简化处理，实际应该根据类型注解确定
                    "required": param.default == inspect.Parameter.empty
                }
                
                if param.annotation != inspect.Parameter.empty:
                    # 简化的类型映射
                    if param.annotation == int:
                        param_info["type"] = "integer"
                    elif param.annotation == bool:
                        param_info["type"] = "boolean"
                    elif param.annotation == list:
                        param_info["type"] = "array"
                
                tool_def["parameters"][param_name] = param_info
            
            self.tools[tool_name] = tool_def
            
            @wraps(func)
            async def wrapper(*args, **kwargs):
                return await func(*args, **kwargs)
            
            return wrapper
        
        return decorator
    
    def prompt(self, name: Optional[str] = None, description: Optional[str] = None):
        """提示词装饰器"""
        def decorator(func: Callable):
            prompt_name = name or func.__name__
            self.prompts[prompt_name] = {
                "name": prompt_name,
                "description": description or func.__doc__ or f"Prompt: {prompt_name}",
                "function": func
            }
            return func
        return decorator
    
    def resource(self, uri_template: str, name: Optional[str] = None, description: Optional[str] = None):
        """资源装饰器"""
        def decorator(func: Callable):
            resource_name = name or func.__name__
            self.resources[resource_name] = {
                "name": resource_name,
                "uri_template": uri_template,
                "description": description or func.__doc__ or f"Resource: {resource_name}",
                "function": func
            }
            return func
        return decorator
    
    def get_tools_info(self) -> List[Dict[str, Any]]:
        """获取工具信息"""
        tools_info = []
        for tool_name, tool_def in self.tools.items():
            tools_info.append({
                "name": tool_def["name"],
                "description": tool_def["description"],
                "parameters": tool_def["parameters"]
            })
        return tools_info
    
    def get_prompts_info(self) -> List[Dict[str, Any]]:
        """获取提示词信息"""
        prompts_info = []
        for prompt_name, prompt_def in self.prompts.items():
            prompts_info.append({
                "name": prompt_def["name"],
                "description": prompt_def["description"]
            })
        return prompts_info
    
    def get_resources_info(self) -> List[Dict[str, Any]]:
        """获取资源信息"""
        resources_info = []
        for resource_name, resource_def in self.resources.items():
            resources_info.append({
                "name": resource_def["name"],
                "uri_template": resource_def["uri_template"],
                "description": resource_def["description"]
            })
        return resources_info
    
    async def call_tool(self, tool_name: str, **kwargs) -> Any:
        """调用工具"""
        if tool_name not in self.tools:
            raise ValueError(f"Tool '{tool_name}' not found")
        
        tool_func = self.tools[tool_name]["function"]
        return await tool_func(**kwargs)
    
    def run_stdio(self):
        """运行STDIO模式（模拟）"""
        print(f"🚀 {self.name} MCP服务器启动成功")
        print(f"📊 已注册 {len(self.tools)} 个工具")
        print(f"📝 已注册 {len(self.prompts)} 个提示词")
        print(f"📁 已注册 {len(self.resources)} 个资源")

        # 输出工具信息
        if self.tools:
            print("\n🔧 可用工具:")
            for tool_name, tool_def in self.tools.items():
                print(f"  - {tool_name}: {tool_def['description']}")

        # 输出提示词信息
        if self.prompts:
            print("\n📝 可用提示词:")
            for prompt_name, prompt_def in self.prompts.items():
                print(f"  - {prompt_name}: {prompt_def['description']}")

        # 输出资源信息
        if self.resources:
            print("\n📁 可用资源:")
            for resource_name, resource_def in self.resources.items():
                print(f"  - {resource_name}: {resource_def['description']}")

        print("\n✅ MCP服务器验证完成")
        return True

    def run(self):
        """运行MCP服务器（模拟）"""
        return self.run_stdio()


# 创建全局实例
mcp = MockMCP("dingtalk-mcp")

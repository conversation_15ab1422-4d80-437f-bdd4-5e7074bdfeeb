"""
钉钉待办任务管理工具

提供创建、查询、更新、删除待办任务的MCP工具。
"""

import json
from typing import List, Optional
from datetime import datetime

from src.utils.todo_api import todo_api, TodoTask
from src.utils.logger import get_logger
from src.utils.exceptions import ValidationError, TaskNotFoundError

logger = get_logger(__name__)


async def create_todo_task(
    creator_id: str,
    subject: str,
    description: Optional[str] = None,
    due_time: Optional[int] = None,
    executor_ids: Optional[List[str]] = None,
    participant_ids: Optional[List[str]] = None,
    priority: int = 0
) -> str:
    """
    创建待办任务
    
    Args:
        creator_id: 创建人UnionId
        subject: 任务标题（必填，最大1024字符）
        description: 任务描述（可选，最大4096字符）
        due_time: 截止时间（Unix时间戳，毫秒）
        executor_ids: 执行人UnionId列表
        participant_ids: 参与人UnionId列表
        priority: 优先级（0-100）
    
    Returns:
        创建成功的任务ID
    
    Raises:
        ValidationError: 参数验证失败
        DingTalkAPIError: API调用失败
    """
    try:
        logger.info(f"创建待办任务: {subject}")
        
        # 参数验证
        if not creator_id:
            raise ValidationError("创建人UnionId不能为空")
        
        if not subject or len(subject.strip()) == 0:
            raise ValidationError("任务标题不能为空")
        
        if len(subject) > 1024:
            raise ValidationError("任务标题不能超过1024个字符")
        
        if description and len(description) > 4096:
            raise ValidationError("任务描述不能超过4096个字符")
        
        if priority < 0 or priority > 100:
            raise ValidationError("优先级必须在0-100之间")
        
        # 创建任务对象
        task = TodoTask(
            creator_id=creator_id,
            subject=subject.strip(),
            description=description.strip() if description else None,
            due_time=due_time,
            executor_ids=executor_ids or [],
            participant_ids=participant_ids or [],
            priority=priority
        )
        
        # 调用API创建任务
        task_id = await todo_api.create_task(task)
        
        result = f"✅ 待办任务创建成功！\n\n📋 任务信息:\n• 任务ID: {task_id}\n• 标题: {subject}"
        
        if description:
            result += f"\n• 描述: {description}"
        
        if due_time:
            due_date = datetime.fromtimestamp(due_time / 1000).strftime("%Y-%m-%d %H:%M:%S")
            result += f"\n• 截止时间: {due_date}"
        
        if executor_ids:
            result += f"\n• 执行人数量: {len(executor_ids)}"
        
        if participant_ids:
            result += f"\n• 参与人数量: {len(participant_ids)}"
        
        result += f"\n• 优先级: {priority}"
        
        return result
        
    except Exception as e:
        logger.error(f"创建待办任务失败: {e}")
        return f"❌ 创建待办任务失败: {str(e)}"


async def get_todo_tasks(
    union_id: str,
    is_done: Optional[bool] = None,
    role_types: Optional[List[str]] = None,
    max_results: int = 10
) -> str:
    """
    查询待办任务列表
    
    Args:
        union_id: 用户UnionId
        is_done: 完成状态过滤（None=全部，True=已完成，False=未完成）
        role_types: 角色类型过滤（executor=执行人，creator=创建人，participant=参与人）
        max_results: 最大返回数量（1-50）
    
    Returns:
        任务列表的格式化字符串
    """
    try:
        logger.info(f"查询待办任务: {union_id}")
        
        # 参数验证
        if not union_id:
            raise ValidationError("用户UnionId不能为空")
        
        if max_results < 1 or max_results > 50:
            raise ValidationError("最大返回数量必须在1-50之间")
        
        if role_types:
            valid_roles = ["executor", "creator", "participant"]
            for role in role_types:
                if role not in valid_roles:
                    raise ValidationError(f"无效的角色类型: {role}，支持的类型: {valid_roles}")
        
        # 查询任务
        result = await todo_api.get_tasks(
            union_id=union_id,
            is_done=is_done,
            role_types=role_types
        )
        
        tasks = result.get("tasks", [])
        
        if not tasks:
            status_text = "全部" if is_done is None else ("已完成" if is_done else "未完成")
            role_text = "全部角色" if not role_types else "、".join(role_types)
            return f"📝 暂无{status_text}的待办任务（角色: {role_text}）"
        
        # 格式化输出
        output = f"📝 待办任务列表 (共{len(tasks)}个)\n\n"
        
        for i, task in enumerate(tasks[:max_results], 1):
            task_id = task.get("id", "未知")
            subject = task.get("subject", "无标题")
            is_done = task.get("isDone", False)
            status_icon = "✅" if is_done else "⏳"
            
            output += f"{i}. {status_icon} {subject}\n"
            output += f"   📌 ID: {task_id}\n"
            
            # 截止时间
            due_time = task.get("dueTime")
            if due_time:
                try:
                    due_date = datetime.fromtimestamp(due_time / 1000).strftime("%Y-%m-%d %H:%M")
                    output += f"   ⏰ 截止: {due_date}\n"
                except:
                    pass
            
            # 执行人
            executors = task.get("executorStatus", [])
            if executors:
                executor_count = len(executors)
                done_count = sum(1 for e in executors if e.get("isDone", False))
                output += f"   👥 执行人: {done_count}/{executor_count} 已完成\n"
            
            output += "\n"
        
        if len(tasks) > max_results:
            output += f"... 还有 {len(tasks) - max_results} 个任务未显示"
        
        return output
        
    except Exception as e:
        logger.error(f"查询待办任务失败: {e}")
        return f"❌ 查询待办任务失败: {str(e)}"


async def update_todo_task(
    union_id: str,
    task_id: str,
    subject: Optional[str] = None,
    description: Optional[str] = None,
    due_time: Optional[int] = None,
    is_done: Optional[bool] = None
) -> str:
    """
    更新待办任务
    
    Args:
        union_id: 用户UnionId
        task_id: 任务ID
        subject: 新标题
        description: 新描述
        due_time: 新截止时间（Unix时间戳，毫秒）
        is_done: 新完成状态
    
    Returns:
        更新结果的格式化字符串
    """
    try:
        logger.info(f"更新待办任务: {task_id}")
        
        # 参数验证
        if not union_id:
            raise ValidationError("用户UnionId不能为空")
        
        if not task_id:
            raise ValidationError("任务ID不能为空")
        
        if subject and len(subject) > 1024:
            raise ValidationError("任务标题不能超过1024个字符")
        
        if description and len(description) > 4096:
            raise ValidationError("任务描述不能超过4096个字符")
        
        # 更新任务
        success = await todo_api.update_task(
            union_id=union_id,
            task_id=task_id,
            subject=subject,
            description=description,
            due_time=due_time,
            is_done=is_done
        )
        
        if success:
            result = f"✅ 待办任务更新成功！\n\n📋 任务ID: {task_id}\n"
            
            if subject:
                result += f"• 新标题: {subject}\n"
            if description is not None:
                result += f"• 新描述: {description}\n"
            if due_time:
                due_date = datetime.fromtimestamp(due_time / 1000).strftime("%Y-%m-%d %H:%M:%S")
                result += f"• 新截止时间: {due_date}\n"
            if is_done is not None:
                status = "已完成" if is_done else "未完成"
                result += f"• 新状态: {status}\n"
            
            return result
        else:
            return f"❌ 待办任务更新失败: 任务ID {task_id}"
        
    except Exception as e:
        logger.error(f"更新待办任务失败: {e}")
        return f"❌ 更新待办任务失败: {str(e)}"


async def delete_todo_task(union_id: str, task_id: str) -> str:
    """
    删除待办任务
    
    Args:
        union_id: 用户UnionId
        task_id: 任务ID
    
    Returns:
        删除结果的格式化字符串
    """
    try:
        logger.info(f"删除待办任务: {task_id}")
        
        # 参数验证
        if not union_id:
            raise ValidationError("用户UnionId不能为空")
        
        if not task_id:
            raise ValidationError("任务ID不能为空")
        
        # 删除任务
        success = await todo_api.delete_task(union_id, task_id)
        
        if success:
            return f"✅ 待办任务删除成功！\n\n📋 已删除任务ID: {task_id}"
        else:
            return f"❌ 待办任务删除失败: 任务ID {task_id}"
        
    except Exception as e:
        logger.error(f"删除待办任务失败: {e}")
        return f"❌ 删除待办任务失败: {str(e)}"

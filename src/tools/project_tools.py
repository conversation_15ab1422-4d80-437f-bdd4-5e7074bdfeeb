"""
钉钉TB项目管理工具

提供项目创建、查询、成员管理、任务管理等MCP工具。
"""

from typing import List, Optional
from src.utils.project_api import project_api
from src.utils.logger import get_logger
from src.utils.exceptions import ValidationError, ProjectNotFoundError

logger = get_logger(__name__)


async def create_project(user_id: str, name: str) -> str:
    """
    创建TB项目
    
    Args:
        user_id: 操作者userId
        name: 项目名称
    
    Returns:
        创建结果的格式化字符串
    """
    try:
        logger.info(f"创建项目: {name}")
        
        # 参数验证
        if not user_id:
            raise ValidationError("操作者userId不能为空")
        
        if not name or len(name.strip()) == 0:
            raise ValidationError("项目名称不能为空")
        
        if len(name) > 100:
            raise ValidationError("项目名称不能超过100个字符")
        
        # 创建项目
        project_id = await project_api.create_project(user_id, name.strip())
        
        result = f"✅ TB项目创建成功！\n\n📁 项目信息:\n• 项目ID: {project_id}\n• 项目名称: {name}\n• 创建人: {user_id}"
        
        return result
        
    except Exception as e:
        logger.error(f"创建项目失败: {e}")
        return f"❌ 创建项目失败: {str(e)}"


async def search_projects(
    user_id: str, 
    name: str, 
    max_results: int = 10
) -> str:
    """
    搜索TB项目
    
    Args:
        user_id: 操作者userId
        name: 项目名称（模糊搜索）
        max_results: 最大返回数量（1-50）
    
    Returns:
        搜索结果的格式化字符串
    """
    try:
        logger.info(f"搜索项目: {name}")
        
        # 参数验证
        if not user_id:
            raise ValidationError("操作者userId不能为空")
        
        if not name or len(name.strip()) == 0:
            raise ValidationError("搜索关键词不能为空")
        
        if max_results < 1 or max_results > 50:
            raise ValidationError("最大返回数量必须在1-50之间")
        
        # 搜索项目
        result = await project_api.search_projects(
            user_id=user_id,
            name=name.strip(),
            max_results=max_results
        )
        
        projects = result.get("projects", [])
        
        if not projects:
            return f"🔍 未找到包含 '{name}' 的项目"
        
        # 格式化输出
        output = f"🔍 项目搜索结果 (关键词: {name})\n\n"
        output += f"📁 找到 {len(projects)} 个项目:\n\n"
        
        for i, project in enumerate(projects, 1):
            project_id = project.get("id", "未知")
            project_name = project.get("name", "无名称")
            status = project.get("status", "未知状态")
            
            output += f"{i}. 📁 {project_name}\n"
            output += f"   🆔 ID: {project_id}\n"
            output += f"   📊 状态: {status}\n\n"
        
        if result.get("has_more", False):
            output += "💡 提示: 还有更多结果，可以调整max_results参数查看更多"
        
        return output
        
    except Exception as e:
        logger.error(f"搜索项目失败: {e}")
        return f"❌ 搜索项目失败: {str(e)}"


async def get_project_members(
    user_id: str, 
    project_id: str, 
    max_results: int = 20
) -> str:
    """
    查询项目成员
    
    Args:
        user_id: 操作者userId
        project_id: 项目ID
        max_results: 最大返回数量
    
    Returns:
        项目成员列表的格式化字符串
    """
    try:
        logger.info(f"查询项目成员: {project_id}")
        
        # 参数验证
        if not user_id:
            raise ValidationError("操作者userId不能为空")
        
        if not project_id:
            raise ValidationError("项目ID不能为空")
        
        if max_results < 1 or max_results > 100:
            raise ValidationError("最大返回数量必须在1-100之间")
        
        # 查询项目成员
        members = await project_api.get_project_members(
            user_id=user_id,
            project_id=project_id,
            max_results=max_results
        )
        
        if not members:
            return f"👥 项目 {project_id} 暂无成员"
        
        # 格式化输出
        output = f"👥 项目成员列表 (项目ID: {project_id})\n\n"
        output += f"📊 成员总数: {len(members)}\n\n"
        
        for i, member in enumerate(members, 1):
            user_id = member.get("userId", "未知")
            name = member.get("name", "未知用户")
            role = member.get("role", "成员")
            
            output += f"{i}. 👤 {name}\n"
            output += f"   🆔 用户ID: {user_id}\n"
            output += f"   🎭 角色: {role}\n\n"
        
        return output
        
    except Exception as e:
        logger.error(f"查询项目成员失败: {e}")
        return f"❌ 查询项目成员失败: {str(e)}"


async def add_project_members(
    user_id: str, 
    project_id: str, 
    member_user_ids: List[str]
) -> str:
    """
    添加项目成员
    
    Args:
        user_id: 操作者userId
        project_id: 项目ID
        member_user_ids: 要添加的成员userId列表
    
    Returns:
        添加结果的格式化字符串
    """
    try:
        logger.info(f"添加项目成员: {project_id}")
        
        # 参数验证
        if not user_id:
            raise ValidationError("操作者userId不能为空")
        
        if not project_id:
            raise ValidationError("项目ID不能为空")
        
        if not member_user_ids or len(member_user_ids) == 0:
            raise ValidationError("成员userId列表不能为空")
        
        if len(member_user_ids) > 10:
            raise ValidationError("一次最多添加10个成员")
        
        # 去重
        unique_user_ids = list(set(member_user_ids))
        
        # 添加项目成员
        success = await project_api.add_project_members(
            user_id=user_id,
            project_id=project_id,
            user_ids=unique_user_ids
        )
        
        if success:
            result = f"✅ 项目成员添加成功！\n\n📁 项目ID: {project_id}\n👥 添加成员数量: {len(unique_user_ids)}\n📋 成员列表:\n"
            
            for i, member_id in enumerate(unique_user_ids, 1):
                result += f"  {i}. {member_id}\n"
            
            return result
        else:
            return f"❌ 项目成员添加失败: 项目ID {project_id}"
        
    except Exception as e:
        logger.error(f"添加项目成员失败: {e}")
        return f"❌ 添加项目成员失败: {str(e)}"


async def remove_project_members(
    user_id: str, 
    project_id: str, 
    member_user_ids: List[str]
) -> str:
    """
    移除项目成员
    
    Args:
        user_id: 操作者userId
        project_id: 项目ID
        member_user_ids: 要移除的成员userId列表
    
    Returns:
        移除结果的格式化字符串
    """
    try:
        logger.info(f"移除项目成员: {project_id}")
        
        # 参数验证
        if not user_id:
            raise ValidationError("操作者userId不能为空")
        
        if not project_id:
            raise ValidationError("项目ID不能为空")
        
        if not member_user_ids or len(member_user_ids) == 0:
            raise ValidationError("成员userId列表不能为空")
        
        if len(member_user_ids) > 10:
            raise ValidationError("一次最多移除10个成员")
        
        # 去重
        unique_user_ids = list(set(member_user_ids))
        
        # 移除项目成员
        success = await project_api.remove_project_members(
            user_id=user_id,
            project_id=project_id,
            user_ids=unique_user_ids
        )
        
        if success:
            result = f"✅ 项目成员移除成功！\n\n📁 项目ID: {project_id}\n👥 移除成员数量: {len(unique_user_ids)}\n📋 成员列表:\n"
            
            for i, member_id in enumerate(unique_user_ids, 1):
                result += f"  {i}. {member_id}\n"
            
            return result
        else:
            return f"❌ 项目成员移除失败: 项目ID {project_id}"
        
    except Exception as e:
        logger.error(f"移除项目成员失败: {e}")
        return f"❌ 移除项目成员失败: {str(e)}"


async def create_project_task(
    user_id: str,
    project_id: str,
    content: str,
    executor_id: Optional[str] = None,
    note: Optional[str] = None,
    start_date: Optional[str] = None,
    due_date: Optional[str] = None
) -> str:
    """
    创建项目任务
    
    Args:
        user_id: 操作者userId
        project_id: 项目ID
        content: 任务标题
        executor_id: 执行人userId
        note: 任务备注
        start_date: 开始时间（ISO格式，如：2023-12-01T10:00:00Z）
        due_date: 截止时间（ISO格式，如：2023-12-31T18:00:00Z）
    
    Returns:
        创建结果的格式化字符串
    """
    try:
        logger.info(f"创建项目任务: {content}")
        
        # 参数验证
        if not user_id:
            raise ValidationError("操作者userId不能为空")
        
        if not project_id:
            raise ValidationError("项目ID不能为空")
        
        if not content or len(content.strip()) == 0:
            raise ValidationError("任务标题不能为空")
        
        if len(content) > 200:
            raise ValidationError("任务标题不能超过200个字符")
        
        if note and len(note) > 1000:
            raise ValidationError("任务备注不能超过1000个字符")
        
        # 创建项目任务
        task_id = await project_api.create_project_task(
            user_id=user_id,
            project_id=project_id,
            content=content.strip(),
            executor_id=executor_id,
            note=note.strip() if note else None,
            start_date=start_date,
            due_date=due_date
        )
        
        result = f"✅ 项目任务创建成功！\n\n📋 任务信息:\n• 任务ID: {task_id}\n• 项目ID: {project_id}\n• 任务标题: {content}"
        
        if executor_id:
            result += f"\n• 执行人: {executor_id}"
        
        if note:
            result += f"\n• 备注: {note}"
        
        if start_date:
            result += f"\n• 开始时间: {start_date}"
        
        if due_date:
            result += f"\n• 截止时间: {due_date}"
        
        return result
        
    except Exception as e:
        logger.error(f"创建项目任务失败: {e}")
        return f"❌ 创建项目任务失败: {str(e)}"

"""
钉钉工作通知工具

提供发送工作通知、查询发送状态等MCP工具。
"""

from typing import Optional, List
from src.utils.notification_api import create_notification_api
from src.utils.logger import get_logger
from src.utils.exceptions import ValidationError
from src.config import config

logger = get_logger(__name__)


async def send_work_notification(
    agent_id: str,
    title: str,
    content: str,
    to_all_user: bool = False,
    userid_list: Optional[str] = None,
    dept_id_list: Optional[str] = None
) -> str:
    """
    发送工作通知
    
    Args:
        agent_id: 钉钉应用的AgentId
        title: 消息标题
        content: 消息内容（支持Markdown格式）
        to_all_user: 是否发送给全员
        userid_list: 接收人用户ID列表，逗号分隔，最多5000人
        dept_id_list: 接收部门ID列表，逗号分隔
    
    Returns:
        发送结果的格式化字符串
    """
    try:
        logger.info(f"发送工作通知: {title}")
        
        # 参数验证
        if not agent_id:
            raise ValidationError("应用AgentId不能为空")
        
        if not title or len(title.strip()) == 0:
            raise ValidationError("消息标题不能为空")
        
        if not content or len(content.strip()) == 0:
            raise ValidationError("消息内容不能为空")
        
        if len(title) > 200:
            raise ValidationError("消息标题不能超过200个字符")
        
        if len(content) > 5000:
            raise ValidationError("消息内容不能超过5000个字符")
        
        # 验证接收人设置
        if not to_all_user and not userid_list and not dept_id_list:
            raise ValidationError("必须指定接收人：设置to_all_user为true，或提供userid_list或dept_id_list")
        
        # 创建通知API实例
        notification_api = create_notification_api(agent_id)
        
        # 发送通知
        task_id = await notification_api.send_markdown_message(
            title=title.strip(),
            content=content.strip(),
            to_all_user=to_all_user,
            userid_list=userid_list,
            dept_id_list=dept_id_list
        )
        
        result = f"✅ 工作通知发送成功！\n\n📨 通知信息:\n• 任务ID: {task_id}\n• 标题: {title}"
        
        if to_all_user:
            result += "\n• 接收范围: 全员"
        else:
            if userid_list:
                user_count = len(userid_list.split(','))
                result += f"\n• 接收用户: {user_count} 人"
            if dept_id_list:
                dept_count = len(dept_id_list.split(','))
                result += f"\n• 接收部门: {dept_count} 个"
        
        result += f"\n\n💡 提示: 可使用任务ID {task_id} 查询发送状态和进度"
        
        return result
        
    except Exception as e:
        logger.error(f"发送工作通知失败: {e}")
        return f"❌ 发送工作通知失败: {str(e)}"


async def get_notification_send_result(agent_id: str, task_id: int) -> str:
    """
    查询工作通知发送结果
    
    Args:
        agent_id: 钉钉应用的AgentId
        task_id: 发送任务ID
    
    Returns:
        发送结果的格式化字符串
    """
    try:
        logger.info(f"查询通知发送结果: {task_id}")
        
        # 参数验证
        if not agent_id:
            raise ValidationError("应用AgentId不能为空")
        
        if not task_id:
            raise ValidationError("任务ID不能为空")
        
        # 创建通知API实例
        notification_api = create_notification_api(agent_id)
        
        # 查询发送结果
        send_result = await notification_api.get_send_result(task_id)
        
        # 格式化输出
        result = f"📊 通知发送结果 (任务ID: {task_id})\n\n"
        
        # 发送统计
        invalid_user_id_list = send_result.get("invalidUserIdList", [])
        invalid_dept_id_list = send_result.get("invalidDeptIdList", [])
        forbidden_user_id_list = send_result.get("forbiddenUserIdList", [])
        failed_user_id_list = send_result.get("failedUserIdList", [])
        read_user_id_list = send_result.get("readUserIdList", [])
        unread_user_id_list = send_result.get("unreadUserIdList", [])
        
        # 统计信息
        total_sent = len(read_user_id_list) + len(unread_user_id_list)
        total_failed = len(invalid_user_id_list) + len(forbidden_user_id_list) + len(failed_user_id_list)
        
        result += f"📈 发送统计:\n"
        result += f"• 成功发送: {total_sent} 人\n"
        result += f"• 已读: {len(read_user_id_list)} 人\n"
        result += f"• 未读: {len(unread_user_id_list)} 人\n"
        result += f"• 发送失败: {total_failed} 人\n"
        
        if total_failed > 0:
            result += f"\n❌ 失败详情:\n"
            if invalid_user_id_list:
                result += f"• 无效用户: {len(invalid_user_id_list)} 人\n"
            if invalid_dept_id_list:
                result += f"• 无效部门: {len(invalid_dept_id_list)} 个\n"
            if forbidden_user_id_list:
                result += f"• 禁止发送: {len(forbidden_user_id_list)} 人\n"
            if failed_user_id_list:
                result += f"• 其他失败: {len(failed_user_id_list)} 人\n"
        
        return result
        
    except Exception as e:
        logger.error(f"查询通知发送结果失败: {e}")
        return f"❌ 查询通知发送结果失败: {str(e)}"


async def get_notification_send_progress(agent_id: str, task_id: int) -> str:
    """
    查询工作通知发送进度
    
    Args:
        agent_id: 钉钉应用的AgentId
        task_id: 发送任务ID
    
    Returns:
        发送进度的格式化字符串
    """
    try:
        logger.info(f"查询通知发送进度: {task_id}")
        
        # 参数验证
        if not agent_id:
            raise ValidationError("应用AgentId不能为空")
        
        if not task_id:
            raise ValidationError("任务ID不能为空")
        
        # 创建通知API实例
        notification_api = create_notification_api(agent_id)
        
        # 查询发送进度
        progress_info = await notification_api.get_send_progress(task_id)
        
        progress = progress_info.get("progress", 0)
        status = progress_info.get("status", 0)
        status_text = progress_info.get("status_text", "未知")
        
        # 格式化输出
        result = f"📊 通知发送进度 (任务ID: {task_id})\n\n"
        result += f"📈 进度: {progress}%\n"
        result += f"📋 状态: {status_text}\n"
        
        # 进度条
        bar_length = 20
        filled_length = int(bar_length * progress // 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        result += f"▓ [{bar}] {progress}%\n"
        
        if status == 2:  # 处理完毕
            result += "\n✅ 发送任务已完成，可查询发送结果获取详细统计信息"
        elif status == 1:  # 处理中
            result += "\n⏳ 发送任务正在处理中，请稍后再查询"
        else:  # 未开始
            result += "\n⏸️ 发送任务尚未开始"
        
        return result
        
    except Exception as e:
        logger.error(f"查询通知发送进度失败: {e}")
        return f"❌ 查询通知发送进度失败: {str(e)}"


async def recall_work_notification(agent_id: str, msg_task_id: int) -> str:
    """
    撤回工作通知
    
    Args:
        agent_id: 钉钉应用的AgentId
        msg_task_id: 要撤回的消息任务ID
    
    Returns:
        撤回结果的格式化字符串
    """
    try:
        logger.info(f"撤回工作通知: {msg_task_id}")
        
        # 参数验证
        if not agent_id:
            raise ValidationError("应用AgentId不能为空")
        
        if not msg_task_id:
            raise ValidationError("消息任务ID不能为空")
        
        # 创建通知API实例
        notification_api = create_notification_api(agent_id)
        
        # 撤回通知
        success = await notification_api.recall_message(msg_task_id)
        
        if success:
            return f"✅ 工作通知撤回成功！\n\n📨 已撤回消息任务ID: {msg_task_id}\n\n💡 提示: 撤回后接收人将无法看到该消息"
        else:
            return f"❌ 工作通知撤回失败: 消息任务ID {msg_task_id}"
        
    except Exception as e:
        logger.error(f"撤回工作通知失败: {e}")
        return f"❌ 撤回工作通知失败: {str(e)}"

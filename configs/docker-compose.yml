version: '3.8'

services:
  dingtalk-mcp:
    build:
      context: ..
      dockerfile: configs/Dockerfile
    container_name: dingtalk-mcp-server
    restart: unless-stopped
    environment:
      # 钉钉应用配置
      - DINGTALK_APP_KEY=${DINGTALK_APP_KEY}
      - DINGTALK_APP_SECRET=${DINGTALK_APP_SECRET}
      - DINGTALK_CORP_ID=${DINGTALK_CORP_ID}
      
      # 安全配置
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      
      # 服务器配置
      - MCP_SERVER_NAME=dingtalk-mcp
      - MCP_SERVER_VERSION=0.1.0
      - LOG_LEVEL=INFO
      - DEBUG_MODE=false
      
      # API配置
      - DINGTALK_API_BASE_URL=https://oapi.dingtalk.com
      - DINGTALK_API_TIMEOUT=30
      - ENA<PERSON>E_REQUEST_LOGGING=false
      
      # 缓存配置
      - CACHE_ENABLED=true
      - CACHE_TTL=300
      
      # 数据库配置
      - DATABASE_URL=sqlite:///./data/dingtalk_mcp.db
      
      # 代理配置（如需要）
      # - HTTP_PROXY=http://proxy.example.com:8080
      # - HTTPS_PROXY=https://proxy.example.com:8080
    
    volumes:
      # 数据持久化
      - dingtalk_data:/app/data
      
      # 日志持久化
      - dingtalk_logs:/app/logs
      
      # 配置文件挂载（可选）
      - ./.env:/app/.env:ro
    
    ports:
      # MCP服务器通常使用STDIO，不需要暴露端口
      # 如果需要HTTP接口，可以取消注释下面的行
      # - "8000:8000"
    
    networks:
      - dingtalk_network
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

volumes:
  dingtalk_data:
    driver: local
  dingtalk_logs:
    driver: local

networks:
  dingtalk_network:
    driver: bridge

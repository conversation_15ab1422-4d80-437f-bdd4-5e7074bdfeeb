{"mcpServers": {"dingtalk-mcp": {"command": "python", "args": ["-m", "src.server"], "cwd": "/path/to/dingtalk-mcp", "env": {"DINGTALK_APP_KEY": "your_app_key_here", "DINGTALK_APP_SECRET": "your_app_secret_here", "DINGTALK_CORP_ID": "your_corp_id_here", "JWT_SECRET_KEY": "your_jwt_secret_key_here", "ENCRYPTION_KEY": "your_encryption_key_here", "LOG_LEVEL": "INFO", "DEBUG_MODE": "false", "CACHE_ENABLED": "true", "CACHE_TTL": "300", "DINGTALK_API_TIMEOUT": "30", "ENABLE_REQUEST_LOGGING": "false"}}}}
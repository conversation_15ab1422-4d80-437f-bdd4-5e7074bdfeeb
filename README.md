# 钉钉MCP服务器

基于FastMCP框架的钉钉集成服务器，为Claude Desktop提供钉钉待办管理、工作通知、TB项目管理等功能。

## 功能特性

### 🔧 待办任务管理
- ✅ 创建待办任务
- 📋 查询任务列表（支持状态和角色过滤）
- ✏️ 更新任务信息
- 🗑️ 删除任务
- 👥 管理执行人和参与人

### 📢 工作通知
- 📨 发送Markdown格式工作通知
- 📊 查询发送结果和统计
- 📈 实时查看发送进度
- ↩️ 撤回已发送通知

### 📁 TB项目管理
- 🆕 创建TB项目
- 🔍 搜索项目（模糊匹配）
- 👥 管理项目成员
- 📋 创建和管理项目任务

## 快速开始

### 1. 环境准备

确保已安装Python 3.11+：

```bash
python --version
```

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd dingtalk-mcp

# 安装依赖
pip install -e .
```

### 3. 配置环境变量

复制环境变量模板：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的钉钉应用信息：

```bash
# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key_here
DINGTALK_APP_SECRET=your_app_secret_here
DINGTALK_CORP_ID=your_corp_id_here

# 安全配置
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here
```

### 4. 配置Claude Desktop

编辑Claude Desktop的配置文件：

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

添加以下配置：

```json
{
  "mcpServers": {
    "dingtalk-mcp": {
      "command": "python",
      "args": ["-m", "src.server"],
      "cwd": "/path/to/dingtalk-mcp",
      "env": {
        "DINGTALK_APP_KEY": "your_app_key_here",
        "DINGTALK_APP_SECRET": "your_app_secret_here",
        "DINGTALK_CORP_ID": "your_corp_id_here",
        "JWT_SECRET_KEY": "your_jwt_secret_key_here",
        "ENCRYPTION_KEY": "your_encryption_key_here"
      }
    }
  }
}
```

### 5. 启动服务

```bash
python -m src.server
```

## 钉钉应用配置

### 1. 创建钉钉应用

1. 登录[钉钉开放平台](https://open.dingtalk.com/)
2. 创建企业内部应用
3. 获取AppKey、AppSecret和CorpId

### 2. 配置应用权限

确保应用具有以下权限：
- 待办任务管理权限
- 工作通知发送权限
- TB项目管理权限
- 用户信息读取权限

### 3. 配置回调地址（如需要）

如果需要接收钉钉回调，请配置相应的回调地址。

## 使用示例

### 待办任务管理

```python
# 创建待办任务
await dingtalk_create_todo(
    creator_id="user_union_id",
    subject="完成项目文档",
    description="编写项目的技术文档和用户手册",
    due_time=1703750400000,  # 2023-12-28的时间戳
    executor_ids=["executor_union_id"],
    priority=80
)

# 查询待办任务
await dingtalk_get_todos(
    union_id="user_union_id",
    is_done=False,
    role_types=["creator", "executor"]
)
```

### 工作通知

```python
# 发送工作通知
await dingtalk_send_notification(
    agent_id="your_agent_id",
    title="项目进度更新",
    content="## 本周进度\n\n- ✅ 完成需求分析\n- 🔄 正在进行开发\n- ⏳ 下周开始测试",
    userid_list="user1,user2,user3"
)
```

### TB项目管理

```python
# 创建项目
await dingtalk_create_project(
    user_id="creator_user_id",
    name="新产品开发项目"
)

# 创建项目任务
await dingtalk_create_project_task(
    user_id="creator_user_id",
    project_id="project_id",
    content="设计产品原型",
    executor_id="designer_user_id",
    due_date="2023-12-31T18:00:00Z"
)
```

## Docker部署

### 使用Docker Compose

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
vim .env

# 启动服务
docker-compose -f configs/docker-compose.yml up -d

# 查看日志
docker-compose -f configs/docker-compose.yml logs -f
```

### 使用Docker

```bash
# 构建镜像
docker build -f configs/Dockerfile -t dingtalk-mcp .

# 运行容器
docker run -d \
  --name dingtalk-mcp \
  --env-file .env \
  -v $(pwd)/data:/app/data \
  dingtalk-mcp
```

## 开发指南

### 项目结构

```
dingtalk-mcp/
├── src/
│   ├── __init__.py
│   ├── server.py              # MCP服务器主文件
│   ├── config.py              # 配置管理
│   ├── tools/                 # MCP工具实现
│   │   ├── todo_tools.py      # 待办任务工具
│   │   ├── notification_tools.py  # 工作通知工具
│   │   └── project_tools.py   # 项目管理工具
│   ├── utils/                 # 工具函数
│   │   ├── auth.py           # 认证管理
│   │   ├── dingtalk_client.py # 钉钉API客户端
│   │   ├── todo_api.py       # 待办API
│   │   ├── notification_api.py # 通知API
│   │   └── project_api.py    # 项目API
│   ├── prompts/              # 提示词模板
│   └── resources/            # 资源访问
├── tests/                    # 测试用例
├── configs/                  # 配置文件
├── docs/                     # 文档
└── data/                     # 数据存储
```

### 运行测试

```bash
# 安装测试依赖
pip install pytest pytest-asyncio

# 运行测试
pytest tests/

# 运行特定测试
pytest tests/test_todo_tools.py -v
```

### 代码格式化

```bash
# 格式化代码
black src/ tests/

# 检查代码风格
ruff check src/ tests/

# 类型检查
mypy src/
```

## 故障排除

### 常见问题

1. **认证失败**
   - 检查AppKey、AppSecret和CorpId是否正确
   - 确认应用权限配置是否完整

2. **API调用失败**
   - 检查网络连接
   - 确认API权限
   - 查看日志获取详细错误信息

3. **Claude Desktop连接失败**
   - 检查配置文件路径是否正确
   - 确认环境变量设置
   - 重启Claude Desktop

### 日志查看

```bash
# 查看服务器日志
tail -f logs/dingtalk_mcp.log

# 调试模式运行
DEBUG_MODE=true python -m src.server
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 支持

如有问题或建议，请提交Issue或联系维护者。

# 05 - 部署维护指南

## 概述

本指南介绍MCP Server的生产环境部署、容器化、监控和维护最佳实践。

## 5.1 生产环境部署

### 5.1.1 环境准备

**生产环境检查清单**
```bash
#!/bin/bash
# production-check.sh - 生产环境检查脚本

echo "=== MCP Server生产环境检查 ==="

# 检查Python版本
echo "检查Python版本..."
python3 --version || { echo "Python3未安装"; exit 1; }

# 检查依赖
echo "检查依赖..."
pip3 list | grep -E "(mcp|httpx|pydantic)" || { echo "缺少必要依赖"; exit 1; }

# 检查配置文件
echo "检查配置文件..."
[ -f "configs/production.json" ] || { echo "生产配置文件不存在"; exit 1; }

# 检查日志目录
echo "检查日志目录..."
mkdir -p logs
[ -w "logs" ] || { echo "日志目录不可写"; exit 1; }

# 检查数据目录权限
echo "检查数据目录..."
mkdir -p data
[ -r "data" ] || { echo "数据目录不可读"; exit 1; }

# 检查端口可用性（如果使用HTTP传输）
echo "检查端口..."
netstat -tuln | grep ":3000" && { echo "端口3000已被占用"; exit 1; }

echo "✅ 生产环境检查通过"
```

**生产配置文件（configs/production.json）**
```json
{
  "server": {
    "name": "mcp-server-prod",
    "version": "1.0.0",
    "environment": "production",
    "transport": {
      "type": "stdio",
      "options": {
        "bufferSize": 16384,
        "timeout": 60000
      }
    }
  },
  "capabilities": {
    "tools": {
      "enabled": true,
      "maxConcurrent": 20,
      "timeout": 30000
    },
    "prompts": {
      "enabled": true,
      "cacheEnabled": true,
      "cacheTTL": 1800
    },
    "resources": {
      "enabled": true,
      "cacheEnabled": true,
      "cacheTTL": 3600,
      "maxFileSize": "50MB"
    }
  },
  "security": {
    "allowedPaths": ["/app/data", "/app/configs"],
    "blockedPaths": ["/etc", "/var", "/usr", "/root"],
    "maxRequestSize": "10MB",
    "rateLimiting": {
      "enabled": true,
      "maxRequests": 1000,
      "windowMs": 60000
    },
    "authentication": {
      "enabled": true,
      "method": "api_key"
    }
  },
  "logging": {
    "level": "info",
    "format": "json",
    "outputs": ["file", "stderr"],
    "file": {
      "path": "/app/logs/server.log",
      "maxSize": "500MB",
      "maxFiles": 10,
      "compress": true
    }
  },
  "performance": {
    "caching": {
      "enabled": true,
      "maxSize": "500MB",
      "ttl": 7200
    },
    "compression": {
      "enabled": true,
      "level": 9
    },
    "pooling": {
      "maxConnections": 100,
      "timeout": 30000
    }
  },
  "monitoring": {
    "metrics": {
      "enabled": true,
      "endpoint": "/metrics",
      "interval": 60
    },
    "healthCheck": {
      "enabled": true,
      "endpoint": "/health",
      "timeout": 5000
    }
  }
}
```

### 5.1.2 部署脚本

**Python部署脚本（deploy.py）**
```python
#!/usr/bin/env python3
"""
MCP Server部署脚本
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Deployer:
    """部署管理器"""
    
    def __init__(self, config_path: str = "configs/production.json"):
        self.config_path = Path(config_path)
        self.project_root = Path.cwd()
        self.deploy_dir = Path("/opt/mcp-server")
        
    def deploy(self):
        """执行部署"""
        try:
            logger.info("开始部署MCP Server...")
            
            # 1. 验证环境
            self._validate_environment()
            
            # 2. 创建部署目录
            self._create_deploy_directory()
            
            # 3. 复制文件
            self._copy_files()
            
            # 4. 安装依赖
            self._install_dependencies()
            
            # 5. 配置服务
            self._configure_service()
            
            # 6. 启动服务
            self._start_service()
            
            logger.info("✅ 部署完成")
            
        except Exception as e:
            logger.error(f"❌ 部署失败: {e}")
            sys.exit(1)
    
    def _validate_environment(self):
        """验证部署环境"""
        logger.info("验证部署环境...")
        
        # 检查Python版本
        if sys.version_info < (3, 10):
            raise RuntimeError("需要Python 3.10或更高版本")
        
        # 检查配置文件
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        # 检查权限
        if os.geteuid() != 0:
            raise PermissionError("需要root权限进行部署")
    
    def _create_deploy_directory(self):
        """创建部署目录"""
        logger.info(f"创建部署目录: {self.deploy_dir}")
        
        self.deploy_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        for subdir in ["src", "configs", "logs", "data"]:
            (self.deploy_dir / subdir).mkdir(exist_ok=True)
    
    def _copy_files(self):
        """复制项目文件"""
        logger.info("复制项目文件...")
        
        # 复制源代码
        shutil.copytree(
            self.project_root / "src",
            self.deploy_dir / "src",
            dirs_exist_ok=True
        )
        
        # 复制配置文件
        shutil.copy2(
            self.config_path,
            self.deploy_dir / "configs" / "server-config.json"
        )
        
        # 复制其他必要文件
        for file in ["requirements.txt", "pyproject.toml"]:
            if (self.project_root / file).exists():
                shutil.copy2(
                    self.project_root / file,
                    self.deploy_dir / file
                )
    
    def _install_dependencies(self):
        """安装依赖"""
        logger.info("安装依赖...")
        
        # 创建虚拟环境
        subprocess.run([
            sys.executable, "-m", "venv",
            str(self.deploy_dir / "venv")
        ], check=True)
        
        # 安装依赖
        pip_path = self.deploy_dir / "venv" / "bin" / "pip"
        subprocess.run([
            str(pip_path), "install", "-r",
            str(self.deploy_dir / "requirements.txt")
        ], check=True)
    
    def _configure_service(self):
        """配置系统服务"""
        logger.info("配置系统服务...")
        
        service_content = f"""[Unit]
Description=MCP Server
After=network.target

[Service]
Type=simple
User=mcp
Group=mcp
WorkingDirectory={self.deploy_dir}
Environment=PYTHONPATH={self.deploy_dir}
ExecStart={self.deploy_dir}/venv/bin/python -m src.server
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
        
        # 写入服务文件
        service_path = Path("/etc/systemd/system/mcp-server.service")
        service_path.write_text(service_content)
        
        # 重新加载systemd
        subprocess.run(["systemctl", "daemon-reload"], check=True)
        subprocess.run(["systemctl", "enable", "mcp-server"], check=True)
    
    def _start_service(self):
        """启动服务"""
        logger.info("启动服务...")
        subprocess.run(["systemctl", "start", "mcp-server"], check=True)
        subprocess.run(["systemctl", "status", "mcp-server"], check=True)

if __name__ == "__main__":
    deployer = Deployer()
    deployer.deploy()
```

## 5.2 容器化部署

### 5.2.1 Docker配置

**Dockerfile**
```dockerfile
# 多阶段构建
FROM python:3.11-slim as builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt pyproject.toml ./

# 安装Python依赖
RUN pip install --no-cache-dir --user -r requirements.txt

# 生产阶段
FROM python:3.11-slim

# 创建非root用户
RUN groupadd -r mcp && useradd -r -g mcp mcp

# 设置工作目录
WORKDIR /app

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制Python包
COPY --from=builder /root/.local /home/<USER>/.local

# 复制应用代码
COPY src/ ./src/
COPY configs/ ./configs/

# 创建必要目录
RUN mkdir -p logs data && \
    chown -R mcp:mcp /app

# 切换到非root用户
USER mcp

# 设置环境变量
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV MCP_CONFIG_PATH=/app/configs/production.json

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import src.server; print('OK')" || exit 1

# 暴露端口（如果使用HTTP传输）
# EXPOSE 3000

# 启动命令
CMD ["python", "-m", "src.server"]
```

**docker-compose.yml**
```yaml
version: '3.8'

services:
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mcp-server
    restart: unless-stopped
    
    # 环境变量
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - MCP_CONFIG_PATH=/app/configs/production.json
    
    # 卷挂载
    volumes:
      - ./data:/app/data:rw
      - ./logs:/app/logs:rw
      - ./configs/production.json:/app/configs/production.json:ro
    
    # 网络（如果使用HTTP传输）
    # ports:
    #   - "3000:3000"
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import src.server; print('OK')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # 可选：添加监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro

volumes:
  grafana-storage:

networks:
  default:
    name: mcp-network
```

**构建和运行**
```bash
# 构建镜像
docker build -t mcp-server:latest .

# 运行容器
docker-compose up -d

# 查看日志
docker-compose logs -f mcp-server

# 停止服务
docker-compose down

# 更新服务
docker-compose pull && docker-compose up -d
```

## 5.3 监控和日志

### 5.3.1 监控指标

**监控指标收集（src/utils/metrics.py）**
```python
import time
import psutil
import threading
from typing import Dict, Any
from collections import defaultdict, deque
import json

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.metrics = defaultdict(lambda: deque(maxlen=1000))
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.start_time = time.time()
        self._lock = threading.Lock()
    
    def increment_counter(self, name: str, value: int = 1, tags: Dict[str, str] = None):
        """增加计数器"""
        with self._lock:
            key = self._make_key(name, tags)
            self.counters[key] += value
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """设置仪表值"""
        with self._lock:
            key = self._make_key(name, tags)
            self.gauges[key] = value
    
    def record_timing(self, name: str, duration: float, tags: Dict[str, str] = None):
        """记录时间指标"""
        with self._lock:
            key = self._make_key(name, tags)
            self.metrics[key].append({
                'timestamp': time.time(),
                'value': duration
            })
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'uptime': time.time() - self.start_time,
            'process_count': len(psutil.pids())
        }
    
    def get_application_metrics(self) -> Dict[str, Any]:
        """获取应用指标"""
        with self._lock:
            return {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'timings': {k: list(v) for k, v in self.metrics.items()}
            }
    
    def export_prometheus(self) -> str:
        """导出Prometheus格式指标"""
        lines = []
        
        # 系统指标
        system = self.get_system_metrics()
        for name, value in system.items():
            lines.append(f"mcp_system_{name} {value}")
        
        # 计数器
        for key, value in self.counters.items():
            lines.append(f"mcp_counter_{key} {value}")
        
        # 仪表
        for key, value in self.gauges.items():
            lines.append(f"mcp_gauge_{key} {value}")
        
        return '\n'.join(lines)
    
    def _make_key(self, name: str, tags: Dict[str, str] = None) -> str:
        """生成指标键"""
        if not tags:
            return name
        
        tag_str = ','.join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"

# 全局指标收集器
metrics = MetricsCollector()

# 指标装饰器
def track_timing(metric_name: str, tags: Dict[str, str] = None):
    """时间跟踪装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                metrics.increment_counter(f"{metric_name}_success", tags=tags)
                return result
            except Exception as e:
                metrics.increment_counter(f"{metric_name}_error", tags=tags)
                raise
            finally:
                duration = time.time() - start_time
                metrics.record_timing(metric_name, duration, tags=tags)
        
        return wrapper
    return decorator

# 在工具中使用指标
@track_timing("tool_execution", {"tool": "calculator"})
@mcp.tool()
async def monitored_calculator(operation: str, a: float, b: float) -> str:
    """带监控的计算器工具"""
    metrics.increment_counter("tool_calls", tags={"tool": "calculator"})
    return await calculator_tool(operation, a, b)

# 健康检查端点
@mcp.tool()
async def health_check() -> str:
    """健康检查"""
    try:
        # 检查各种组件状态
        system_metrics = metrics.get_system_metrics()
        
        # 简单的健康检查逻辑
        if system_metrics['memory_percent'] > 90:
            raise Exception("内存使用率过高")
        
        if system_metrics['cpu_percent'] > 95:
            raise Exception("CPU使用率过高")
        
        return json.dumps({
            "status": "healthy",
            "timestamp": time.time(),
            "metrics": system_metrics
        })
        
    except Exception as e:
        return json.dumps({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        })

# 指标导出端点
@mcp.tool()
async def export_metrics() -> str:
    """导出指标"""
    return metrics.export_prometheus()
```

### 5.3.2 日志管理

**日志轮转配置（configs/logging.conf）**
```ini
[loggers]
keys=root,mcp

[handlers]
keys=consoleHandler,fileHandler,rotatingFileHandler

[formatters]
keys=simpleFormatter,jsonFormatter

[logger_root]
level=INFO
handlers=consoleHandler

[logger_mcp]
level=INFO
handlers=rotatingFileHandler
qualname=mcp
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=jsonFormatter
args=(sys.stderr,)

[handler_fileHandler]
class=FileHandler
level=INFO
formatter=jsonFormatter
args=('/app/logs/server.log',)

[handler_rotatingFileHandler]
class=handlers.RotatingFileHandler
level=INFO
formatter=jsonFormatter
args=('/app/logs/server.log', 'a', 100*1024*1024, 10)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s

[formatter_jsonFormatter]
class=pythonjsonlogger.jsonlogger.JsonFormatter
format=%(asctime)s %(name)s %(levelname)s %(message)s
```

**✅ 验证检查点**：监控指标收集正常，日志轮转配置生效

## 5.4 版本管理和更新

### 5.4.1 版本控制策略

**版本管理脚本（scripts/version-manager.py）**
```python
#!/usr/bin/env python3
"""
版本管理脚本
"""

import json
import subprocess
import sys
from pathlib import Path
from typing import Tuple

class VersionManager:
    """版本管理器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.version_file = self.project_root / "src" / "__version__.py"
        self.config_file = self.project_root / "configs" / "server-config.json"
    
    def get_current_version(self) -> str:
        """获取当前版本"""
        try:
            if self.version_file.exists():
                content = self.version_file.read_text()
                # 解析 __version__ = "x.y.z"
                for line in content.split('\n'):
                    if line.startswith('__version__'):
                        return line.split('"')[1]
            return "0.1.0"
        except Exception:
            return "0.1.0"
    
    def bump_version(self, part: str = "patch") -> str:
        """升级版本号"""
        current = self.get_current_version()
        major, minor, patch = map(int, current.split('.'))
        
        if part == "major":
            major += 1
            minor = 0
            patch = 0
        elif part == "minor":
            minor += 1
            patch = 0
        elif part == "patch":
            patch += 1
        else:
            raise ValueError(f"无效的版本部分: {part}")
        
        new_version = f"{major}.{minor}.{patch}"
        
        # 更新版本文件
        self._update_version_file(new_version)
        
        # 更新配置文件
        self._update_config_version(new_version)
        
        # 创建Git标签
        self._create_git_tag(new_version)
        
        return new_version
    
    def _update_version_file(self, version: str):
        """更新版本文件"""
        content = f'__version__ = "{version}"\n'
        self.version_file.write_text(content)
    
    def _update_config_version(self, version: str):
        """更新配置文件版本"""
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            config['server']['version'] = version
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
    
    def _create_git_tag(self, version: str):
        """创建Git标签"""
        try:
            subprocess.run(['git', 'add', '.'], check=True)
            subprocess.run(['git', 'commit', '-m', f'Bump version to {version}'], check=True)
            subprocess.run(['git', 'tag', f'v{version}'], check=True)
            print(f"✅ 创建Git标签: v{version}")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Git操作失败: {e}")

if __name__ == "__main__":
    manager = VersionManager()
    
    if len(sys.argv) > 1:
        part = sys.argv[1]
        new_version = manager.bump_version(part)
        print(f"版本已升级到: {new_version}")
    else:
        current = manager.get_current_version()
        print(f"当前版本: {current}")
```

### 5.4.2 自动化更新

**更新脚本（scripts/update.sh）**
```bash
#!/bin/bash
# 自动化更新脚本

set -e

echo "=== MCP Server自动更新 ==="

# 配置
REPO_URL="https://github.com/your-org/mcp-server.git"
DEPLOY_DIR="/opt/mcp-server"
BACKUP_DIR="/opt/mcp-server-backup"
SERVICE_NAME="mcp-server"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "需要root权限"
    exit 1
fi

# 1. 停止服务
echo "停止服务..."
systemctl stop $SERVICE_NAME

# 2. 备份当前版本
echo "备份当前版本..."
if [ -d "$DEPLOY_DIR" ]; then
    rm -rf "$BACKUP_DIR"
    cp -r "$DEPLOY_DIR" "$BACKUP_DIR"
fi

# 3. 拉取最新代码
echo "拉取最新代码..."
cd "$DEPLOY_DIR"
git fetch origin
git reset --hard origin/main

# 4. 更新依赖
echo "更新依赖..."
source venv/bin/activate
pip install -r requirements.txt

# 5. 运行测试
echo "运行测试..."
python -m pytest tests/ -v || {
    echo "测试失败，回滚..."
    rm -rf "$DEPLOY_DIR"
    mv "$BACKUP_DIR" "$DEPLOY_DIR"
    systemctl start $SERVICE_NAME
    exit 1
}

# 6. 重启服务
echo "重启服务..."
systemctl start $SERVICE_NAME
systemctl status $SERVICE_NAME

# 7. 健康检查
echo "健康检查..."
sleep 10
if systemctl is-active --quiet $SERVICE_NAME; then
    echo "✅ 更新成功"
    rm -rf "$BACKUP_DIR"
else
    echo "❌ 服务启动失败，回滚..."
    systemctl stop $SERVICE_NAME
    rm -rf "$DEPLOY_DIR"
    mv "$BACKUP_DIR" "$DEPLOY_DIR"
    systemctl start $SERVICE_NAME
    exit 1
fi

echo "=== 更新完成 ==="
```

**✅ 验证检查点**：版本管理流程建立，自动化更新脚本测试通过

---

**恭喜！** 🎉 您已完成MCP Server开发流程的全部学习。现在您可以：

1. **快速开始**：使用提供的模板快速搭建MCP Server
2. **生产部署**：按照部署指南将服务部署到生产环境
3. **持续改进**：利用监控和日志持续优化服务性能
4. **扩展功能**：基于核心框架添加更多业务功能

**下一步建议**：
- 查看 [templates/](./templates/) 目录获取可用模板
- 参考 [examples/](./examples/) 目录学习实际案例
- 阅读 [best-practices/](./best-practices/) 了解最佳实践

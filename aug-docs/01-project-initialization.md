# 01 - 项目初始化指南

## 概述

本指南将引导您完成MCP Server项目的初始化设置，包括环境准备、项目结构规划和基础配置。

## 1.1 环境准备

### 1.1.1 系统要求检查

**Python环境**
```bash
# 检查Python版本（需要 ≥ 3.10）
python --version

# 安装uv包管理器（推荐）
curl -LsSf https://astral.sh/uv/install.sh | sh
# 或使用pip
pip install uv
```

**Node.js环境**
```bash
# 检查Node.js版本（需要 ≥ 18.0）
node --version

# 检查npm版本
npm --version

# 安装pnpm（推荐）
npm install -g pnpm
```

### 1.1.2 开发工具安装

```bash
# Git（版本控制）
git --version

# VS Code（推荐IDE）
code --version

# Docker（容器化部署）
docker --version
```

**✅ 验证检查点**：所有工具版本符合要求

## 1.2 项目结构规划

### 1.2.1 标准项目结构

**Python项目结构**
```
my-mcp-server/
├── src/
│   ├── __init__.py
│   ├── server.py              # 主服务器文件
│   ├── tools/                 # 工具模块
│   │   ├── __init__.py
│   │   └── example_tool.py
│   ├── prompts/               # 提示词模块
│   │   ├── __init__.py
│   │   └── example_prompt.py
│   ├── resources/             # 资源模块
│   │   ├── __init__.py
│   │   └── example_resource.py
│   └── utils/                 # 工具函数
│       ├── __init__.py
│       └── helpers.py
├── tests/                     # 测试文件
│   ├── __init__.py
│   ├── test_tools.py
│   ├── test_prompts.py
│   └── test_resources.py
├── docs/                      # 文档
├── configs/                   # 配置文件
│   ├── development.json
│   └── production.json
├── pyproject.toml            # Python项目配置
├── requirements.txt          # 依赖列表
├── README.md
└── .gitignore
```

**TypeScript项目结构**
```
my-mcp-server/
├── src/
│   ├── index.ts              # 主服务器文件
│   ├── tools/                # 工具模块
│   │   └── exampleTool.ts
│   ├── prompts/              # 提示词模块
│   │   └── examplePrompt.ts
│   ├── resources/            # 资源模块
│   │   └── exampleResource.ts
│   └── utils/                # 工具函数
│       └── helpers.ts
├── tests/                    # 测试文件
│   ├── tools.test.ts
│   ├── prompts.test.ts
│   └── resources.test.ts
├── docs/                     # 文档
├── configs/                  # 配置文件
│   ├── development.json
│   └── production.json
├── package.json              # Node.js项目配置
├── tsconfig.json            # TypeScript配置
├── README.md
└── .gitignore
```

### 1.2.2 快速项目创建

**Python快速启动**
```bash
# 创建项目目录
mkdir my-mcp-server && cd my-mcp-server

# 初始化Python项目
uv init .

# 创建虚拟环境
uv venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

# 创建目录结构
mkdir -p src/{tools,prompts,resources,utils} tests configs docs
touch src/__init__.py src/server.py
touch src/tools/__init__.py src/prompts/__init__.py
touch src/resources/__init__.py src/utils/__init__.py
```

**TypeScript快速启动**
```bash
# 创建项目目录
mkdir my-mcp-server && cd my-mcp-server

# 初始化Node.js项目
npm init -y

# 创建目录结构
mkdir -p src/{tools,prompts,resources,utils} tests configs docs
touch src/index.ts
```

**✅ 验证检查点**：项目目录结构创建完成

## 1.3 依赖配置

### 1.3.1 Python依赖安装

**基础依赖（pyproject.toml）**
```toml
[project]
name = "my-mcp-server"
version = "0.1.0"
description = "My MCP Server"
authors = [{name = "Your Name", email = "<EMAIL>"}]
requires-python = ">=3.10"
dependencies = [
    "mcp[cli]>=1.2.0",
    "httpx>=0.25.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "ruff>=0.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
strict = true
```

**安装依赖**
```bash
# 安装生产依赖
uv add "mcp[cli]>=1.2.0" httpx pydantic python-dotenv

# 安装开发依赖
uv add --dev pytest pytest-asyncio black isort mypy ruff
```

### 1.3.2 TypeScript依赖配置

**package.json配置**
```json
{
  "name": "my-mcp-server",
  "version": "0.1.0",
  "description": "My MCP Server",
  "main": "dist/index.js",
  "scripts": {
    "build": "tsc",
    "dev": "tsx watch src/index.ts",
    "start": "node dist/index.js",
    "test": "jest",
    "lint": "eslint src/**/*.ts",
    "format": "prettier --write src/**/*.ts"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "axios": "^1.6.0",
    "zod": "^3.22.0",
    "dotenv": "^16.3.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.0.0",
    "jest": "^29.0.0",
    "@types/jest": "^29.0.0",
    "ts-jest": "^29.0.0",
    "tsx": "^4.0.0",
    "typescript": "^5.0.0",
    "prettier": "^3.0.0"
  }
}
```

**tsconfig.json配置**
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "commonjs",
    "lib": ["ES2022"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
```

**安装依赖**
```bash
# 安装生产依赖
npm install @modelcontextprotocol/sdk axios zod dotenv

# 安装开发依赖
npm install -D @types/node @typescript-eslint/eslint-plugin @typescript-eslint/parser eslint jest @types/jest ts-jest tsx typescript prettier
```

**✅ 验证检查点**：所有依赖安装成功，无版本冲突

## 1.4 基础配置文件

### 1.4.1 环境配置

**创建.env文件**
```bash
# 开发环境配置
NODE_ENV=development
LOG_LEVEL=debug
PORT=3000

# API配置
API_BASE_URL=https://api.example.com
API_KEY=your_api_key_here

# 数据库配置（如需要）
DATABASE_URL=sqlite:///./data.db

# MCP配置
MCP_SERVER_NAME=my-mcp-server
MCP_SERVER_VERSION=0.1.0
```

### 1.4.2 Git配置

**.gitignore文件**
```gitignore
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# 日志
logs/
*.log

# 临时文件
tmp/
temp/
```

**✅ 验证检查点**：配置文件创建完成，Git仓库初始化

## 1.5 开发环境验证

### 1.5.1 创建基础服务器文件

**Python基础服务器（src/server.py）**
```python
#!/usr/bin/env python3
"""
基础MCP Server实现
"""

import asyncio
import logging
from mcp.server.fastmcp import FastMCP

# 配置日志（重要：不要使用print，会干扰STDIO通信）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# 初始化FastMCP服务器
mcp = FastMCP("my-mcp-server")

@mcp.tool()
async def hello_world(name: str = "World") -> str:
    """
    简单的问候工具
    
    Args:
        name: 要问候的名字
    
    Returns:
        问候消息
    """
    logger.info(f"Hello tool called with name: {name}")
    return f"Hello, {name}! This is your MCP Server."

if __name__ == "__main__":
    logger.info("Starting MCP Server...")
    mcp.run(transport='stdio')
```

**TypeScript基础服务器（src/index.ts）**
```typescript
#!/usr/bin/env node
/**
 * 基础MCP Server实现
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

// 创建服务器实例
const server = new Server(
  {
    name: 'my-mcp-server',
    version: '0.1.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 注册工具列表处理器
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'hello_world',
        description: '简单的问候工具',
        inputSchema: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: '要问候的名字',
              default: 'World',
            },
          },
        },
      },
    ],
  };
});

// 注册工具调用处理器
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  if (name === 'hello_world') {
    const userName = (args as { name?: string }).name || 'World';
    console.error(`Hello tool called with name: ${userName}`); // 使用stderr记录日志
    
    return {
      content: [
        {
          type: 'text',
          text: `Hello, ${userName}! This is your MCP Server.`,
        },
      ],
    };
  }

  throw new Error(`Unknown tool: ${name}`);
});

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  console.error('Starting MCP Server...'); // 使用stderr记录日志
  await server.connect(transport);
}

main().catch((error) => {
  console.error('Server error:', error);
  process.exit(1);
});
```

### 1.5.2 测试基础功能

**Python测试**
```bash
# 运行服务器
uv run src/server.py

# 在另一个终端测试（需要MCP客户端）
echo '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}' | uv run src/server.py
```

**TypeScript测试**
```bash
# 编译TypeScript
npm run build

# 运行服务器
npm start

# 或直接运行开发模式
npm run dev
```

**✅ 验证检查点**：基础服务器启动成功，hello_world工具可用

## 1.6 常见问题解决

### 1.6.1 Python常见问题

**问题：ModuleNotFoundError**
```bash
# 解决方案：确保虚拟环境激活
source .venv/bin/activate
uv sync
```

**问题：STDIO通信错误**
```python
# 错误：使用print输出
print("Debug message")  # ❌

# 正确：使用logging
import logging
logger = logging.getLogger(__name__)
logger.info("Debug message")  # ✅
```

### 1.6.2 TypeScript常见问题

**问题：模块导入错误**
```bash
# 解决方案：检查tsconfig.json配置
npm run build
```

**问题：类型错误**
```typescript
// 确保安装类型定义
npm install -D @types/node
```

## 1.7 项目模板选择指南

### 1.7.1 模板复杂度级别

**简单级别（适合学习和原型）**
- 单文件实现
- 基础工具功能
- 最小依赖
- 快速启动（30分钟内）

**中等级别（适合小型项目）**
- 模块化结构
- 完整的三大功能模块
- 基础测试覆盖
- 配置管理

**复杂级别（适合生产环境）**
- 企业级架构
- 完整的错误处理
- 性能优化
- 安全最佳实践
- CI/CD集成

### 1.7.2 选择建议

**如果您是MCP新手**：从简单级别开始
**如果您有经验且需要快速交付**：选择中等级别
**如果您需要生产级应用**：使用复杂级别模板

**✅ 最终验证检查点**：
- [ ] 项目结构完整
- [ ] 依赖安装成功
- [ ] 基础服务器运行正常
- [ ] 开发环境配置完成
- [ ] Git仓库初始化
- [ ] 选择了合适的复杂度级别

---

**下一步**：继续阅读 [02-核心功能开发](./02-core-features.md) 开始实现MCP的三大核心功能模块。

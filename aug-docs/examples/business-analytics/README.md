# 商业数据分析 MCP Server

## 概述

这是一个完整的商业数据分析MCP Server实现，展示如何构建企业级的数据分析和报表生成系统。

## 功能特性

### 📊 核心功能
1. **销售数据分析** - 销售趋势、业绩统计、同比分析
2. **客户分析** - 客户画像、行为分析、价值评估
3. **产品分析** - 产品销量、库存分析、利润分析
4. **财务报表** - 收入报表、成本分析、利润报表
5. **预测分析** - 销售预测、趋势预测、风险评估

### 🛠️ 技术特性
- **数据处理**: Pandas + NumPy 高性能数据处理
- **可视化**: Matplotlib + Seaborn 专业图表生成
- **数据库**: SQLite 轻量级数据存储
- **缓存**: Redis 高性能缓存
- **异步**: 全异步架构，支持高并发
- **监控**: 完整的性能监控和日志记录

## 项目结构

```
business-analytics/
├── README.md                 # 项目说明
├── requirements.txt          # Python依赖
├── .env.example             # 环境变量模板
├── docker-compose.yml       # Docker配置
├── server.py                # 主服务器文件
├── src/                     # 源代码
│   ├── __init__.py
│   ├── tools/               # 工具实现
│   │   ├── __init__.py
│   │   ├── sales_analysis.py
│   │   ├── customer_analysis.py
│   │   ├── product_analysis.py
│   │   ├── financial_reports.py
│   │   └── prediction_tools.py
│   ├── prompts/             # 提示词实现
│   │   ├── __init__.py
│   │   ├── report_templates.py
│   │   └── analysis_prompts.py
│   ├── resources/           # 资源实现
│   │   ├── __init__.py
│   │   ├── data_manager.py
│   │   └── chart_generator.py
│   └── utils/               # 工具函数
│       ├── __init__.py
│       ├── database.py
│       ├── cache.py
│       ├── metrics.py
│       └── validators.py
├── configs/                 # 配置文件
│   ├── server-config.json
│   └── logging.conf
├── data/                    # 示例数据
│   ├── sample_sales.csv
│   ├── sample_customers.csv
│   └── sample_products.csv
├── tests/                   # 测试文件
│   ├── test_tools.py
│   ├── test_prompts.py
│   └── test_resources.py
├── docs/                    # 文档
│   ├── api.md
│   ├── deployment.md
│   └── user-guide.md
└── scripts/                 # 脚本文件
    ├── setup.sh
    ├── load_sample_data.py
    └── generate_reports.py
```

## 快速开始

### 1. 环境准备

**系统要求**
- Python 3.10+
- Redis Server (可选，用于缓存)
- 8GB+ RAM (推荐)

**安装依赖**
```bash
# 克隆项目
git clone <repository-url>
cd business-analytics

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

**.env 配置示例**
```env
# 数据库配置
DATABASE_URL=sqlite:///data/analytics.db

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/0
CACHE_ENABLED=true
CACHE_TTL=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/server.log

# 性能配置
MAX_WORKERS=4
CHUNK_SIZE=10000
CHART_DPI=300

# 安全配置
ALLOWED_PATHS=data,reports,charts
MAX_FILE_SIZE=100MB
```

### 3. 初始化数据

```bash
# 创建数据库和表
python scripts/setup.sh

# 加载示例数据
python scripts/load_sample_data.py

# 验证数据加载
python -c "
import sqlite3
conn = sqlite3.connect('data/analytics.db')
print('销售记录数:', conn.execute('SELECT COUNT(*) FROM sales').fetchone()[0])
print('客户记录数:', conn.execute('SELECT COUNT(*) FROM customers').fetchone()[0])
print('产品记录数:', conn.execute('SELECT COUNT(*) FROM products').fetchone()[0])
conn.close()
"
```

### 4. 启动服务器

```bash
# 直接启动
python server.py

# 或使用Docker
docker-compose up -d

# 检查服务状态
docker-compose ps
```

### 5. 配置Claude Desktop

在Claude Desktop配置文件中添加：

```json
{
  "mcpServers": {
    "business-analytics": {
      "command": "python",
      "args": ["/path/to/business-analytics/server.py"],
      "cwd": "/path/to/business-analytics",
      "env": {
        "DATABASE_URL": "sqlite:///data/analytics.db",
        "LOG_LEVEL": "INFO"
      }
    }
  }
}
```

## 功能演示

### 📈 销售数据分析

```
# 查看销售趋势
请分析最近3个月的销售趋势

# 业绩对比
对比今年和去年同期的销售业绩

# 区域分析
分析各个地区的销售表现
```

**示例输出**：
```
📊 销售趋势分析报告

📅 时间范围: 2024-01-01 至 2024-03-31

💰 关键指标:
• 总销售额: ¥2,450,000 (+15.3% 同比)
• 订单数量: 1,250 (+8.7% 同比)
• 平均订单价值: ¥1,960 (+6.1% 同比)

📈 月度趋势:
• 1月: ¥750,000 (基准月)
• 2月: ¥820,000 (+9.3% 环比)
• 3月: ¥880,000 (+7.3% 环比)

🏆 最佳表现:
• 最佳产品: 智能手机 (¥680,000)
• 最佳地区: 华东地区 (¥980,000)
• 最佳销售: 张三 (¥150,000)

[图表已生成: charts/sales_trend_2024Q1.png]
```

### 👥 客户分析

```
# 客户画像分析
分析我们的核心客户群体特征

# 客户价值评估
评估客户的生命周期价值

# 流失风险预警
识别有流失风险的客户
```

### 📦 产品分析

```
# 产品销量排行
显示最受欢迎的产品排行榜

# 库存分析
分析当前库存状况和补货建议

# 利润分析
分析各产品线的利润贡献
```

### 💼 财务报表

```
# 生成月度财务报表
生成2024年3月的财务报表

# 成本分析
分析各项成本的构成和变化

# 利润预测
预测下季度的利润情况
```

## 高级功能

### 🤖 智能分析

**自动异常检测**
```python
# 系统会自动检测以下异常:
- 销售额突然下降超过20%
- 客户投诉率异常增高
- 库存周转率异常
- 成本波动超出正常范围
```

**趋势预测**
```python
# 基于历史数据进行预测:
- 未来3个月销售预测
- 季节性趋势分析
- 市场需求预测
- 风险评估
```

### 📊 自定义报表

**报表模板**
```python
# 支持多种报表格式:
- Excel格式 (.xlsx)
- PDF格式 (.pdf)
- 图片格式 (.png, .jpg)
- 网页格式 (.html)
```

**可视化图表**
```python
# 丰富的图表类型:
- 折线图 (趋势分析)
- 柱状图 (对比分析)
- 饼图 (占比分析)
- 散点图 (相关性分析)
- 热力图 (分布分析)
```

## 性能优化

### 🚀 缓存策略

```python
# 多层缓存架构:
1. 内存缓存 - 热点数据 (LRU, 1GB)
2. Redis缓存 - 计算结果 (TTL: 1小时)
3. 文件缓存 - 图表文件 (TTL: 24小时)
```

### ⚡ 并发处理

```python
# 异步处理能力:
- 支持100+并发请求
- 数据分析任务队列
- 后台报表生成
- 实时数据更新
```

### 📈 性能监控

```python
# 关键性能指标:
- 请求响应时间 < 2秒
- 数据处理吞吐量 > 10MB/s
- 内存使用率 < 80%
- CPU使用率 < 70%
```

## 部署指南

### 🐳 Docker部署

```bash
# 构建镜像
docker build -t business-analytics:latest .

# 运行容器
docker run -d \
  --name business-analytics \
  -p 3000:3000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  business-analytics:latest
```

### ☸️ Kubernetes部署

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: business-analytics
spec:
  replicas: 3
  selector:
    matchLabels:
      app: business-analytics
  template:
    metadata:
      labels:
        app: business-analytics
    spec:
      containers:
      - name: business-analytics
        image: business-analytics:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          value: "************************************/analytics"
        - name: REDIS_URL
          value: "redis://redis:6379/0"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
```

## 故障排查

### 🔍 常见问题

**1. 数据加载失败**
```bash
# 检查数据文件
ls -la data/
# 检查数据库连接
python -c "import sqlite3; sqlite3.connect('data/analytics.db')"
```

**2. 图表生成失败**
```bash
# 检查matplotlib后端
python -c "import matplotlib; print(matplotlib.get_backend())"
# 安装字体支持
sudo apt-get install fonts-dejavu-core
```

**3. 性能问题**
```bash
# 检查内存使用
free -h
# 检查Redis状态
redis-cli ping
# 查看日志
tail -f logs/server.log
```

### 📊 监控面板

访问 `http://localhost:3001` 查看Grafana监控面板：
- 系统资源使用情况
- 请求响应时间分布
- 错误率统计
- 缓存命中率

## 扩展开发

### 🔧 添加新的分析工具

```python
# src/tools/custom_analysis.py
from src.utils.database import get_db_connection
from src.utils.cache import cache_result

@cache_result(ttl=3600)
async def custom_analysis_tool(
    metric: str,
    start_date: str,
    end_date: str,
    filters: dict = None
) -> str:
    """
    自定义分析工具
    
    Args:
        metric: 分析指标
        start_date: 开始日期
        end_date: 结束日期
        filters: 过滤条件
    
    Returns:
        分析结果
    """
    # 实现您的分析逻辑
    pass
```

### 📝 添加新的报表模板

```python
# src/prompts/custom_reports.py
CUSTOM_REPORT_TEMPLATE = """
根据以下数据生成{report_type}报表:

数据摘要:
{data_summary}

请生成包含以下内容的报表:
1. 执行摘要
2. 关键发现
3. 数据分析
4. 建议措施
5. 风险评估

报表格式: {format}
目标受众: {audience}
"""
```

## 技术支持

### 📞 获取帮助
- **文档**: [docs/](./docs/) 目录
- **API参考**: [docs/api.md](./docs/api.md)
- **部署指南**: [docs/deployment.md](./docs/deployment.md)
- **用户手册**: [docs/user-guide.md](./docs/user-guide.md)

### 🐛 问题反馈
如果遇到问题，请提供以下信息：
1. 错误日志 (`logs/server.log`)
2. 系统环境 (`python --version`, `pip list`)
3. 配置文件 (`.env`, `configs/server-config.json`)
4. 重现步骤

---

**开始您的商业数据分析之旅！** 📊

这个示例展示了如何构建一个完整的企业级MCP Server，您可以基于此模板开发适合您业务需求的分析系统。

# MCP协议规范文档

## 📋 协议概述

Model Context Protocol (MCP) 是一个开放协议，用于标准化应用程序如何为大型语言模型(LLM)提供上下文。

### 核心概念
- **Server** - 提供上下文的应用程序
- **Client** - 消费上下文的应用程序（如Claude Desktop）
- **Transport** - 通信传输层（STDIO、HTTP、WebSocket）
- **Capabilities** - 服务器支持的功能集合

## 🔧 协议架构

### 三大核心功能模块

#### 1. Tools（工具）
```json
{
  "method": "tools/call",
  "params": {
    "name": "tool_name",
    "arguments": {
      "param1": "value1",
      "param2": "value2"
    }
  }
}
```

**工具定义结构：**
```python
@mcp.tool()
async def tool_name(param1: str, param2: int) -> dict:
    """工具描述"""
    # 工具实现
    return {"result": "success"}
```

#### 2. Prompts（提示词模板）
```json
{
  "method": "prompts/get",
  "params": {
    "name": "prompt_name",
    "arguments": {
      "variable1": "value1"
    }
  }
}
```

**提示词定义结构：**
```python
@mcp.prompt("prompt_name", "提示词描述")
async def prompt_handler(**kwargs) -> str:
    return f"处理后的提示词内容: {kwargs}"
```

#### 3. Resources（资源）
```json
{
  "method": "resources/read",
  "params": {
    "uri": "file://path/to/resource"
  }
}
```

**资源定义结构：**
```python
@mcp.resource("file://documents/{path}")
async def get_document(path: str) -> str:
    """获取文档资源"""
    return await read_file(path)
```

## 📡 通信协议

### JSON-RPC 2.0 基础
MCP基于JSON-RPC 2.0协议进行通信：

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "method_name",
  "params": {
    "param1": "value1"
  }
}
```

### 标准响应格式
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "data": "response_data"
  }
}
```

### 错误响应格式
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32600,
    "message": "Invalid Request",
    "data": "Additional error information"
  }
}
```

## 🚀 服务器生命周期

### 1. 初始化阶段
```python
# 服务器启动
mcp = FastMCP("server-name")

# 注册功能
@mcp.tool()
async def my_tool():
    pass

# 启动服务器
await mcp.run()
```

### 2. 能力协商
```json
{
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "tools": {},
      "prompts": {},
      "resources": {}
    },
    "clientInfo": {
      "name": "client-name",
      "version": "1.0.0"
    }
  }
}
```

### 3. 功能调用
客户端可以调用服务器注册的工具、提示词和资源。

### 4. 关闭阶段
```json
{
  "method": "notifications/cancelled",
  "params": {}
}
```

## 📊 数据类型规范

### 基础类型
```typescript
type ToolResult = {
  content: Array<TextContent | ImageContent>;
  isError?: boolean;
};

type TextContent = {
  type: "text";
  text: string;
};

type ImageContent = {
  type: "image";
  data: string;  // base64编码
  mimeType: string;
};
```

### 工具参数类型
```python
# 支持的参数类型
str     # 字符串
int     # 整数
float   # 浮点数
bool    # 布尔值
list    # 列表
dict    # 字典
Optional[T]  # 可选类型
```

### 资源URI格式
```
file://path/to/file
http://example.com/api/resource
custom://scheme/resource
```

## 🔒 安全规范

### 输入验证
```python
def validate_input(data: dict) -> bool:
    """验证输入数据"""
    # 检查必需字段
    required_fields = ["name", "arguments"]
    for field in required_fields:
        if field not in data:
            return False
    
    # 验证数据类型
    if not isinstance(data["arguments"], dict):
        return False
    
    return True
```

### 路径安全
```python
import os
from pathlib import Path

def validate_file_path(file_path: str) -> bool:
    """验证文件路径安全性"""
    try:
        # 解析路径
        path = Path(file_path).resolve()
        
        # 检查路径遍历攻击
        if ".." in str(path):
            return False
        
        # 检查允许的目录
        allowed_dirs = ["/documents", "/uploads"]
        if not any(str(path).startswith(dir) for dir in allowed_dirs):
            return False
        
        return True
    except Exception:
        return False
```

### 错误处理
```python
try:
    result = await process_request(request)
    return {"result": result}
except ValidationError as e:
    return {
        "error": {
            "code": -32602,
            "message": "Invalid params",
            "data": str(e)
        }
    }
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    return {
        "error": {
            "code": -32603,
            "message": "Internal error"
        }
    }
```

## 📈 性能优化

### 异步处理
```python
import asyncio

@mcp.tool()
async def async_tool(data: str) -> dict:
    """异步工具实现"""
    # 使用异步操作
    result = await async_process(data)
    return {"result": result}
```

### 缓存策略
```python
from functools import lru_cache
import aioredis

# 内存缓存
@lru_cache(maxsize=128)
def cached_function(key: str):
    return expensive_operation(key)

# Redis缓存
async def get_cached_data(key: str):
    redis = await aioredis.from_url("redis://localhost")
    cached = await redis.get(key)
    if cached:
        return json.loads(cached)
    
    data = await fetch_data(key)
    await redis.setex(key, 3600, json.dumps(data))
    return data
```

## 🔍 调试和监控

### 日志记录
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

@mcp.tool()
async def logged_tool(param: str) -> dict:
    logger.info(f"Tool called with param: {param}")
    try:
        result = await process(param)
        logger.info(f"Tool completed successfully")
        return result
    except Exception as e:
        logger.error(f"Tool failed: {e}")
        raise
```

### 健康检查
```python
@mcp.tool()
async def health_check() -> dict:
    """服务器健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }
```

---

**这是MCP协议的核心规范，AI助手必须严格遵循这些标准来生成符合协议的代码！** 📋✅

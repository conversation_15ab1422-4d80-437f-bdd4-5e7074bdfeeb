# MCP Server API接口文档库

## 📚 文档库说明

本目录包含MCP Server开发所需的所有API接口文档、技术规范和参考资料。这些文档为AI助手提供准确的技术信息，确保生成的代码符合最新的协议标准。

## 📁 文档结构

### 🔧 核心协议文档
```
api-docs/
├── mcp-protocol/              # MCP协议核心文档
│   ├── specification.md       # MCP协议规范
│   ├── transport.md           # 传输层协议
│   ├── json-rpc.md           # JSON-RPC实现
│   └── security.md           # 安全规范
├── sdk-reference/            # SDK参考文档
│   ├── fastmcp/              # FastMCP Python SDK
│   ├── mcp-sdk-js/           # JavaScript/TypeScript SDK
│   └── comparison.md         # SDK对比选择
├── api-reference/            # API接口参考
│   ├── tools-api.md          # Tools API规范
│   ├── prompts-api.md        # Prompts API规范
│   ├── resources-api.md      # Resources API规范
│   └── server-api.md         # Server API规范
├── integration/              # 集成接口文档
│   ├── claude-desktop.md     # Claude Desktop集成
│   ├── vscode.md            # VS Code集成
│   └── custom-clients.md    # 自定义客户端集成
├── external-apis/           # 外部API文档
│   ├── openai.md            # OpenAI API
│   ├── anthropic.md         # Anthropic API
│   ├── huggingface.md       # HuggingFace API
│   └── common-services.md   # 常用服务API
└── schemas/                 # 数据模式定义
    ├── request-schemas.json  # 请求数据模式
    ├── response-schemas.json # 响应数据模式
    └── config-schemas.json  # 配置数据模式
```

## 🎯 AI助手使用指南

### 文档调用策略
```python
def get_api_documentation(development_context):
    """根据开发上下文获取相应的API文档"""
    
    docs = {}
    
    # 1. 核心协议文档（必须参考）
    docs["mcp_spec"] = load_api_doc("mcp-protocol/specification.md")
    docs["transport"] = load_api_doc("mcp-protocol/transport.md")
    
    # 2. SDK选择和使用
    if development_context.language == "python":
        docs["sdk"] = load_api_doc("sdk-reference/fastmcp/")
    elif development_context.language == "typescript":
        docs["sdk"] = load_api_doc("sdk-reference/mcp-sdk-js/")
    
    # 3. API接口规范
    if "tools" in development_context.features:
        docs["tools_api"] = load_api_doc("api-reference/tools-api.md")
    if "prompts" in development_context.features:
        docs["prompts_api"] = load_api_doc("api-reference/prompts-api.md")
    if "resources" in development_context.features:
        docs["resources_api"] = load_api_doc("api-reference/resources-api.md")
    
    # 4. 集成配置
    docs["claude_integration"] = load_api_doc("integration/claude-desktop.md")
    
    # 5. 外部API（根据需求）
    if "openai" in development_context.external_services:
        docs["openai_api"] = load_api_doc("external-apis/openai.md")
    if "anthropic" in development_context.external_services:
        docs["anthropic_api"] = load_api_doc("external-apis/anthropic.md")
    
    # 6. 数据模式验证
    docs["schemas"] = load_api_doc("schemas/")
    
    return docs
```

### 使用优先级
1. **🔴 必须参考** - MCP协议规范和传输层文档
2. **🟡 重要参考** - 对应语言的SDK文档和API规范
3. **🟢 按需参考** - 集成配置和外部API文档
4. **🔵 验证参考** - 数据模式和安全规范

## 📋 文档维护标准

### 文档质量要求
- ✅ **准确性** - 与最新版本协议保持同步
- ✅ **完整性** - 包含完整的API签名和示例
- ✅ **实用性** - 提供可直接使用的代码示例
- ✅ **时效性** - 定期更新以反映最新变化

### 更新频率
- **MCP协议文档** - 跟随官方版本更新
- **SDK文档** - 跟随SDK版本更新
- **外部API文档** - 月度检查更新
- **集成文档** - 季度检查更新

## 🔗 与AI助手提示词系统的关系

### 文档分工
```
aug-docs/                    # AI助手提示词系统
├── AI工作流程指导           # 如何帮助用户
├── 代码模板库              # 可生成的代码
├── 实战案例库              # 完整实现示例
├── 最佳实践库              # 开发规范指导
└── api-docs/               # 技术参考文档库
    ├── 协议规范            # 准确的技术标准
    ├── API接口文档         # 详细的接口说明
    ├── SDK使用指南         # 具体的SDK用法
    └── 集成配置文档        # 配置参数说明
```

### 协同使用方式
1. **AI助手提示词** 告诉AI如何工作
2. **API技术文档** 提供准确的技术细节
3. **两者结合** 确保AI生成正确且高质量的代码

## 🚀 快速开始

### AI助手开发MCP Server时的文档调用顺序：

1. **理解需求** → 参考 `../AI-ASSISTANT-GUIDE.md`
2. **选择方案** → 参考 `../templates/README.md`
3. **查阅协议** → 参考 `mcp-protocol/specification.md`
4. **选择SDK** → 参考 `sdk-reference/comparison.md`
5. **实现功能** → 参考 `api-reference/` 对应API文档
6. **配置集成** → 参考 `integration/claude-desktop.md`
7. **验证代码** → 参考 `schemas/` 数据模式
8. **应用最佳实践** → 参考 `../best-practices/README.md`

---

**这个API文档库为AI助手提供准确的技术参考，确保生成的MCP Server代码符合最新标准！** 📚🎯

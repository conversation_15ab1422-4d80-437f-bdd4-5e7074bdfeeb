# FastMCP Python SDK 参考文档

## 📚 SDK概述

FastMCP是Python生态系统中最流行的MCP Server开发框架，提供了简洁的API和强大的功能。

### 核心特性
- 🚀 **快速开发** - 装饰器风格的简洁API
- 🔧 **完整功能** - 支持Tools、Prompts、Resources
- 🛡️ **类型安全** - 完整的类型注解支持
- ⚡ **高性能** - 基于asyncio的异步架构
- 🔒 **安全可靠** - 内置安全验证机制

## 🛠️ 安装和导入

### 安装
```bash
# 基础安装
pip install "mcp[cli]"

# 完整安装（包含所有依赖）
pip install "mcp[cli,dev,test]"

# 特定版本
pip install "mcp[cli]==1.2.0"
```

### 基础导入
```python
from mcp.server.fastmcp import FastMCP
from mcp.server.models import InitializationOptions
import asyncio
import logging
```

## 🏗️ 基础服务器结构

### 最小服务器
```python
#!/usr/bin/env python3
from mcp.server.fastmcp import FastMCP

# 创建服务器实例
mcp = FastMCP("my-server")

@mcp.tool()
async def hello_world(name: str) -> str:
    """简单的问候工具"""
    return f"Hello, {name}!"

# 启动服务器
async def main():
    await mcp.run()

if __name__ == "__main__":
    asyncio.run(main())
```

### 完整服务器模板
```python
#!/usr/bin/env python3
"""
完整的MCP Server模板
包含错误处理、日志记录、配置管理
"""

import asyncio
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any

from mcp.server.fastmcp import FastMCP
from mcp.server.models import InitializationOptions

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建服务器实例
mcp = FastMCP("production-server")

# 工具实现
@mcp.tool()
async def process_data(
    data: str,
    format_type: str = "json",
    validate: bool = True
) -> Dict[str, Any]:
    """
    处理数据的工具
    
    Args:
        data: 输入数据
        format_type: 数据格式 (json/xml/csv)
        validate: 是否验证数据
    
    Returns:
        处理结果字典
    """
    try:
        logger.info(f"Processing data with format: {format_type}")
        
        # 数据验证
        if validate and not data.strip():
            raise ValueError("数据不能为空")
        
        # 数据处理逻辑
        result = {
            "status": "success",
            "processed_data": data.upper(),
            "format": format_type,
            "length": len(data)
        }
        
        logger.info("Data processing completed successfully")
        return result
        
    except Exception as e:
        logger.error(f"Data processing failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

# 提示词模板
@mcp.prompt("analysis_prompt", "数据分析提示词模板")
async def analysis_prompt(
    data_type: str,
    analysis_goal: str = "general"
) -> str:
    """生成数据分析提示词"""
    
    template = f"""
请分析以下{data_type}数据：

分析目标：{analysis_goal}

请提供：
1. 数据概览
2. 关键指标
3. 趋势分析
4. 建议措施

数据内容：
{{data}}
"""
    return template.strip()

# 资源访问
@mcp.resource("file://data/{path}")
async def get_data_file(path: str) -> str:
    """获取数据文件资源"""
    try:
        file_path = Path("data") / path
        
        # 安全检查
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {path}")
        
        if file_path.suffix not in ['.txt', '.json', '.csv']:
            raise ValueError(f"不支持的文件类型: {file_path.suffix}")
        
        # 读取文件
        content = file_path.read_text(encoding='utf-8')
        logger.info(f"Successfully read file: {path}")
        
        return content
        
    except Exception as e:
        logger.error(f"Failed to read file {path}: {e}")
        raise

# 启动函数
async def main():
    """启动服务器"""
    try:
        logger.info("Starting MCP Server...")
        await mcp.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed to start: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔧 API参考

### FastMCP类
```python
class FastMCP:
    def __init__(
        self,
        name: str,
        version: str = "1.0.0"
    ):
        """
        初始化MCP服务器
        
        Args:
            name: 服务器名称
            version: 服务器版本
        """
```

### 装饰器API

#### @mcp.tool()
```python
@mcp.tool()
async def tool_name(
    param1: str,
    param2: int = 10,
    param3: Optional[bool] = None
) -> Dict[str, Any]:
    """
    工具装饰器
    
    支持的参数类型：
    - str, int, float, bool
    - List[T], Dict[str, T]
    - Optional[T]
    - Union[T1, T2]
    """
    pass
```

#### @mcp.prompt()
```python
@mcp.prompt("prompt_name", "提示词描述")
async def prompt_handler(**kwargs) -> str:
    """
    提示词装饰器
    
    Args:
        **kwargs: 动态参数
    
    Returns:
        str: 格式化的提示词
    """
    pass
```

#### @mcp.resource()
```python
@mcp.resource("scheme://pattern/{variable}")
async def resource_handler(variable: str) -> str:
    """
    资源装饰器
    
    支持的URI模式：
    - file://path/{variable}
    - http://domain/{path}
    - custom://scheme/{resource}
    """
    pass
```

## 📊 数据类型和验证

### 支持的类型
```python
from typing import List, Dict, Optional, Union
from pydantic import BaseModel

# 基础类型
str_param: str
int_param: int
float_param: float
bool_param: bool

# 复合类型
list_param: List[str]
dict_param: Dict[str, Any]
optional_param: Optional[str]
union_param: Union[str, int]

# Pydantic模型
class DataModel(BaseModel):
    name: str
    value: int
    tags: List[str] = []

@mcp.tool()
async def typed_tool(data: DataModel) -> Dict[str, Any]:
    """使用Pydantic模型的工具"""
    return {
        "name": data.name,
        "value": data.value,
        "tag_count": len(data.tags)
    }
```

### 输入验证
```python
from pydantic import validator

class ValidatedInput(BaseModel):
    email: str
    age: int
    
    @validator('email')
    def validate_email(cls, v):
        if '@' not in v:
            raise ValueError('无效的邮箱地址')
        return v
    
    @validator('age')
    def validate_age(cls, v):
        if v < 0 or v > 150:
            raise ValueError('年龄必须在0-150之间')
        return v
```

## 🔒 安全和错误处理

### 异常处理
```python
from mcp.server.exceptions import McpError

@mcp.tool()
async def safe_tool(data: str) -> Dict[str, Any]:
    """安全的工具实现"""
    try:
        # 输入验证
        if not data or len(data) > 1000:
            raise McpError("数据长度必须在1-1000字符之间")
        
        # 业务逻辑
        result = await process_data(data)
        return {"status": "success", "result": result}
        
    except McpError:
        # MCP特定错误，直接抛出
        raise
    except ValueError as e:
        # 转换为MCP错误
        raise McpError(f"数据验证失败: {e}")
    except Exception as e:
        # 记录日志并抛出通用错误
        logger.error(f"工具执行失败: {e}")
        raise McpError("内部服务器错误")
```

### 安全配置
```python
import os
from pathlib import Path

class SecurityConfig:
    """安全配置类"""
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {'.txt', '.json', '.csv', '.md'}
    
    # 允许的目录
    ALLOWED_DIRS = [
        Path("/documents"),
        Path("/uploads"),
        Path("/data")
    ]
    
    # 最大文件大小 (字节)
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    @classmethod
    def validate_file_access(cls, file_path: str) -> bool:
        """验证文件访问权限"""
        try:
            path = Path(file_path).resolve()
            
            # 检查路径遍历
            if '..' in str(path):
                return False
            
            # 检查允许的目录
            if not any(str(path).startswith(str(dir)) for dir in cls.ALLOWED_DIRS):
                return False
            
            # 检查文件扩展名
            if path.suffix not in cls.ALLOWED_EXTENSIONS:
                return False
            
            # 检查文件大小
            if path.exists() and path.stat().st_size > cls.MAX_FILE_SIZE:
                return False
            
            return True
            
        except Exception:
            return False
```

## 🚀 性能优化

### 异步最佳实践
```python
import aiohttp
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 异步HTTP请求
async def fetch_data(url: str) -> dict:
    """异步获取数据"""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.json()

# CPU密集型任务
executor = ThreadPoolExecutor(max_workers=4)

async def cpu_intensive_task(data: str) -> str:
    """CPU密集型任务"""
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(
        executor, 
        process_heavy_computation, 
        data
    )
    return result

# 批量处理
async def batch_process(items: List[str]) -> List[dict]:
    """批量处理数据"""
    tasks = [process_item(item) for item in items]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理异常
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Item {i} failed: {result}")
            processed_results.append({"error": str(result)})
        else:
            processed_results.append(result)
    
    return processed_results
```

---

**FastMCP SDK提供了强大而简洁的API，让AI助手能够快速生成高质量的MCP Server代码！** 🚀📚

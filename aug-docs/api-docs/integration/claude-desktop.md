# Claude Desktop 集成配置文档

## 📋 集成概述

Claude Desktop是Anthropic官方的桌面客户端，支持通过MCP协议集成自定义服务器。本文档提供完整的集成配置指南。

## 📁 配置文件位置

### macOS
```bash
~/Library/Application Support/Claude/claude_desktop_config.json
```

### Windows
```bash
%APPDATA%/Claude/claude_desktop_config.json
```

### Linux
```bash
~/.config/<PERSON>/claude_desktop_config.json
```

## ⚙️ 基础配置格式

### 最小配置
```json
{
  "mcpServers": {
    "my-server": {
      "command": "python",
      "args": ["/path/to/server.py"]
    }
  }
}
```

### 完整配置
```json
{
  "mcpServers": {
    "my-server": {
      "command": "python",
      "args": ["/absolute/path/to/server.py"],
      "cwd": "/absolute/path/to/working/directory",
      "env": {
        "LOG_LEVEL": "INFO",
        "API_KEY": "your-api-key",
        "DATABASE_URL": "sqlite:///data.db"
      }
    }
  }
}
```

## 🔧 配置参数详解

### 必需参数

#### command
```json
{
  "command": "python"
}
```
- **类型**: string
- **说明**: 执行命令，通常是Python解释器路径
- **示例**: 
  - `"python"` - 使用系统默认Python
  - `"/usr/bin/python3"` - 使用特定Python版本
  - `"C:\\Python39\\python.exe"` - Windows绝对路径

#### args
```json
{
  "args": ["/path/to/server.py"]
}
```
- **类型**: array of strings
- **说明**: 命令行参数，第一个通常是服务器脚本路径
- **注意**: 必须使用绝对路径

### 可选参数

#### cwd
```json
{
  "cwd": "/path/to/working/directory"
}
```
- **类型**: string
- **说明**: 工作目录，服务器运行时的当前目录
- **用途**: 相对路径解析、配置文件查找

#### env
```json
{
  "env": {
    "LOG_LEVEL": "INFO",
    "API_KEY": "secret-key",
    "PYTHONPATH": "/custom/path"
  }
}
```
- **类型**: object
- **说明**: 环境变量设置
- **常用变量**:
  - `LOG_LEVEL`: 日志级别
  - `API_KEY`: API密钥
  - `DATABASE_URL`: 数据库连接
  - `PYTHONPATH`: Python模块路径

## 📝 配置示例

### Python项目配置
```json
{
  "mcpServers": {
    "document-analyzer": {
      "command": "python",
      "args": ["/Users/<USER>/projects/mcp-server/server.py"],
      "cwd": "/Users/<USER>/projects/mcp-server",
      "env": {
        "LOG_LEVEL": "INFO",
        "OPENAI_API_KEY": "sk-...",
        "MAX_FILE_SIZE": "10MB"
      }
    }
  }
}
```

### 虚拟环境配置
```json
{
  "mcpServers": {
    "my-server": {
      "command": "/Users/<USER>/projects/venv/bin/python",
      "args": ["/Users/<USER>/projects/server.py"],
      "cwd": "/Users/<USER>/projects",
      "env": {
        "VIRTUAL_ENV": "/Users/<USER>/projects/venv"
      }
    }
  }
}
```

### 多服务器配置
```json
{
  "mcpServers": {
    "data-analyzer": {
      "command": "python",
      "args": ["/path/to/data-analyzer/server.py"],
      "cwd": "/path/to/data-analyzer",
      "env": {
        "LOG_LEVEL": "INFO"
      }
    },
    "file-processor": {
      "command": "python",
      "args": ["/path/to/file-processor/server.py"],
      "cwd": "/path/to/file-processor",
      "env": {
        "LOG_LEVEL": "DEBUG",
        "TEMP_DIR": "/tmp/processor"
      }
    },
    "api-gateway": {
      "command": "node",
      "args": ["/path/to/api-gateway/server.js"],
      "cwd": "/path/to/api-gateway",
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

## 🔍 故障排查

### 常见问题和解决方案

#### 1. 服务器无法启动
```json
// 错误配置
{
  "command": "python",
  "args": ["./server.py"]  // ❌ 相对路径
}

// 正确配置
{
  "command": "python",
  "args": ["/absolute/path/to/server.py"]  // ✅ 绝对路径
}
```

#### 2. Python模块找不到
```json
{
  "command": "python",
  "args": ["/path/to/server.py"],
  "env": {
    "PYTHONPATH": "/path/to/project:/path/to/dependencies"
  }
}
```

#### 3. 权限问题
```bash
# 确保脚本有执行权限
chmod +x /path/to/server.py

# 确保Python可执行
which python
```

#### 4. 环境变量问题
```json
{
  "env": {
    "PATH": "/usr/local/bin:/usr/bin:/bin",
    "HOME": "/Users/<USER>",
    "LOG_LEVEL": "DEBUG"
  }
}
```

### 调试技巧

#### 启用详细日志
```json
{
  "env": {
    "LOG_LEVEL": "DEBUG",
    "MCP_DEBUG": "1"
  }
}
```

#### 测试配置
```bash
# 手动测试服务器
cd /path/to/working/directory
python /path/to/server.py

# 检查环境变量
env | grep LOG_LEVEL
```

## 🔄 配置更新流程

### 1. 编辑配置文件
```bash
# macOS
open ~/Library/Application\ Support/Claude/claude_desktop_config.json

# Windows
notepad %APPDATA%\Claude\claude_desktop_config.json

# Linux
nano ~/.config/Claude/claude_desktop_config.json
```

### 2. 验证JSON格式
```bash
# 使用jq验证JSON格式
cat claude_desktop_config.json | jq .

# 或使用Python验证
python -m json.tool claude_desktop_config.json
```

### 3. 重启Claude Desktop
- 完全退出Claude Desktop应用
- 重新启动应用
- 配置会自动加载

### 4. 验证集成
在Claude Desktop中测试MCP服务器功能：
```
请帮我测试MCP服务器的连接状态
```

## 📊 性能优化配置

### 资源限制
```json
{
  "env": {
    "MAX_MEMORY": "512MB",
    "MAX_CPU_PERCENT": "50",
    "TIMEOUT_SECONDS": "30"
  }
}
```

### 缓存配置
```json
{
  "env": {
    "CACHE_ENABLED": "true",
    "CACHE_TTL": "3600",
    "CACHE_SIZE": "100MB"
  }
}
```

### 并发控制
```json
{
  "env": {
    "MAX_CONCURRENT_REQUESTS": "10",
    "WORKER_THREADS": "4"
  }
}
```

## 🔒 安全配置

### 敏感信息处理
```json
{
  "env": {
    "API_KEY_FILE": "/secure/path/to/api_key.txt",
    "SSL_CERT_PATH": "/path/to/cert.pem",
    "ALLOWED_HOSTS": "localhost,127.0.0.1"
  }
}
```

### 访问控制
```json
{
  "env": {
    "ALLOWED_PATHS": "/documents,/uploads",
    "DENIED_PATHS": "/system,/private",
    "MAX_FILE_SIZE": "10MB"
  }
}
```

---

**正确的Claude Desktop配置是MCP Server成功运行的关键！请确保使用绝对路径和正确的环境变量。** 🔧✅

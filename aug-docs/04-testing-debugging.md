# 04 - 测试调试流程

## 概述

本指南介绍MCP Server的测试策略、调试工具使用和常见问题排查方法。

## 4.1 单元测试

### 4.1.1 Python测试框架设置

**测试配置（pytest.ini）**
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=src
    --cov-report=html
    --cov-report=term-missing
asyncio_mode = auto
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
```

**工具测试示例（tests/test_tools.py）**
```python
import pytest
import asyncio
from unittest.mock import AsyncMock, patch
from src.tools.example_tool import calculator_tool, read_file_tool

class TestCalculatorTool:
    """计算器工具测试"""
    
    @pytest.mark.asyncio
    async def test_addition(self):
        """测试加法运算"""
        result = await calculator_tool("add", 5, 3)
        assert "8" in result
        assert "5 add 3 = 8" in result
    
    @pytest.mark.asyncio
    async def test_division_by_zero(self):
        """测试除零错误"""
        with pytest.raises(ValueError, match="除数不能为零"):
            await calculator_tool("divide", 10, 0)
    
    @pytest.mark.asyncio
    async def test_invalid_operation(self):
        """测试无效运算类型"""
        with pytest.raises(ValueError, match="无效的运算类型"):
            await calculator_tool("invalid", 5, 3)
    
    @pytest.mark.parametrize("operation,a,b,expected", [
        ("add", 1, 2, "3"),
        ("subtract", 5, 3, "2"),
        ("multiply", 4, 3, "12"),
        ("divide", 8, 2, "4"),
    ])
    @pytest.mark.asyncio
    async def test_all_operations(self, operation, a, b, expected):
        """参数化测试所有运算"""
        result = await calculator_tool(operation, a, b)
        assert expected in result

class TestFileReadTool:
    """文件读取工具测试"""
    
    @pytest.mark.asyncio
    async def test_read_existing_file(self, tmp_path):
        """测试读取存在的文件"""
        # 创建临时文件
        test_file = tmp_path / "test.txt"
        test_content = "Hello, World!"
        test_file.write_text(test_content, encoding="utf-8")
        
        # 测试读取
        result = await read_file_tool(str(test_file))
        assert result == test_content
    
    @pytest.mark.asyncio
    async def test_read_nonexistent_file(self):
        """测试读取不存在的文件"""
        with pytest.raises(FileNotFoundError):
            await read_file_tool("nonexistent.txt")
    
    @pytest.mark.asyncio
    async def test_unsafe_path(self):
        """测试不安全的路径"""
        with pytest.raises(ValueError, match="不安全的文件路径"):
            await read_file_tool("../../../etc/passwd")
    
    @pytest.mark.asyncio
    @patch('aiofiles.open')
    async def test_file_read_error(self, mock_open):
        """测试文件读取错误"""
        mock_open.side_effect = PermissionError("权限拒绝")
        
        with pytest.raises(PermissionError):
            await read_file_tool("test.txt")

# 测试夹具
@pytest.fixture
async def mock_mcp_server():
    """模拟MCP服务器"""
    from mcp.server.fastmcp import FastMCP
    server = FastMCP("test-server")
    return server

@pytest.fixture
def sample_config():
    """示例配置"""
    return {
        "server": {"name": "test-server"},
        "capabilities": {"tools": {"enabled": True}},
        "security": {"allowedPaths": ["./test_data"]}
    }
```

**运行测试**
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_tools.py

# 运行特定测试类
pytest tests/test_tools.py::TestCalculatorTool

# 运行特定测试方法
pytest tests/test_tools.py::TestCalculatorTool::test_addition

# 运行带标记的测试
pytest -m unit

# 生成覆盖率报告
pytest --cov=src --cov-report=html
```

### 4.1.2 TypeScript测试框架设置

**Jest配置（jest.config.js）**
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testTimeout: 10000,
};
```

**工具测试示例（tests/tools.test.ts）**
```typescript
import { calculatorTool, readFileTool } from '../src/tools/exampleTool';
import fs from 'fs/promises';
import path from 'path';
import { tmpdir } from 'os';

describe('CalculatorTool', () => {
  test('should perform addition correctly', async () => {
    const result = await calculatorTool({
      operation: 'add',
      a: 5,
      b: 3,
    });
    
    expect(result).toContain('8');
    expect(result).toContain('5 add 3 = 8');
  });

  test('should throw error for division by zero', async () => {
    await expect(calculatorTool({
      operation: 'divide',
      a: 10,
      b: 0,
    })).rejects.toThrow('除数不能为零');
  });

  test('should throw error for invalid operation', async () => {
    await expect(calculatorTool({
      operation: 'invalid' as any,
      a: 5,
      b: 3,
    })).rejects.toThrow('不支持的运算类型');
  });

  test.each([
    ['add', 1, 2, '3'],
    ['subtract', 5, 3, '2'],
    ['multiply', 4, 3, '12'],
    ['divide', 8, 2, '4'],
  ])('should perform %s operation correctly', async (operation, a, b, expected) => {
    const result = await calculatorTool({
      operation: operation as any,
      a,
      b,
    });
    
    expect(result).toContain(expected);
  });
});

describe('ReadFileTool', () => {
  let tempDir: string;
  let testFile: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(tmpdir(), 'mcp-test-'));
    testFile = path.join(tempDir, 'test.txt');
  });

  afterEach(async () => {
    await fs.rm(tempDir, { recursive: true, force: true });
  });

  test('should read existing file', async () => {
    const testContent = 'Hello, World!';
    await fs.writeFile(testFile, testContent, 'utf-8');

    const result = await readFileTool({
      filePath: testFile,
      encoding: 'utf-8',
    });

    expect(result).toBe(testContent);
  });

  test('should throw error for nonexistent file', async () => {
    await expect(readFileTool({
      filePath: 'nonexistent.txt',
    })).rejects.toThrow();
  });

  test('should throw error for unsafe path', async () => {
    await expect(readFileTool({
      filePath: '../../../etc/passwd',
    })).rejects.toThrow('不安全的文件路径');
  });
});

// 模拟和夹具
jest.mock('fs/promises');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('FileReadTool with mocks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should handle file read errors', async () => {
    mockFs.readFile.mockRejectedValue(new Error('权限拒绝'));

    await expect(readFileTool({
      filePath: 'test.txt',
    })).rejects.toThrow('读取文件失败');
  });
});
```

**运行测试**
```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- tools.test.ts

# 运行监视模式
npm test -- --watch

# 生成覆盖率报告
npm test -- --coverage
```

**✅ 验证检查点**：单元测试覆盖率 > 80%，所有测试通过

## 4.2 集成测试

### 4.2.1 MCP协议集成测试

**Python集成测试（tests/test_integration.py）**
```python
import pytest
import asyncio
import json
from mcp.client import ClientSession
from mcp.client.stdio import StdioClientTransport
import subprocess
import sys
import os

class TestMCPIntegration:
    """MCP协议集成测试"""
    
    @pytest.fixture
    async def mcp_client(self):
        """创建MCP客户端连接"""
        # 启动服务器进程
        server_cmd = [sys.executable, "-m", "src.server"]
        transport = StdioClientTransport(
            command=server_cmd[0],
            args=server_cmd[1:],
            cwd=os.getcwd()
        )
        
        # 创建客户端会话
        session = ClientSession(transport)
        await session.initialize()
        
        yield session
        
        # 清理
        await session.close()
    
    @pytest.mark.asyncio
    async def test_server_initialization(self, mcp_client):
        """测试服务器初始化"""
        # 测试服务器信息
        info = await mcp_client.get_server_info()
        assert info.name == "my-mcp-server"
        assert info.version is not None
    
    @pytest.mark.asyncio
    async def test_tools_list(self, mcp_client):
        """测试工具列表"""
        tools = await mcp_client.list_tools()
        tool_names = [tool.name for tool in tools.tools]
        
        assert "calculator" in tool_names
        assert "read_file" in tool_names
    
    @pytest.mark.asyncio
    async def test_tool_call(self, mcp_client):
        """测试工具调用"""
        result = await mcp_client.call_tool("calculator", {
            "operation": "add",
            "a": 5,
            "b": 3
        })
        
        assert len(result.content) > 0
        assert "8" in result.content[0].text
    
    @pytest.mark.asyncio
    async def test_prompts_list(self, mcp_client):
        """测试提示词列表"""
        prompts = await mcp_client.list_prompts()
        prompt_names = [prompt.name for prompt in prompts.prompts]
        
        assert "code_review" in prompt_names
        assert "generate_docs" in prompt_names
    
    @pytest.mark.asyncio
    async def test_prompt_get(self, mcp_client):
        """测试获取提示词"""
        result = await mcp_client.get_prompt("code_review", {
            "code": "def hello(): return 'world'",
            "language": "python"
        })
        
        assert len(result.messages) > 0
        assert "代码审查" in result.messages[0].content.text
    
    @pytest.mark.asyncio
    async def test_resources_list(self, mcp_client):
        """测试资源列表"""
        resources = await mcp_client.list_resources()
        resource_uris = [resource.uri for resource in resources.resources]
        
        assert any("file://" in uri for uri in resource_uris)
    
    @pytest.mark.asyncio
    async def test_error_handling(self, mcp_client):
        """测试错误处理"""
        with pytest.raises(Exception):
            await mcp_client.call_tool("nonexistent_tool", {})
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, mcp_client):
        """测试并发请求"""
        tasks = []
        for i in range(5):
            task = mcp_client.call_tool("calculator", {
                "operation": "add",
                "a": i,
                "b": 1
            })
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        assert len(results) == 5
        
        for i, result in enumerate(results):
            expected = str(i + 1)
            assert expected in result.content[0].text

@pytest.mark.slow
class TestPerformance:
    """性能测试"""
    
    @pytest.mark.asyncio
    async def test_response_time(self, mcp_client):
        """测试响应时间"""
        import time
        
        start_time = time.time()
        await mcp_client.call_tool("calculator", {
            "operation": "add",
            "a": 1,
            "b": 1
        })
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response_time < 1.0  # 响应时间应小于1秒
    
    @pytest.mark.asyncio
    async def test_memory_usage(self, mcp_client):
        """测试内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 执行多次操作
        for _ in range(100):
            await mcp_client.call_tool("calculator", {
                "operation": "add",
                "a": 1,
                "b": 1
            })
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（例如小于10MB）
        assert memory_increase < 10 * 1024 * 1024
```

**TypeScript集成测试（tests/integration.test.ts）**
```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn, ChildProcess } from 'child_process';
import path from 'path';

describe('MCP Integration Tests', () => {
  let client: Client;
  let transport: StdioClientTransport;

  beforeAll(async () => {
    // 创建传输和客户端
    transport = new StdioClientTransport({
      command: 'node',
      args: ['dist/index.js'],
      cwd: process.cwd(),
    });

    client = new Client({
      name: 'test-client',
      version: '1.0.0',
    }, {
      capabilities: {},
    });

    // 连接到服务器
    await client.connect(transport);
  });

  afterAll(async () => {
    await client.close();
  });

  test('should initialize server', async () => {
    const response = await client.request(
      { method: 'initialize' },
      {
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'test-client', version: '1.0.0' },
        },
      }
    );

    expect(response.serverInfo.name).toBe('my-mcp-server');
  });

  test('should list tools', async () => {
    const response = await client.request(
      { method: 'tools/list' },
      { method: 'tools/list' }
    );

    const toolNames = response.tools.map((tool: any) => tool.name);
    expect(toolNames).toContain('calculator');
    expect(toolNames).toContain('read_file');
  });

  test('should call tool successfully', async () => {
    const response = await client.request(
      { method: 'tools/call' },
      {
        method: 'tools/call',
        params: {
          name: 'calculator',
          arguments: { operation: 'add', a: 5, b: 3 },
        },
      }
    );

    expect(response.content[0].text).toContain('8');
  });

  test('should handle errors gracefully', async () => {
    await expect(
      client.request(
        { method: 'tools/call' },
        {
          method: 'tools/call',
          params: {
            name: 'nonexistent_tool',
            arguments: {},
          },
        }
      )
    ).rejects.toThrow();
  });

  test('should handle concurrent requests', async () => {
    const promises = Array.from({ length: 5 }, (_, i) =>
      client.request(
        { method: 'tools/call' },
        {
          method: 'tools/call',
          params: {
            name: 'calculator',
            arguments: { operation: 'add', a: i, b: 1 },
          },
        }
      )
    );

    const results = await Promise.all(promises);
    expect(results).toHaveLength(5);

    results.forEach((result, i) => {
      expect(result.content[0].text).toContain(String(i + 1));
    });
  });
});

describe('Performance Tests', () => {
  test('should respond within acceptable time', async () => {
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['dist/index.js'],
    });

    const client = new Client({
      name: 'perf-test-client',
      version: '1.0.0',
    }, {
      capabilities: {},
    });

    try {
      await client.connect(transport);

      const startTime = Date.now();
      await client.request(
        { method: 'tools/call' },
        {
          method: 'tools/call',
          params: {
            name: 'calculator',
            arguments: { operation: 'add', a: 1, b: 1 },
          },
        }
      );
      const endTime = Date.now();

      const responseTime = endTime - startTime;
      expect(responseTime).toBeLessThan(1000); // 应在1秒内响应
    } finally {
      await client.close();
    }
  });
});
```

**✅ 验证检查点**：集成测试通过，MCP协议兼容性验证成功

## 4.3 调试工具和技巧

### 4.3.1 日志调试

**结构化日志实现（src/utils/logger.py）**
```python
import logging
import json
import sys
from datetime import datetime
from typing import Any, Dict

class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str, level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 创建处理器（输出到stderr，避免干扰STDIO通信）
        handler = logging.StreamHandler(sys.stderr)
        handler.setFormatter(self._get_formatter())
        
        self.logger.addHandler(handler)
    
    def _get_formatter(self):
        """获取日志格式化器"""
        return logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self._log("INFO", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self._log("ERROR", message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self._log("DEBUG", message, **kwargs)
    
    def _log(self, level: str, message: str, **kwargs):
        """记录结构化日志"""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": level,
            "message": message,
            "context": kwargs
        }
        
        # 输出到stderr
        print(json.dumps(log_data, ensure_ascii=False), file=sys.stderr)

# 使用示例
logger = StructuredLogger("mcp-server")

@mcp.tool()
async def debug_calculator(operation: str, a: float, b: float) -> str:
    """带调试信息的计算器"""
    logger.info("计算器工具被调用", 
                operation=operation, a=a, b=b)
    
    try:
        result = await calculator_tool(operation, a, b)
        logger.info("计算完成", result=result)
        return result
    except Exception as e:
        logger.error("计算失败", error=str(e), 
                    operation=operation, a=a, b=b)
        raise
```

### 4.3.2 MCP Inspector工具

**使用MCP Inspector进行调试**
```bash
# 安装MCP Inspector
npm install -g @modelcontextprotocol/inspector

# 启动Inspector
mcp-inspector

# 或者直接连接到服务器
mcp-inspector --command "python -m src.server"
```

**Inspector配置文件（inspector-config.json）**
```json
{
  "servers": {
    "my-server": {
      "command": "python",
      "args": ["-m", "src.server"],
      "cwd": "/path/to/project"
    }
  },
  "ui": {
    "theme": "dark",
    "autoConnect": true
  },
  "debugging": {
    "logLevel": "debug",
    "showRawMessages": true,
    "recordSession": true
  }
}
```

### 4.3.3 常见问题排查

**问题排查清单**
```python
class DiagnosticTool:
    """诊断工具"""
    
    @staticmethod
    async def check_server_health() -> Dict[str, Any]:
        """检查服务器健康状态"""
        import psutil
        import sys
        
        return {
            "python_version": sys.version,
            "memory_usage": psutil.virtual_memory()._asdict(),
            "cpu_usage": psutil.cpu_percent(),
            "disk_usage": psutil.disk_usage('/')._asdict(),
            "process_count": len(psutil.pids()),
        }
    
    @staticmethod
    async def validate_mcp_protocol() -> Dict[str, bool]:
        """验证MCP协议实现"""
        checks = {
            "stdio_transport": True,  # 检查STDIO传输
            "json_rpc": True,         # 检查JSON-RPC格式
            "tool_schema": True,      # 检查工具模式
            "error_handling": True,   # 检查错误处理
        }
        
        # 实际验证逻辑...
        return checks
    
    @staticmethod
    async def test_tool_performance() -> Dict[str, float]:
        """测试工具性能"""
        import time
        
        results = {}
        
        # 测试各个工具的响应时间
        tools = ["calculator", "read_file"]
        for tool_name in tools:
            start_time = time.time()
            try:
                # 调用工具...
                pass
            except Exception:
                pass
            end_time = time.time()
            
            results[tool_name] = end_time - start_time
        
        return results

# 注册诊断工具
@mcp.tool()
async def diagnose_server() -> str:
    """服务器诊断工具"""
    health = await DiagnosticTool.check_server_health()
    protocol = await DiagnosticTool.validate_mcp_protocol()
    performance = await DiagnosticTool.test_tool_performance()
    
    return json.dumps({
        "health": health,
        "protocol": protocol,
        "performance": performance
    }, indent=2, ensure_ascii=False)
```

**常见错误及解决方案**

| 错误类型 | 症状 | 解决方案 |
|---------|------|----------|
| STDIO通信错误 | 客户端无法连接 | 检查是否使用print输出，改用stderr日志 |
| JSON-RPC格式错误 | 协议解析失败 | 验证消息格式，使用MCP Inspector检查 |
| 工具调用超时 | 工具执行时间过长 | 添加超时处理，优化算法性能 |
| 内存泄漏 | 内存使用持续增长 | 检查缓存策略，及时清理资源 |
| 权限错误 | 文件访问被拒绝 | 检查文件权限，验证安全策略 |

**✅ 验证检查点**：调试工具配置完成，常见问题排查流程建立

---

**下一步**：继续阅读 [05-部署维护指南](./05-deployment-maintenance.md) 学习生产环境部署和维护。

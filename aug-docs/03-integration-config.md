# 03 - 集成配置管理

## 概述

本指南介绍MCP Server的配置管理、客户端连接设置、认证机制和性能优化配置。

## 3.1 MCP Server配置

### 3.1.1 基础配置结构

**配置文件模板（configs/server-config.json）**
```json
{
  "server": {
    "name": "my-mcp-server",
    "version": "1.0.0",
    "description": "My MCP Server Application",
    "transport": {
      "type": "stdio",
      "options": {
        "bufferSize": 8192,
        "timeout": 30000
      }
    }
  },
  "capabilities": {
    "tools": {
      "enabled": true,
      "maxConcurrent": 10
    },
    "prompts": {
      "enabled": true,
      "cacheEnabled": true,
      "cacheTTL": 300
    },
    "resources": {
      "enabled": true,
      "cacheEnabled": true,
      "cacheTTL": 600,
      "maxFileSize": "10MB"
    }
  },
  "security": {
    "allowedPaths": ["./data", "./configs"],
    "blockedPaths": ["/etc", "/var", "/usr"],
    "maxRequestSize": "1MB",
    "rateLimiting": {
      "enabled": true,
      "maxRequests": 100,
      "windowMs": 60000
    }
  },
  "logging": {
    "level": "info",
    "format": "json",
    "outputs": ["stderr", "file"],
    "file": {
      "path": "./logs/server.log",
      "maxSize": "100MB",
      "maxFiles": 5
    }
  },
  "performance": {
    "caching": {
      "enabled": true,
      "maxSize": "100MB",
      "ttl": 3600
    },
    "compression": {
      "enabled": true,
      "level": 6
    }
  }
}
```

### 3.1.2 Python配置管理实现

**配置管理器（src/utils/config.py）**
```python
import json
import os
from typing import Any, Dict, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "configs/server-config.json"):
        self.config_path = Path(config_path)
        self._config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info(f"配置文件加载成功: {self.config_path}")
            else:
                logger.warning(f"配置文件不存在，使用默认配置: {self.config_path}")
                self._config = self._get_default_config()
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self._config = self._get_default_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值（支持点号分隔的嵌套键）
        
        Args:
            key: 配置键，如 "server.name" 或 "capabilities.tools.enabled"
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception:
            return default
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return self.get("server", {})
    
    def get_capabilities_config(self) -> Dict[str, Any]:
        """获取功能配置"""
        return self.get("capabilities", {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return self.get("security", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get("logging", {})
    
    def is_feature_enabled(self, feature: str) -> bool:
        """检查功能是否启用"""
        return self.get(f"capabilities.{feature}.enabled", False)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "server": {
                "name": "mcp-server",
                "version": "1.0.0",
                "transport": {"type": "stdio"}
            },
            "capabilities": {
                "tools": {"enabled": True},
                "prompts": {"enabled": True},
                "resources": {"enabled": True}
            },
            "security": {
                "allowedPaths": ["./data"],
                "maxRequestSize": "1MB"
            },
            "logging": {
                "level": "info",
                "format": "text"
            }
        }

# 全局配置实例
config = ConfigManager()
```

**集成到主服务器（src/server.py）**
```python
import logging
from mcp.server.fastmcp import FastMCP
from .utils.config import config

# 配置日志
log_config = config.get_logging_config()
logging.basicConfig(
    level=getattr(logging, log_config.get("level", "INFO").upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 初始化服务器
server_config = config.get_server_config()
mcp = FastMCP(
    name=server_config.get("name", "mcp-server"),
    version=server_config.get("version", "1.0.0")
)

# 根据配置启用功能
if config.is_feature_enabled("tools"):
    logger.info("工具功能已启用")
    # 注册工具...

if config.is_feature_enabled("prompts"):
    logger.info("提示词功能已启用")
    # 注册提示词...

if config.is_feature_enabled("resources"):
    logger.info("资源功能已启用")
    # 注册资源...
```

### 3.1.3 TypeScript配置管理实现

**配置管理器（src/utils/config.ts）**
```typescript
import fs from 'fs/promises';
import path from 'path';

export interface ServerConfig {
  server: {
    name: string;
    version: string;
    description?: string;
    transport: {
      type: 'stdio' | 'http';
      options?: Record<string, any>;
    };
  };
  capabilities: {
    tools: { enabled: boolean; maxConcurrent?: number };
    prompts: { enabled: boolean; cacheEnabled?: boolean };
    resources: { enabled: boolean; cacheEnabled?: boolean };
  };
  security: {
    allowedPaths: string[];
    blockedPaths?: string[];
    maxRequestSize: string;
    rateLimiting?: {
      enabled: boolean;
      maxRequests: number;
      windowMs: number;
    };
  };
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    format: 'json' | 'text';
    outputs: string[];
  };
}

export class ConfigManager {
  private config: ServerConfig;
  private configPath: string;

  constructor(configPath: string = 'configs/server-config.json') {
    this.configPath = configPath;
    this.config = this.getDefaultConfig();
    this.loadConfig();
  }

  private async loadConfig(): Promise<void> {
    try {
      const configData = await fs.readFile(this.configPath, 'utf-8');
      this.config = { ...this.config, ...JSON.parse(configData) };
      console.error(`配置文件加载成功: ${this.configPath}`);
    } catch (error) {
      console.error(`加载配置文件失败，使用默认配置: ${error}`);
    }
  }

  public get<T = any>(key: string, defaultValue?: T): T {
    const keys = key.split('.');
    let value: any = this.config;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return defaultValue as T;
      }
    }

    return value as T;
  }

  public getServerConfig() {
    return this.config.server;
  }

  public getCapabilitiesConfig() {
    return this.config.capabilities;
  }

  public getSecurityConfig() {
    return this.config.security;
  }

  public getLoggingConfig() {
    return this.config.logging;
  }

  public isFeatureEnabled(feature: keyof ServerConfig['capabilities']): boolean {
    return this.config.capabilities[feature]?.enabled ?? false;
  }

  private getDefaultConfig(): ServerConfig {
    return {
      server: {
        name: 'mcp-server',
        version: '1.0.0',
        transport: { type: 'stdio' },
      },
      capabilities: {
        tools: { enabled: true },
        prompts: { enabled: true },
        resources: { enabled: true },
      },
      security: {
        allowedPaths: ['./data'],
        maxRequestSize: '1MB',
      },
      logging: {
        level: 'info',
        format: 'text',
        outputs: ['stderr'],
      },
    };
  }
}

// 全局配置实例
export const config = new ConfigManager();
```

**✅ 验证检查点**：配置文件加载成功，功能开关正常工作

## 3.2 客户端连接配置

### 3.2.1 Claude Desktop配置

**Claude Desktop配置文件（claude_desktop_config.json）**
```json
{
  "mcpServers": {
    "my-mcp-server": {
      "command": "python",
      "args": ["-m", "src.server"],
      "cwd": "/absolute/path/to/your/project",
      "env": {
        "PYTHONPATH": "/absolute/path/to/your/project",
        "LOG_LEVEL": "info"
      }
    },
    "my-ts-server": {
      "command": "node",
      "args": ["dist/index.js"],
      "cwd": "/absolute/path/to/your/project",
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

**配置文件位置**：
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Windows**: `%APPDATA%/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

### 3.2.2 自定义客户端配置

**Python客户端示例**
```python
import asyncio
from mcp.client import ClientSession
from mcp.client.stdio import StdioClientTransport

async def connect_to_server():
    """连接到MCP服务器"""
    # 创建STDIO传输
    transport = StdioClientTransport(
        command="python",
        args=["-m", "src.server"],
        cwd="/path/to/server"
    )
    
    # 创建客户端会话
    async with ClientSession(transport) as session:
        # 初始化连接
        await session.initialize()
        
        # 列出可用工具
        tools = await session.list_tools()
        print(f"可用工具: {[tool.name for tool in tools.tools]}")
        
        # 调用工具
        result = await session.call_tool("calculator", {
            "operation": "add",
            "a": 5,
            "b": 3
        })
        print(f"计算结果: {result.content[0].text}")

# 运行客户端
asyncio.run(connect_to_server())
```

**TypeScript客户端示例**
```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function connectToServer() {
  // 创建STDIO传输
  const transport = new StdioClientTransport({
    command: 'node',
    args: ['dist/index.js'],
    cwd: '/path/to/server'
  });

  // 创建客户端
  const client = new Client({
    name: 'my-client',
    version: '1.0.0'
  }, {
    capabilities: {}
  });

  try {
    // 连接到服务器
    await client.connect(transport);

    // 列出可用工具
    const tools = await client.request(
      { method: 'tools/list' },
      { method: 'tools/list' }
    );
    console.log('可用工具:', tools.tools.map(t => t.name));

    // 调用工具
    const result = await client.request(
      { method: 'tools/call' },
      {
        method: 'tools/call',
        params: {
          name: 'calculator',
          arguments: { operation: 'add', a: 5, b: 3 }
        }
      }
    );
    console.log('计算结果:', result.content[0].text);

  } finally {
    await client.close();
  }
}

connectToServer().catch(console.error);
```

**✅ 验证检查点**：客户端连接成功，工具调用正常

## 3.3 认证和安全配置

### 3.3.1 基础安全措施

**安全配置实现（src/utils/security.py）**
```python
import os
import hashlib
import hmac
from typing import List, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class SecurityManager:
    """安全管理器"""
    
    def __init__(self, config: dict):
        self.allowed_paths = [Path(p).resolve() for p in config.get("allowedPaths", [])]
        self.blocked_paths = [Path(p).resolve() for p in config.get("blockedPaths", [])]
        self.max_request_size = self._parse_size(config.get("maxRequestSize", "1MB"))
        self.api_key = os.getenv("MCP_API_KEY")
    
    def validate_path(self, file_path: str) -> bool:
        """验证文件路径是否安全"""
        try:
            path = Path(file_path).resolve()
            
            # 检查是否在阻止列表中
            for blocked in self.blocked_paths:
                if self._is_subpath(path, blocked):
                    logger.warning(f"访问被阻止的路径: {path}")
                    return False
            
            # 检查是否在允许列表中
            for allowed in self.allowed_paths:
                if self._is_subpath(path, allowed):
                    return True
            
            logger.warning(f"访问未授权路径: {path}")
            return False
            
        except Exception as e:
            logger.error(f"路径验证失败: {e}")
            return False
    
    def validate_request_size(self, data: str) -> bool:
        """验证请求大小"""
        size = len(data.encode('utf-8'))
        if size > self.max_request_size:
            logger.warning(f"请求大小超限: {size} > {self.max_request_size}")
            return False
        return True
    
    def validate_api_key(self, provided_key: Optional[str]) -> bool:
        """验证API密钥"""
        if not self.api_key:
            return True  # 如果未设置API密钥，则不验证
        
        if not provided_key:
            return False
        
        return hmac.compare_digest(self.api_key, provided_key)
    
    def _is_subpath(self, path: Path, parent: Path) -> bool:
        """检查路径是否为父路径的子路径"""
        try:
            path.relative_to(parent)
            return True
        except ValueError:
            return False
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串（如 "1MB", "500KB"）"""
        size_str = size_str.upper().strip()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)

# 在主服务器中集成安全管理
from .utils.security import SecurityManager
from .utils.config import config

security = SecurityManager(config.get_security_config())

# 在工具中使用安全验证
@mcp.tool()
async def secure_read_file(file_path: str, api_key: Optional[str] = None) -> str:
    """安全的文件读取工具"""
    # 验证API密钥
    if not security.validate_api_key(api_key):
        raise ValueError("无效的API密钥")
    
    # 验证文件路径
    if not security.validate_path(file_path):
        raise ValueError("无权访问该文件路径")
    
    # 读取文件...
    async with aiofiles.open(file_path, 'r') as file:
        content = await file.read()
    
    # 验证响应大小
    if not security.validate_request_size(content):
        raise ValueError("文件内容过大")
    
    return content
```

**✅ 验证检查点**：安全策略配置完成，路径验证正常，API密钥验证有效

## 3.4 性能优化配置

### 3.4.1 缓存策略

**缓存管理器（src/utils/cache.py）**
```python
import asyncio
import time
from typing import Any, Dict, Optional, Tuple
import json
import hashlib

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 100, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.access_times: Dict[str, float] = {}
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self.cache:
            value, timestamp = self.cache[key]
            
            # 检查是否过期
            if time.time() - timestamp < self.ttl:
                self.access_times[key] = time.time()
                return value
            else:
                # 清理过期缓存
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
        
        return None
    
    async def set(self, key: str, value: Any):
        """设置缓存值"""
        # 如果缓存已满，清理最久未访问的项
        if len(self.cache) >= self.max_size:
            await self._evict_lru()
        
        self.cache[key] = (value, time.time())
        self.access_times[key] = time.time()
    
    async def _evict_lru(self):
        """清理最久未访问的缓存项"""
        if not self.access_times:
            return
        
        # 找到最久未访问的键
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # 删除缓存项
        if lru_key in self.cache:
            del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = json.dumps([args, kwargs], sort_keys=True, default=str)
        return hashlib.md5(key_data.encode()).hexdigest()

# 缓存装饰器
def cached(ttl: int = 3600):
    """缓存装饰器"""
    def decorator(func):
        cache = CacheManager(ttl=ttl)
        
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = cache.generate_key(func.__name__, *args, **kwargs)
            
            # 尝试从缓存获取
            result = await cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await cache.set(cache_key, result)
            
            return result
        
        return wrapper
    return decorator

# 使用缓存装饰器
@cached(ttl=300)  # 缓存5分钟
async def expensive_operation(param1: str, param2: int) -> str:
    """耗时操作示例"""
    await asyncio.sleep(1)  # 模拟耗时操作
    return f"结果: {param1}-{param2}"
```

**✅ 验证检查点**：缓存机制工作正常，性能提升明显

---

**下一步**：继续阅读 [04-测试调试流程](./04-testing-debugging.md) 学习测试和调试最佳实践。

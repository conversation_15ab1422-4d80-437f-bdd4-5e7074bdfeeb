# 02 - 核心功能开发

## 概述

本指南详细介绍MCP协议的三大核心功能模块：工具(Tools)、提示词(Prompts)、资源(Resources)的实现方法。

## 2.1 工具(Tools)模块开发

### 2.1.1 工具基础概念

工具是MCP Server向客户端暴露的可调用函数，允许LLM执行特定操作。

**工具设计原则**：
- 单一职责：每个工具只做一件事
- 参数验证：严格验证输入参数
- 错误处理：提供清晰的错误信息
- 文档完整：详细的描述和参数说明

### 2.1.2 Python工具实现

**基础工具模板（src/tools/example_tool.py）**
```python
from typing import Optional, Dict, Any
import logging
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class CalculatorInput(BaseModel):
    """计算器输入参数模型"""
    operation: str = Field(..., description="运算类型: add, subtract, multiply, divide")
    a: float = Field(..., description="第一个数字")
    b: float = Field(..., description="第二个数字")

async def calculator_tool(operation: str, a: float, b: float) -> str:
    """
    简单计算器工具
    
    Args:
        operation: 运算类型 (add, subtract, multiply, divide)
        a: 第一个数字
        b: 第二个数字
    
    Returns:
        计算结果字符串
        
    Raises:
        ValueError: 当运算类型无效或除零时
    """
    try:
        # 参数验证
        valid_operations = ["add", "subtract", "multiply", "divide"]
        if operation not in valid_operations:
            raise ValueError(f"无效的运算类型: {operation}. 支持的类型: {valid_operations}")
        
        # 执行计算
        if operation == "add":
            result = a + b
        elif operation == "subtract":
            result = a - b
        elif operation == "multiply":
            result = a * b
        elif operation == "divide":
            if b == 0:
                raise ValueError("除数不能为零")
            result = a / b
        
        logger.info(f"计算完成: {a} {operation} {b} = {result}")
        return f"计算结果: {a} {operation} {b} = {result}"
        
    except Exception as e:
        logger.error(f"计算错误: {str(e)}")
        raise ValueError(f"计算失败: {str(e)}")

# 文件操作工具示例
async def read_file_tool(file_path: str, encoding: str = "utf-8") -> str:
    """
    读取文件内容工具
    
    Args:
        file_path: 文件路径
        encoding: 文件编码，默认utf-8
    
    Returns:
        文件内容
    """
    try:
        import os
        import aiofiles
        
        # 安全检查：防止路径遍历攻击
        if ".." in file_path or file_path.startswith("/"):
            raise ValueError("不安全的文件路径")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 异步读取文件
        async with aiofiles.open(file_path, 'r', encoding=encoding) as file:
            content = await file.read()
            
        logger.info(f"成功读取文件: {file_path}")
        return content
        
    except Exception as e:
        logger.error(f"读取文件失败: {str(e)}")
        raise
```

**在主服务器中注册工具（src/server.py）**
```python
from mcp.server.fastmcp import FastMCP
from .tools.example_tool import calculator_tool, read_file_tool

mcp = FastMCP("my-mcp-server")

# 注册计算器工具
@mcp.tool()
async def calculator(operation: str, a: float, b: float) -> str:
    """计算器工具"""
    return await calculator_tool(operation, a, b)

# 注册文件读取工具
@mcp.tool()
async def read_file(file_path: str, encoding: str = "utf-8") -> str:
    """读取文件工具"""
    return await read_file_tool(file_path, encoding)
```

### 2.1.3 TypeScript工具实现

**基础工具模板（src/tools/exampleTool.ts）**
```typescript
import { z } from 'zod';

// 输入参数验证模式
export const CalculatorInputSchema = z.object({
  operation: z.enum(['add', 'subtract', 'multiply', 'divide']),
  a: z.number(),
  b: z.number(),
});

export type CalculatorInput = z.infer<typeof CalculatorInputSchema>;

/**
 * 计算器工具实现
 */
export async function calculatorTool(input: CalculatorInput): Promise<string> {
  try {
    // 参数验证
    const { operation, a, b } = CalculatorInputSchema.parse(input);
    
    let result: number;
    
    switch (operation) {
      case 'add':
        result = a + b;
        break;
      case 'subtract':
        result = a - b;
        break;
      case 'multiply':
        result = a * b;
        break;
      case 'divide':
        if (b === 0) {
          throw new Error('除数不能为零');
        }
        result = a / b;
        break;
      default:
        throw new Error(`不支持的运算类型: ${operation}`);
    }
    
    console.error(`计算完成: ${a} ${operation} ${b} = ${result}`);
    return `计算结果: ${a} ${operation} ${b} = ${result}`;
    
  } catch (error) {
    console.error('计算错误:', error);
    throw new Error(`计算失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// 文件操作工具示例
export const ReadFileInputSchema = z.object({
  filePath: z.string(),
  encoding: z.string().default('utf-8'),
});

export type ReadFileInput = z.infer<typeof ReadFileInputSchema>;

/**
 * 读取文件工具实现
 */
export async function readFileTool(input: ReadFileInput): Promise<string> {
  try {
    const { filePath, encoding } = ReadFileInputSchema.parse(input);
    const fs = await import('fs/promises');
    const path = await import('path');
    
    // 安全检查
    if (filePath.includes('..') || path.isAbsolute(filePath)) {
      throw new Error('不安全的文件路径');
    }
    
    // 读取文件
    const content = await fs.readFile(filePath, { encoding: encoding as BufferEncoding });
    
    console.error(`成功读取文件: ${filePath}`);
    return content;
    
  } catch (error) {
    console.error('读取文件失败:', error);
    throw new Error(`读取文件失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}
```

**在主服务器中注册工具（src/index.ts）**
```typescript
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { calculatorTool, readFileTool, CalculatorInputSchema, ReadFileInputSchema } from './tools/exampleTool.js';

const server = new Server({
  name: 'my-mcp-server',
  version: '0.1.0',
}, {
  capabilities: { tools: {} },
});

// 注册工具列表
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'calculator',
        description: '执行基本数学运算',
        inputSchema: {
          type: 'object',
          properties: {
            operation: {
              type: 'string',
              enum: ['add', 'subtract', 'multiply', 'divide'],
              description: '运算类型',
            },
            a: { type: 'number', description: '第一个数字' },
            b: { type: 'number', description: '第二个数字' },
          },
          required: ['operation', 'a', 'b'],
        },
      },
      {
        name: 'read_file',
        description: '读取文件内容',
        inputSchema: {
          type: 'object',
          properties: {
            filePath: { type: 'string', description: '文件路径' },
            encoding: { type: 'string', description: '文件编码', default: 'utf-8' },
          },
          required: ['filePath'],
        },
      },
    ],
  };
});

// 注册工具调用处理器
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  
  try {
    switch (name) {
      case 'calculator':
        const result = await calculatorTool(args as any);
        return { content: [{ type: 'text', text: result }] };
        
      case 'read_file':
        const content = await readFileTool(args as any);
        return { content: [{ type: 'text', text: content }] };
        
      default:
        throw new Error(`未知工具: ${name}`);
    }
  } catch (error) {
    throw new Error(`工具执行失败: ${error instanceof Error ? error.message : String(error)}`);
  }
});
```

**✅ 验证检查点**：工具注册成功，参数验证正常，错误处理完整

## 2.2 提示词(Prompts)模块开发

### 2.2.1 提示词基础概念

提示词是预定义的模板，帮助用户快速完成特定任务。

**提示词设计原则**：
- 清晰明确：目标和用途明确
- 参数化：支持动态内容替换
- 可复用：适用于多种场景
- 结构化：有组织的内容布局

### 2.2.2 Python提示词实现

**提示词模板（src/prompts/example_prompt.py）**
```python
from typing import Dict, Any, Optional
from datetime import datetime

class CodeReviewPrompt:
    """代码审查提示词模板"""
    
    @staticmethod
    def generate(
        code: str,
        language: str,
        focus_areas: Optional[list] = None,
        severity_level: str = "medium"
    ) -> str:
        """
        生成代码审查提示词
        
        Args:
            code: 要审查的代码
            language: 编程语言
            focus_areas: 关注领域列表
            severity_level: 严重程度 (low, medium, high)
        
        Returns:
            格式化的提示词
        """
        if focus_areas is None:
            focus_areas = ["安全性", "性能", "可读性", "最佳实践"]
        
        focus_text = "、".join(focus_areas)
        
        prompt = f"""
# 代码审查请求

## 基本信息
- **语言**: {language}
- **审查时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **严重程度**: {severity_level}

## 审查重点
请重点关注以下方面：{focus_text}

## 待审查代码
```{language}
{code}
```

## 审查要求
1. 识别潜在的问题和改进点
2. 提供具体的修改建议
3. 解释问题的影响和重要性
4. 如果代码质量良好，请给出积极反馈

请按照以下格式提供审查结果：

### 🔍 发现的问题
[列出发现的问题]

### 💡 改进建议
[提供具体的改进建议]

### ⭐ 优点
[指出代码的优点]

### 📊 总体评分
[给出1-10分的评分并说明理由]
"""
        return prompt.strip()

class DocumentationPrompt:
    """文档生成提示词模板"""
    
    @staticmethod
    def generate(
        code: str,
        doc_type: str = "api",
        include_examples: bool = True
    ) -> str:
        """
        生成文档编写提示词
        
        Args:
            code: 需要文档的代码
            doc_type: 文档类型 (api, user_guide, technical)
            include_examples: 是否包含示例
        
        Returns:
            格式化的提示词
        """
        examples_section = """
## 示例要求
请提供以下类型的示例：
1. 基本使用示例
2. 高级用法示例
3. 错误处理示例
4. 最佳实践示例
""" if include_examples else ""
        
        prompt = f"""
# 文档生成请求

## 文档类型
{doc_type.upper()} 文档

## 源代码
```
{code}
```

## 文档要求
1. 清晰的功能描述
2. 完整的参数说明
3. 返回值描述
4. 可能的异常情况
5. 使用注意事项

{examples_section}

## 输出格式
请使用Markdown格式，包含适当的标题层级和代码块。
"""
        return prompt.strip()
```

**在主服务器中注册提示词（src/server.py）**
```python
from .prompts.example_prompt import CodeReviewPrompt, DocumentationPrompt

@mcp.prompt()
async def code_review(
    code: str,
    language: str,
    focus_areas: list = None,
    severity_level: str = "medium"
) -> str:
    """代码审查提示词"""
    return CodeReviewPrompt.generate(code, language, focus_areas, severity_level)

@mcp.prompt()
async def generate_docs(
    code: str,
    doc_type: str = "api",
    include_examples: bool = True
) -> str:
    """文档生成提示词"""
    return DocumentationPrompt.generate(code, doc_type, include_examples)
```

**✅ 验证检查点**：提示词模板创建成功，参数替换正常，格式化输出正确

## 2.3 资源(Resources)模块开发

### 2.3.1 资源基础概念

资源提供对文件、数据库、API等外部数据源的访问能力。

**资源设计原则**：
- 统一接口：标准化的访问方式
- 缓存策略：提高访问性能
- 权限控制：安全的访问控制
- 错误恢复：优雅的错误处理

### 2.3.2 Python资源实现

**资源模板（src/resources/example_resource.py）**
```python
from typing import Dict, Any, Optional, List
import json
import aiofiles
import asyncio
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class FileResource:
    """文件资源管理器"""
    
    def __init__(self, base_path: str = "./data"):
        self.base_path = base_path
        self.cache = {}
        self.cache_ttl = timedelta(minutes=5)
    
    async def read(self, resource_path: str) -> str:
        """
        读取资源内容
        
        Args:
            resource_path: 资源路径
            
        Returns:
            资源内容
        """
        try:
            # 检查缓存
            cache_key = f"file:{resource_path}"
            if self._is_cached(cache_key):
                logger.info(f"从缓存读取资源: {resource_path}")
                return self.cache[cache_key]["content"]
            
            # 安全路径检查
            safe_path = self._validate_path(resource_path)
            
            # 异步读取文件
            async with aiofiles.open(safe_path, 'r', encoding='utf-8') as file:
                content = await file.read()
            
            # 更新缓存
            self._update_cache(cache_key, content)
            
            logger.info(f"成功读取资源: {resource_path}")
            return content
            
        except Exception as e:
            logger.error(f"读取资源失败: {resource_path}, 错误: {str(e)}")
            raise
    
    async def list(self, directory: str = "") -> List[Dict[str, Any]]:
        """
        列出资源目录
        
        Args:
            directory: 目录路径
            
        Returns:
            资源列表
        """
        try:
            import os
            
            target_path = os.path.join(self.base_path, directory)
            target_path = os.path.normpath(target_path)
            
            if not target_path.startswith(self.base_path):
                raise ValueError("不安全的目录路径")
            
            resources = []
            for item in os.listdir(target_path):
                item_path = os.path.join(target_path, item)
                stat = os.stat(item_path)
                
                resources.append({
                    "name": item,
                    "type": "directory" if os.path.isdir(item_path) else "file",
                    "size": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "path": os.path.relpath(item_path, self.base_path)
                })
            
            return resources
            
        except Exception as e:
            logger.error(f"列出资源失败: {directory}, 错误: {str(e)}")
            raise
    
    def _validate_path(self, resource_path: str) -> str:
        """验证并返回安全的文件路径"""
        import os
        
        # 防止路径遍历攻击
        if ".." in resource_path or resource_path.startswith("/"):
            raise ValueError("不安全的资源路径")
        
        safe_path = os.path.join(self.base_path, resource_path)
        safe_path = os.path.normpath(safe_path)
        
        # 确保路径在基础目录内
        if not safe_path.startswith(self.base_path):
            raise ValueError("资源路径超出允许范围")
        
        return safe_path
    
    def _is_cached(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]["timestamp"]
        return datetime.now() - cache_time < self.cache_ttl
    
    def _update_cache(self, cache_key: str, content: str):
        """更新缓存"""
        self.cache[cache_key] = {
            "content": content,
            "timestamp": datetime.now()
        }

class ConfigResource:
    """配置资源管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self._config = None
        self._last_modified = None
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键（支持点号分隔的嵌套键）
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            await self._load_config()
            
            # 支持嵌套键访问，如 "database.host"
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            logger.error(f"获取配置失败: {key}, 错误: {str(e)}")
            return default
    
    async def _load_config(self):
        """加载配置文件"""
        try:
            import os
            
            # 检查文件是否被修改
            if os.path.exists(self.config_file):
                modified_time = os.path.getmtime(self.config_file)
                if self._last_modified != modified_time:
                    async with aiofiles.open(self.config_file, 'r', encoding='utf-8') as file:
                        content = await file.read()
                        self._config = json.loads(content)
                        self._last_modified = modified_time
                        logger.info(f"配置文件已重新加载: {self.config_file}")
            else:
                if self._config is None:
                    self._config = {}
                    logger.warning(f"配置文件不存在: {self.config_file}")
                    
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            if self._config is None:
                self._config = {}
```

**在主服务器中注册资源（src/server.py）**
```python
from .resources.example_resource import FileResource, ConfigResource

# 初始化资源管理器
file_resource = FileResource("./data")
config_resource = ConfigResource("./config.json")

@mcp.resource()
async def read_file_resource(path: str) -> str:
    """读取文件资源"""
    return await file_resource.read(path)

@mcp.resource()
async def list_resources(directory: str = "") -> str:
    """列出资源目录"""
    resources = await file_resource.list(directory)
    return json.dumps(resources, indent=2, ensure_ascii=False)

@mcp.resource()
async def get_config(key: str, default: str = None) -> str:
    """获取配置值"""
    value = await config_resource.get(key, default)
    return str(value) if value is not None else ""
```

**✅ 验证检查点**：资源访问正常，缓存机制工作，权限控制有效

---

**下一步**：继续阅读 [03-集成配置管理](./03-integration-config.md) 学习服务器配置和客户端集成。

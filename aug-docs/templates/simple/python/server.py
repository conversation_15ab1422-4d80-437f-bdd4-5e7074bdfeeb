#!/usr/bin/env python3
"""
简单MCP Server模板
单文件实现，包含基础工具功能

使用方法：
1. 安装依赖：pip install "mcp[cli]" httpx
2. 运行服务器：python server.py
3. 在Claude Desktop中配置此服务器
"""

import asyncio
import logging
import json
from typing import Optional
from mcp.server.fastmcp import FastMCP
import httpx

# 配置日志（重要：使用stderr，避免干扰STDIO通信）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# 初始化MCP服务器
mcp = FastMCP("simple-mcp-server")

# ============================================================================
# 工具实现
# ============================================================================

@mcp.tool()
async def calculator(operation: str, a: float, b: float) -> str:
    """
    简单计算器工具
    
    Args:
        operation: 运算类型 (add, subtract, multiply, divide)
        a: 第一个数字
        b: 第二个数字
    
    Returns:
        计算结果
    """
    try:
        logger.info(f"计算器调用: {operation}({a}, {b})")
        
        if operation == "add":
            result = a + b
        elif operation == "subtract":
            result = a - b
        elif operation == "multiply":
            result = a * b
        elif operation == "divide":
            if b == 0:
                raise ValueError("除数不能为零")
            result = a / b
        else:
            raise ValueError(f"不支持的运算: {operation}")
        
        response = f"计算结果: {a} {operation} {b} = {result}"
        logger.info(f"计算完成: {response}")
        return response
        
    except Exception as e:
        error_msg = f"计算错误: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.tool()
async def get_weather(city: str, country: str = "CN") -> str:
    """
    获取天气信息工具（使用免费API）
    
    Args:
        city: 城市名称
        country: 国家代码，默认CN
    
    Returns:
        天气信息
    """
    try:
        logger.info(f"获取天气: {city}, {country}")
        
        # 使用OpenWeatherMap免费API（需要注册获取API key）
        # 这里使用模拟数据作为示例
        weather_data = {
            "city": city,
            "country": country,
            "temperature": "22°C",
            "description": "晴朗",
            "humidity": "65%",
            "wind_speed": "5 km/h"
        }
        
        response = f"""
🌤️ {city}, {country} 天气信息:
🌡️ 温度: {weather_data['temperature']}
☀️ 天气: {weather_data['description']}
💧 湿度: {weather_data['humidity']}
💨 风速: {weather_data['wind_speed']}

注意: 这是示例数据，实际使用请配置真实的天气API
"""
        
        logger.info(f"天气信息获取成功: {city}")
        return response.strip()
        
    except Exception as e:
        error_msg = f"获取天气信息失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.tool()
async def text_analyzer(text: str) -> str:
    """
    文本分析工具
    
    Args:
        text: 要分析的文本
    
    Returns:
        文本分析结果
    """
    try:
        logger.info(f"分析文本，长度: {len(text)}")
        
        # 基础文本分析
        word_count = len(text.split())
        char_count = len(text)
        char_count_no_spaces = len(text.replace(' ', ''))
        line_count = len(text.split('\n'))
        
        # 简单的情感分析（基于关键词）
        positive_words = ['好', '棒', '优秀', '喜欢', '开心', '满意', 'good', 'great', 'excellent', 'love', 'happy']
        negative_words = ['坏', '差', '糟糕', '讨厌', '难过', '不满', 'bad', 'terrible', 'awful', 'hate', 'sad']
        
        positive_count = sum(1 for word in positive_words if word.lower() in text.lower())
        negative_count = sum(1 for word in negative_words if word.lower() in text.lower())
        
        if positive_count > negative_count:
            sentiment = "积极 😊"
        elif negative_count > positive_count:
            sentiment = "消极 😔"
        else:
            sentiment = "中性 😐"
        
        response = f"""
📊 文本分析结果:

📝 基础统计:
• 字符数: {char_count}
• 字符数(不含空格): {char_count_no_spaces}
• 单词数: {word_count}
• 行数: {line_count}

😊 情感分析:
• 情感倾向: {sentiment}
• 积极词汇: {positive_count}
• 消极词汇: {negative_count}

💡 文本预览:
{text[:100]}{'...' if len(text) > 100 else ''}
"""
        
        logger.info("文本分析完成")
        return response.strip()
        
    except Exception as e:
        error_msg = f"文本分析失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.tool()
async def random_quote() -> str:
    """
    获取随机名言警句
    
    Returns:
        随机名言
    """
    try:
        logger.info("获取随机名言")
        
        quotes = [
            "成功不是终点，失败不是致命的，重要的是继续前进的勇气。 - 温斯顿·丘吉尔",
            "生活就像骑自行车，要保持平衡，就必须不断前进。 - 阿尔伯特·爱因斯坦",
            "不要等待机会，而要创造机会。 - 乔治·伯纳德·肖",
            "成功的秘诀在于坚持自己的目标。 - 本杰明·迪斯雷利",
            "唯一不可能的事情是你不去尝试的事情。 - 未知",
            "The only way to do great work is to love what you do. - Steve Jobs",
            "Innovation distinguishes between a leader and a follower. - Steve Jobs",
            "Stay hungry, stay foolish. - Steve Jobs",
            "The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt",
            "It is during our darkest moments that we must focus to see the light. - Aristotle"
        ]
        
        import random
        selected_quote = random.choice(quotes)
        
        response = f"💭 今日名言:\n\n{selected_quote}"
        
        logger.info("随机名言获取成功")
        return response
        
    except Exception as e:
        error_msg = f"获取名言失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.tool()
async def system_info() -> str:
    """
    获取系统信息
    
    Returns:
        系统信息
    """
    try:
        logger.info("获取系统信息")
        
        import platform
        import sys
        from datetime import datetime
        
        info = {
            "操作系统": platform.system(),
            "系统版本": platform.release(),
            "架构": platform.machine(),
            "Python版本": sys.version.split()[0],
            "当前时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "服务器名称": "simple-mcp-server",
            "服务器版本": "1.0.0"
        }
        
        response = "🖥️ 系统信息:\n\n"
        for key, value in info.items():
            response += f"• {key}: {value}\n"
        
        logger.info("系统信息获取成功")
        return response.strip()
        
    except Exception as e:
        error_msg = f"获取系统信息失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

# ============================================================================
# 服务器启动
# ============================================================================

def main():
    """主函数"""
    logger.info("启动简单MCP服务器...")
    logger.info("可用工具:")
    logger.info("  - calculator: 基础计算器")
    logger.info("  - get_weather: 天气查询（示例数据）")
    logger.info("  - text_analyzer: 文本分析")
    logger.info("  - random_quote: 随机名言")
    logger.info("  - system_info: 系统信息")
    
    try:
        # 运行MCP服务器
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
        raise

if __name__ == "__main__":
    main()

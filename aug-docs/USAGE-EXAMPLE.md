# AI助手使用示例

## 🤖 使用场景演示

以下是AI助手如何使用这套提示词系统来帮助用户开发MCP Server的完整示例。

## 📝 对话示例

### 用户请求
```
用户: "我想创建一个MCP Server来处理文档分析，你能帮我吗？"
```

### AI助手响应流程

#### 第1步：需求分析（基于AI-ASSISTANT-GUIDE.md）
```
AI助手: 我来帮您开发文档分析MCP Server！为了提供最合适的方案，我需要了解：

🔍 **技术背景**
- 您更熟悉Python还是TypeScript？
- 您的开发经验如何？（新手/中级/高级）

🎯 **项目需求**
- 这个项目是用于学习、原型验证还是生产环境？
- 文档分析具体包括什么功能？（PDF解析、文本提取、内容分析等）

📊 **功能模块**
- 需要工具功能吗？（文档处理函数）
- 需要提示词模板吗？（分析报告模板）
- 需要资源访问吗？（文件系统、数据库）

请告诉我这些信息，我会为您推荐最合适的开发方案！
```

#### 第2步：用户回复
```
用户: "我熟悉Python，有中级开发经验。想做一个生产级的系统，
主要功能是PDF文档解析、文本提取和智能摘要生成。
需要工具功能和提示词模板。"
```

#### 第3步：方案推荐（基于templates/和examples/）
```
AI助手: 根据您的需求，我推荐以下方案：

🎯 **推荐方案：生产级 Python MCP Server**

📋 **项目结构**（基于templates/production/python/）
```
document-analyzer/
├── server.py                 # 主服务器
├── requirements.txt          # 依赖管理
├── .env.example             # 环境配置
├── docker-compose.yml       # 容器化部署
├── src/
│   ├── tools/               # 文档处理工具
│   │   ├── pdf_parser.py    # PDF解析
│   │   ├── text_extractor.py # 文本提取
│   │   └── summarizer.py    # 智能摘要
│   ├── prompts/             # 提示词模板
│   │   └── analysis_templates.py
│   ├── resources/           # 资源管理
│   │   └── file_manager.py
│   └── utils/               # 工具函数
├── tests/                   # 测试用例
└── docs/                    # 文档
```

🛠️ **技术栈**
- 核心框架：FastMCP
- PDF处理：PyPDF2 + pdfplumber
- 文本分析：spaCy + transformers
- 部署方式：Docker + docker-compose

⭐ **选择理由**
- 生产级架构，支持高并发和容错
- 完整的安全验证和错误处理
- 模块化设计，易于扩展和维护
- 包含监控和日志记录

您觉得这个方案如何？我现在就可以为您生成完整的代码！
```

#### 第4步：代码生成（基于templates/production/python/）
```
AI助手: 完美！我为您生成了完整的文档分析MCP Server项目：

📁 **主服务器文件 (server.py)**
```python
#!/usr/bin/env python3
"""
文档分析MCP Server
支持PDF解析、文本提取和智能摘要生成
"""

import asyncio
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any

from mcp.server.fastmcp import FastMCP
from mcp.server.models import InitializationOptions
import uvloop

# 导入自定义模块
from src.tools.pdf_parser import parse_pdf_tool
from src.tools.text_extractor import extract_text_tool
from src.tools.summarizer import summarize_text_tool
from src.prompts.analysis_templates import get_analysis_prompts
from src.resources.file_manager import FileManager
from src.utils.config import load_config
from src.utils.security import SecurityManager
from src.utils.logger import setup_logging

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

# 创建MCP服务器实例
mcp = FastMCP("document-analyzer")

# 初始化组件
config = load_config()
security_manager = SecurityManager(config.get("security", {}))
file_manager = FileManager(config.get("storage", {}))

@mcp.tool()
async def parse_pdf(
    file_path: str,
    extract_images: bool = False,
    extract_tables: bool = True
) -> Dict[str, Any]:
    """
    解析PDF文档并提取内容
    
    Args:
        file_path: PDF文件路径
        extract_images: 是否提取图片
        extract_tables: 是否提取表格
    
    Returns:
        解析结果字典
    """
    try:
        # 安全验证
        if not security_manager.validate_file_path(file_path):
            raise ValueError("无效的文件路径")
        
        # 调用PDF解析工具
        result = await parse_pdf_tool(
            file_path, 
            extract_images=extract_images,
            extract_tables=extract_tables
        )
        
        logger.info(f"成功解析PDF: {file_path}")
        return result
        
    except Exception as e:
        logger.error(f"PDF解析失败: {str(e)}")
        raise

@mcp.tool()
async def extract_text(
    content: str,
    language: str = "zh",
    clean_text: bool = True
) -> Dict[str, Any]:
    """
    从内容中提取和清理文本
    
    Args:
        content: 原始内容
        language: 语言代码
        clean_text: 是否清理文本
    
    Returns:
        提取结果
    """
    try:
        result = await extract_text_tool(
            content,
            language=language,
            clean_text=clean_text
        )
        
        logger.info("文本提取完成")
        return result
        
    except Exception as e:
        logger.error(f"文本提取失败: {str(e)}")
        raise

@mcp.tool()
async def generate_summary(
    text: str,
    summary_type: str = "abstract",
    max_length: int = 200
) -> str:
    """
    生成文本智能摘要
    
    Args:
        text: 输入文本
        summary_type: 摘要类型 (abstract/bullet_points/key_insights)
        max_length: 最大长度
    
    Returns:
        生成的摘要
    """
    try:
        summary = await summarize_text_tool(
            text,
            summary_type=summary_type,
            max_length=max_length
        )
        
        logger.info(f"生成摘要完成，类型: {summary_type}")
        return summary
        
    except Exception as e:
        logger.error(f"摘要生成失败: {str(e)}")
        raise

# 注册提示词模板
analysis_prompts = get_analysis_prompts()
for prompt_name, prompt_data in analysis_prompts.items():
    mcp.prompt(prompt_name, prompt_data["description"])(
        lambda **kwargs: prompt_data["template"].format(**kwargs)
    )

# 注册资源
@mcp.resource("file://documents/{path}")
async def get_document_resource(path: str) -> str:
    """获取文档资源"""
    try:
        return await file_manager.get_file_content(path)
    except Exception as e:
        logger.error(f"资源获取失败: {str(e)}")
        raise

async def main():
    """启动服务器"""
    try:
        # 设置事件循环
        asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
        
        # 启动服务器
        logger.info("启动文档分析MCP Server...")
        await mcp.run()
        
    except KeyboardInterrupt:
        logger.info("服务器停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
```

📁 **依赖文件 (requirements.txt)**
```
mcp>=1.2.0
fastmcp>=0.9.0
PyPDF2>=3.0.1
pdfplumber>=0.9.0
spacy>=3.7.0
transformers>=4.30.0
uvloop>=0.17.0
python-dotenv>=1.0.0
pydantic>=2.0.0
aiofiles>=23.0.0
redis>=4.5.0
```

📁 **环境配置 (.env.example)**
```env
# 服务器配置
LOG_LEVEL=INFO
MAX_FILE_SIZE=50MB
ALLOWED_EXTENSIONS=pdf,txt,docx

# 安全配置
ALLOWED_PATHS=/documents,/uploads
MAX_CONCURRENT_REQUESTS=10

# AI模型配置
HUGGINGFACE_API_KEY=your_api_key_here
OPENAI_API_KEY=your_api_key_here

# 缓存配置
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600
```

🚀 **快速启动**
```bash
# 1. 创建项目目录
mkdir document-analyzer
cd document-analyzer

# 2. 保存上述文件
# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境
cp .env.example .env
# 编辑 .env 文件

# 5. 运行服务器
python server.py
```

⚙️ **Claude Desktop配置**
```json
{
  "mcpServers": {
    "document-analyzer": {
      "command": "python",
      "args": ["/完整路径/document-analyzer/server.py"],
      "cwd": "/完整路径/document-analyzer",
      "env": {
        "LOG_LEVEL": "INFO"
      }
    }
  }
}
```

✅ **测试验证**
重启Claude Desktop后，您可以测试：
```
# 解析PDF文档
请帮我解析这个PDF文件：/documents/report.pdf

# 提取文本内容
从这段内容中提取关键文本信息

# 生成智能摘要
为这篇文档生成一个200字的摘要
```

所有代码都已经为您准备好了，这是一个完整的生产级文档分析系统！
有任何问题随时告诉我。
```

## 🎯 关键要点

### AI助手的工作方式
1. **结构化询问** - 使用AI-ASSISTANT-GUIDE.md中的模板
2. **智能推荐** - 基于templates/目录选择合适方案
3. **代码生成** - 使用模板生成完整可运行代码
4. **详细指导** - 提供配置、测试、部署的完整指导

### 提示词系统的价值
- **标准化流程** - 确保AI助手提供一致的高质量服务
- **完整代码库** - 提供可直接使用的代码模板
- **最佳实践** - 内置安全、性能、错误处理等最佳实践
- **可扩展性** - 支持不同复杂度和业务场景的需求

---

**这就是AI助手如何使用这套提示词系统来帮助用户的完整流程！** 🤖⭐

#!/usr/bin/env python3
"""
钉钉MCP工具使用演示

展示如何在Python代码中直接使用钉钉MCP工具。
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def demo_todo_management():
    """演示待办任务管理"""
    print("📋 待办任务管理演示")
    print("-" * 30)
    
    try:
        from tools.todo_tools import create_todo_task, get_todo_tasks, update_todo_task
        
        # 1. 创建待办任务
        print("1️⃣ 创建待办任务...")
        
        # 计算一周后的时间戳
        due_time = int((datetime.now() + timedelta(days=7)).timestamp() * 1000)
        
        result = await create_todo_task(
            creator_id="demo_user_001",
            subject="完成钉钉MCP集成测试",
            description="需要完成以下工作：\n1. 功能测试\n2. 性能测试\n3. 文档编写",
            due_time=due_time,
            executor_ids=["executor_001", "executor_002"],
            participant_ids=["participant_001"],
            priority=85
        )
        print(f"   结果: {result}")
        
        # 2. 查询待办任务
        print("\n2️⃣ 查询待办任务...")
        result = await get_todo_tasks(
            union_id="demo_user_001",
            is_done=False,
            role_types=[["creator"], ["executor"]]
        )
        print(f"   结果: {result}")
        
        # 3. 更新任务状态
        print("\n3️⃣ 更新任务状态...")
        result = await update_todo_task(
            union_id="demo_user_001",
            task_id="demo_task_123",
            subject="完成钉钉MCP集成测试（已更新）",
            done=False,
            priority=90
        )
        print(f"   结果: {result}")
        
    except Exception as e:
        print(f"   ❌ 演示过程中出现错误: {e}")

async def demo_work_notifications():
    """演示工作通知功能"""
    print("\n🔔 工作通知功能演示")
    print("-" * 30)
    
    try:
        from tools.notification_tools import (
            send_work_notification,
            get_notification_send_result,
            get_notification_send_progress
        )
        
        # 1. 发送工作通知
        print("1️⃣ 发送工作通知...")
        
        markdown_content = """# 项目进度更新通知

## 本周完成情况

✅ **已完成**
- 钉钉MCP服务器开发
- 基础功能测试
- 文档编写

🔄 **进行中**
- 性能优化
- 安全加固
- 部署准备

📅 **下周计划**
- 生产环境部署
- 用户培训
- 监控配置

---
*发送时间: {}*
""".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        result = await send_work_notification(
            agent_id="demo_agent_123",
            title="项目进度更新通知",
            content=markdown_content,
            to_all_user=False,
            userid_list="user001,user002,user003"
        )
        print(f"   结果: {result}")
        
        # 2. 查询发送结果
        print("\n2️⃣ 查询发送结果...")
        result = await get_notification_send_result(
            agent_id="demo_agent_123",
            task_id=12345
        )
        print(f"   结果: {result}")
        
        # 3. 查询发送进度
        print("\n3️⃣ 查询发送进度...")
        result = await get_notification_send_progress(
            agent_id="demo_agent_123",
            task_id=12345
        )
        print(f"   结果: {result}")
        
    except Exception as e:
        print(f"   ❌ 演示过程中出现错误: {e}")

async def demo_project_management():
    """演示项目管理功能"""
    print("\n🏗️ 项目管理功能演示")
    print("-" * 30)
    
    try:
        from tools.project_tools import (
            create_project,
            search_projects,
            get_project_members,
            add_project_members,
            create_project_task
        )
        
        # 1. 创建项目
        print("1️⃣ 创建项目...")
        result = await create_project(
            user_id="demo_user_001",
            name="钉钉MCP集成项目 - 演示"
        )
        print(f"   结果: {result}")
        
        # 2. 搜索项目
        print("\n2️⃣ 搜索项目...")
        result = await search_projects(
            user_id="demo_user_001",
            name="钉钉MCP",
            max_results=5
        )
        print(f"   结果: {result}")
        
        # 3. 添加项目成员
        print("\n3️⃣ 添加项目成员...")
        result = await add_project_members(
            user_id="demo_user_001",
            project_id="demo_project_123",
            user_ids=["member_001", "member_002", "member_003"]
        )
        print(f"   结果: {result}")
        
        # 4. 创建项目任务
        print("\n4️⃣ 创建项目任务...")
        result = await create_project_task(
            user_id="demo_user_001",
            project_id="demo_project_123",
            content="实现钉钉API集成功能",
            executor_id="executor_001",
            note="需要实现认证、待办、通知等API的集成",
            start_date="2025-07-30T10:00:00Z",
            due_date="2025-08-15T18:00:00Z"
        )
        print(f"   结果: {result}")
        
    except Exception as e:
        print(f"   ❌ 演示过程中出现错误: {e}")

async def demo_batch_operations():
    """演示批量操作"""
    print("\n⚡ 批量操作演示")
    print("-" * 30)
    
    try:
        from tools.todo_tools import create_todo_task
        from tools.notification_tools import send_work_notification
        from tools.project_tools import create_project
        
        print("1️⃣ 并发执行多个操作...")
        
        # 准备多个任务
        tasks = [
            create_todo_task("user1", "任务1：代码审查"),
            create_todo_task("user2", "任务2：单元测试"),
            create_todo_task("user3", "任务3：文档更新"),
            send_work_notification("agent1", "批量通知", "这是批量操作的测试通知"),
            create_project("user1", "批量创建的测试项目")
        ]
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"   ✅ 并发执行5个操作耗时: {duration:.2f}秒")
        
        # 统计结果
        success_count = 0
        error_count = 0
        
        for i, result in enumerate(results, 1):
            if isinstance(result, Exception):
                print(f"   ❌ 操作{i}: {str(result)[:50]}...")
                error_count += 1
            else:
                print(f"   ✅ 操作{i}: {str(result)[:50]}...")
                success_count += 1
        
        print(f"\n   📊 执行结果: 成功{success_count}个, 失败{error_count}个")
        
    except Exception as e:
        print(f"   ❌ 批量操作演示失败: {e}")

async def demo_error_handling():
    """演示错误处理"""
    print("\n⚠️ 错误处理演示")
    print("-" * 30)
    
    try:
        from tools.todo_tools import create_todo_task
        
        # 1. 参数验证错误
        print("1️⃣ 测试参数验证...")
        result = await create_todo_task("", "")  # 空参数
        print(f"   空参数处理: {result}")
        
        # 2. 长度限制错误
        print("\n2️⃣ 测试长度限制...")
        long_title = "x" * 2000  # 超长标题
        result = await create_todo_task("user", long_title)
        print(f"   长度限制处理: {result}")
        
        # 3. 优先级范围错误
        print("\n3️⃣ 测试优先级范围...")
        result = await create_todo_task("user", "test", priority=150)  # 超出范围
        print(f"   优先级范围处理: {result}")
        
        print("\n   ✅ 所有错误都被正确处理，返回友好的错误信息")
        
    except Exception as e:
        print(f"   ❌ 错误处理演示失败: {e}")

async def main():
    """主演示函数"""
    print("🎭 钉钉MCP工具使用演示")
    print("=" * 50)
    
    # 设置演示环境变量
    os.environ.update({
        'DINGTALK_APP_KEY': 'demo_app_key',
        'DINGTALK_APP_SECRET': 'demo_app_secret',
        'DINGTALK_CORP_ID': 'demo_corp_id',
        'JWT_SECRET_KEY': 'demo_jwt_secret_key_for_demonstration_12345678901234567890',
        'ENCRYPTION_KEY': 'demo_encryption_key_for_demonstration_12345678901234567890',
        'LOG_LEVEL': 'INFO',
        'DEBUG_MODE': 'false'
    })
    
    # 执行各项演示
    await demo_todo_management()
    await demo_work_notifications()
    await demo_project_management()
    await demo_batch_operations()
    await demo_error_handling()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 演示总结")
    print("\n✅ 功能演示完成:")
    print("  • 待办任务管理 - 创建、查询、更新")
    print("  • 工作通知发送 - 发送、查询状态、查询进度")
    print("  • 项目管理 - 创建项目、管理成员、创建任务")
    print("  • 批量操作 - 并发执行多个工具")
    print("  • 错误处理 - 参数验证、友好错误信息")
    
    print("\n💡 注意事项:")
    print("  • 演示使用测试凭证，API调用会返回认证错误")
    print("  • 在生产环境中配置真实凭证后，所有功能将正常工作")
    print("  • 所有工具都支持异步调用和并发执行")
    print("  • 错误处理机制确保系统稳定性")
    
    print("\n🚀 开始使用:")
    print("  1. 配置钉钉应用凭证到 .env 文件")
    print("  2. 启动MCP服务器: python3 -m src.server")
    print("  3. 在Claude Desktop中使用钉钉功能")
    
    print("\n📚 更多信息:")
    print("  • 使用指南: USAGE_GUIDE.md")
    print("  • API文档: docs/API.md")
    print("  • 部署指南: docs/DEPLOYMENT.md")

if __name__ == "__main__":
    # 兼容Python 3.6及以下版本
    if hasattr(asyncio, 'run'):
        asyncio.run(main())
    else:
        loop = asyncio.get_event_loop()
        try:
            loop.run_until_complete(main())
        finally:
            loop.close()

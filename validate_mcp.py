#!/usr/bin/env python3
"""
MCP服务器验证脚本

用于验证钉钉MCP服务器的基本功能。
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def validate_mcp_server():
    """验证MCP服务器"""
    print("🔍 开始验证钉钉MCP服务器...")
    
    try:
        # 设置测试环境变量
        os.environ.update({
            'DINGTALK_APP_KEY': 'test_app_key',
            'DINGTALK_APP_SECRET': 'test_app_secret',
            'DINGTALK_CORP_ID': 'test_corp_id',
            'JWT_SECRET_KEY': 'test_jwt_secret_key_for_validation',
            'ENCRYPTION_KEY': 'test_encryption_key_for_validation',
            'LOG_LEVEL': 'INFO',
            'DEBUG_MODE': 'false'
        })
        
        # 导入配置
        from config import DingTalkConfig
        print("✅ 配置模块导入成功")
        
        # 验证配置
        config = DingTalkConfig()
        print(f"✅ 配置验证成功: {config.app_name} v{config.version}")
        
        # 导入工具模块
        from tools import todo_tools, notification_tools, project_tools
        print("✅ 工具模块导入成功")
        
        # 验证工具函数
        tools_to_check = [
            # 待办任务工具
            ('todo_tools.create_todo_task', todo_tools.create_todo_task),
            ('todo_tools.get_todo_tasks', todo_tools.get_todo_tasks),
            ('todo_tools.update_todo_task', todo_tools.update_todo_task),
            ('todo_tools.delete_todo_task', todo_tools.delete_todo_task),
            
            # 通知工具
            ('notification_tools.send_work_notification', notification_tools.send_work_notification),
            ('notification_tools.get_notification_send_result', notification_tools.get_notification_send_result),
            ('notification_tools.get_notification_send_progress', notification_tools.get_notification_send_progress),
            ('notification_tools.recall_work_notification', notification_tools.recall_work_notification),
            
            # 项目管理工具
            ('project_tools.create_project', project_tools.create_project),
            ('project_tools.search_projects', project_tools.search_projects),
            ('project_tools.get_project_members', project_tools.get_project_members),
            ('project_tools.add_project_members', project_tools.add_project_members),
            ('project_tools.create_project_task', project_tools.create_project_task),
        ]
        
        for tool_name, tool_func in tools_to_check:
            if callable(tool_func):
                print(f"✅ 工具函数验证成功: {tool_name}")
            else:
                print(f"❌ 工具函数验证失败: {tool_name} 不是可调用函数")
                return False
        
        # 导入服务器模块
        try:
            import server
            print("✅ 服务器模块导入成功")
        except Exception as e:
            print(f"❌ 服务器模块导入失败: {e}")
            return False
        
        # 验证MCP工具装饰器
        if hasattr(server, 'mcp'):
            print("✅ FastMCP实例创建成功")
        else:
            print("❌ FastMCP实例未找到")
            return False
        
        print("\n🎉 钉钉MCP服务器验证完成！")
        print("\n📋 验证结果总结:")
        print("  ✅ 配置管理模块正常")
        print("  ✅ 待办任务工具正常")
        print("  ✅ 工作通知工具正常") 
        print("  ✅ 项目管理工具正常")
        print("  ✅ MCP服务器模块正常")
        print("\n🚀 服务器已准备就绪，可以与Claude Desktop集成！")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入错误: {e}")
        print("💡 请确保已安装所有依赖: pip install -e .")
        return False
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False

def validate_project_structure():
    """验证项目结构"""
    print("\n📁 验证项目结构...")
    
    required_files = [
        "src/__init__.py",
        "src/server.py",
        "src/config.py",
        "src/tools/__init__.py",
        "src/tools/todo_tools.py",
        "src/tools/notification_tools.py", 
        "src/tools/project_tools.py",
        "src/utils/__init__.py",
        "src/utils/auth.py",
        "src/utils/dingtalk_client.py",
        "pyproject.toml",
        ".env.example",
        "README.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 项目结构验证完成")
    return True

async def main():
    """主函数"""
    print("🔧 钉钉MCP服务器验证工具")
    print("=" * 50)
    
    # 验证项目结构
    if not validate_project_structure():
        sys.exit(1)
    
    # 验证MCP服务器
    if not await validate_mcp_server():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎯 下一步操作建议:")
    print("1. 配置钉钉应用凭证到 .env 文件")
    print("2. 运行测试: python run_tests.py --all")
    print("3. 启动服务器: python -m src.server")
    print("4. 配置Claude Desktop集成")

if __name__ == "__main__":
    # 兼容Python 3.6及以下版本
    if hasattr(asyncio, 'run'):
        asyncio.run(main())
    else:
        loop = asyncio.get_event_loop()
        try:
            loop.run_until_complete(main())
        finally:
            loop.close()
